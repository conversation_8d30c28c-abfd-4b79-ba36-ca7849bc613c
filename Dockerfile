# Use the official Golang image to create a build artifact.
# This is known as a multi-stage build.
# https://docs.docker.com/build/building/multi-stage/
FROM golang:1.24.5-alpine AS builder

# Set the Current Working Directory inside the container
WORKDIR /app

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod tidy

# Copy the source code
COPY . .

# ARG instructions are not allowed before a FROM instruction, but we can use them here
# These values can be overwritten at build time
ARG VERSION=dev
ARG COMMIT=unknown
ARG DATE=unknown

# Build the Go app
# CGO_ENABLED=0 is required for a static build
# -ldflags="-w -s" reduces the size of the binary
# The -X flag sets the value of a string variable in the target package
RUN CGO_ENABLED=0 GOOS=linux go build -a -ldflags="-w -s -X 'github.com/ajiwo/resumatter/internal/cli.version=${VERSION}' -X 'github.com/ajiwo/resumatter/internal/cli.commit=${COMMIT}' -X 'github.com/ajiwo/resumatter/internal/cli.date=${DATE}'" -o /resumatter cmd/resumatter/main.go

# Use a minimal image for the final stage
FROM alpine:3.20

# Add a non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set the Current Working Directory inside the container
WORKDIR /app

# Add certificates and timezone data
RUN apk add --no-cache ca-certificates tzdata

# Copy the Pre-built binary file from the previous stage
COPY --from=builder /resumatter .

# Switch to the non-root user
USER appuser

# Expose port 8780 to the outside world
EXPOSE 8780

# Command to run the executable
ENTRYPOINT ["/app/resumatter"]
CMD ["serve"]

