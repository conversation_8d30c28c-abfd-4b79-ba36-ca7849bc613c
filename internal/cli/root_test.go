package cli

import (
	"bytes"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_loadConfig(t *testing.T) {
	t.Run("loads config from file", func(t *testing.T) {
		cp := getConfigPath()
		cfg, err := loadConfig(cp)
		require.NoError(t, err)
		assert.Equal(t, "8380", cfg.Server.Port)
	})

	t.Run("returns error for non-existent file", func(t *testing.T) {
		_, err := loadConfig("non-existent-config.yaml")
		assert.Error(t, err)
	})
}

func TestExecute(t *testing.T) {
	// This is a basic test to ensure the command executes without crashing.
	// More specific command logic is tested in other files.
	// We can redirect output to a buffer to check for specific output if needed.
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	os.Args = []string{"resumatter", "--help"}
	Execute()
}

func Test_RootCmd(t *testing.T) {
	var out bytes.Buffer
	rootCmd.SetOut(&out)
	rootCmd.SetErr(&out)

	t.Run("shows help", func(t *testing.T) {
		out.Reset()
		rootCmd.SetArgs([]string{"--help"})
		err := rootCmd.Execute()
		assert.NoError(t, err)
		assert.Contains(t, out.String(), "AI-powered resume tailoring tool")
	})
}
