package cli

import (
	"fmt"
	"os"

	"github.com/ajiwo/resumatter/config"
	"github.com/spf13/cobra"
)

var (
	configFile string
	verbose    bool
)

// loadConfig loads the configuration from file or environment variables
func loadConfig(configFile string) (*config.Config, error) {
	var cfg *config.Config
	var err error

	if configFile != "" && configFile != "config.yaml" {
		// User specified a config file, try to load it
		cfg, err = config.NewFromFile(configFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load configuration from %s: %w", configFile, err)
		}
	} else {
		// Try to load from default config file, but fall back to environment variables
		cfg, err = config.New(config.Options{
			ConfigName:     "config",
			ConfigType:     "yaml",
			ApplyDefaults:  true,
			SkipValidation: false,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to load configuration: %w", err)
		}
	}

	return cfg, nil
}

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "resumatter",
	Short: "AI-powered resume tailoring tool",
	Long: `Resumatter is an AI-powered resume tailoring tool designed to optimize your resume for any job description.

It leverages Generative AI to analyze your resume and a target job posting, then rewrites your resume to maximize its impact and improve its Applicant Tracking System (ATS) score.`,
}

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "config.yaml", "config file path")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")

	// Add subcommands
	rootCmd.AddCommand(validateCmd)
	rootCmd.AddCommand(showCmd)
	rootCmd.AddCommand(listPresetsCmd)
	rootCmd.AddCommand(showPresetCmd)
	rootCmd.AddCommand(serveCmd)
	rootCmd.AddCommand(versionCmd)
}
