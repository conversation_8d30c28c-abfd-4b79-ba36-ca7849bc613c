package cli

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

// listPresetsCmd represents the list-presets command
var listPresetsCmd = &cobra.Command{
	Use:   "list-presets",
	Short: "List all available AI presets",
	Long: `List all available AI presets with their basic information.
This shows the preset names, providers, models, and key settings.`,
	Run: func(cmd *cobra.Command, args []string) {
		if err := runListPresets(cmd.OutOrStdout(), configFile, verbose); err != nil {
			fmt.Fprintf(os.Stderr, "%v\n", err)
			os.Exit(1)
		}
	},
}
