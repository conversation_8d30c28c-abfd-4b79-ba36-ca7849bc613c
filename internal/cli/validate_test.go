package cli

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_runValidate(t *testing.T) {
	t.Run("valid config", func(t *testing.T) {
		var out bytes.Buffer
		cp := getConfigPath()

		err := runValidate(&out, cp, false)
		require.NoError(t, err)
		assert.Contains(t, out.String(), "SUCCESS: Configuration is valid!")
	})

	t.Run("invalid config", func(t *testing.T) {
		var out bytes.Buffer
		err := runValidate(&out, "non-existent-config.yaml", false)
		assert.Error(t, err)
	})
}
