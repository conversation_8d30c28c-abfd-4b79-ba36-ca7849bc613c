package cli

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

// validateCmd represents the validate command
var validateCmd = &cobra.Command{
	Use:   "validate",
	Short: "Validate the configuration file",
	Long: `Validate the configuration file for syntax and semantic correctness.
This command will:
- Parse the YAML file
- Validate all configuration sections
- Report any errors found
- Exit with code 0 if valid, 1 if invalid`,
	Run: func(cmd *cobra.Command, args []string) {
		if err := runValidate(cmd.OutOrStdout(), configFile, verbose); err != nil {
			fmt.Fprintf(os.Stderr, "%v\n", err)
			os.Exit(1)
		}
	},
}
