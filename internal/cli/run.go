package cli

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"maps"
	"os"
	"reflect"
	"strings"
	"time"

	"github.com/ajiwo/resumatter/ai"
	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/internal/server"
	"github.com/ajiwo/resumatter/logger"
	"github.com/ajiwo/resumatter/telemetry"
	"github.com/ajiwo/resumatter/util"
	"github.com/fsnotify/fsnotify"
	"github.com/gin-gonic/gin"
)

func runValidate(cmdOut io.Writer, configFile string, verbose bool) error {
	cfg, err := loadConfig(configFile)
	if err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	fmt.Fprintln(cmdOut, "SUCCESS: Configuration is valid!")
	if verbose {
		fmt.Fprintf(cmdOut, "- Found %d AI presets\n", len(cfg.AI.Presets))
		fmt.Fprintf(cmdOut, "- Default preset: %s\n", cfg.AI.DefaultPreset)
		fmt.Fprintf(cmdOut, "- Found %d operations\n", len(cfg.AI.Operations))
		fmt.Fprintf(cmdOut, "- Server port: %s\n", cfg.Server.Port)
		fmt.Fprintf(cmdOut, "- Environment: %s\n", cfg.Server.Environment)
		fmt.Fprintf(cmdOut, "- TLS mode: %s\n", cfg.TLS.Mode)
		fmt.Fprintf(cmdOut, "- Auth enabled: %t\n", cfg.Auth.Enabled)
		fmt.Fprintf(cmdOut, "- Rate limiting enabled: %t\n", cfg.RateLimit.Enabled)
		fmt.Fprintf(cmdOut, "- Log level: %s\n", cfg.Logging.Level)
	}
	return nil
}

func runShow(cmdOut io.Writer, configFile string, verbose bool) error {
	cfg, err := loadConfig(configFile)
	if err != nil {
		return fmt.Errorf("error loading configuration: %w", err)
	}

	jsonData, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal configuration: %w", err)
	}

	fmt.Fprintln(cmdOut, string(jsonData))
	return nil
}

func runListPresets(cmdOut io.Writer, configFile string, verbose bool) error {
	cfg, err := loadConfig(configFile)
	if err != nil {
		return fmt.Errorf("error loading configuration: %w", err)
	}

	fmt.Fprintf(cmdOut, "Available AI Presets (Default: %s):\n", cfg.AI.DefaultPreset)

	for name, preset := range cfg.AI.Presets {
		marker := "  "
		if name == cfg.AI.DefaultPreset {
			marker = "* "
		}

		fmt.Fprintf(cmdOut, "%s%s:\n", marker, name)
		fmt.Fprintf(cmdOut, "    Provider: %s\n", preset.Provider)
		fmt.Fprintf(cmdOut, "    Model: %s\n", preset.Model)
		fmt.Fprintf(cmdOut, "    Temperature: %.2f\n", preset.Temperature)
		fmt.Fprintf(cmdOut, "    Max Tokens: %d\n", preset.MaxTokens)
		fmt.Fprintf(cmdOut, "    Timeout: %s\n", preset.Timeout)
		fmt.Fprintf(cmdOut, "    Circuit Breaker: %t\n", preset.CircuitBreaker.Enabled)
		fmt.Fprintln(cmdOut)
	}

	if len(cfg.AI.Operations) > 0 {
		fmt.Fprintln(cmdOut, "Operations using presets:")
		for opName, op := range cfg.AI.Operations {
			fmt.Fprintf(cmdOut, "  %s -> %s", opName, op.Preset)
			if op.Temperature != nil {
				fmt.Fprintf(cmdOut, " (temp: %.2f)", *op.Temperature)
			}
			if op.MaxTokens != nil {
				fmt.Fprintf(cmdOut, " (tokens: %d)", *op.MaxTokens)
			}
			if op.Timeout != "" {
				fmt.Fprintf(cmdOut, " (timeout: %s)", op.Timeout)
			}
			fmt.Fprintln(cmdOut)
		}
	}
	return nil
}

func runShowPreset(cmdOut io.Writer, configFile, presetName string, verbose bool) error {
	cfg, err := loadConfig(configFile)
	if err != nil {
		return fmt.Errorf("error loading configuration: %w", err)
	}

	preset, exists := cfg.AI.Presets[presetName]
	if !exists {
		var availablePresets []string
		for name := range cfg.AI.Presets {
			availablePresets = append(availablePresets, name)
		}
		return fmt.Errorf("preset '%s' not found. Available presets: %s", presetName, strings.Join(availablePresets, " "))
	}

	fmt.Fprintf(cmdOut, "Preset: %s", presetName)
	if presetName == cfg.AI.DefaultPreset {
		fmt.Fprint(cmdOut, " (DEFAULT)")
	}
	fmt.Fprintln(cmdOut)
	fmt.Fprintln(cmdOut, strings.Repeat("=", 50))

	fmt.Fprintf(cmdOut, "Provider: %s\n", preset.Provider)
	fmt.Fprintf(cmdOut, "Model: %s\n", preset.Model)
	fmt.Fprintf(cmdOut, "Temperature: %.2f\n", preset.Temperature)
	fmt.Fprintf(cmdOut, "Max Tokens: %d\n", preset.MaxTokens)
	fmt.Fprintf(cmdOut, "Timeout: %s\n", preset.Timeout)
	if apiKey, ok := preset.ProviderConfig["api_key"].(string); ok {
		fmt.Fprintf(cmdOut, "API Key: %s\n", util.MaskAPIKey(apiKey))
	} else {
		fmt.Fprintln(cmdOut, "API Key: (not specified in provider_config)")
	}

	fmt.Fprintln(cmdOut, "Circuit Breaker:")
	fmt.Fprintf(cmdOut, "  Enabled: %t\n", preset.CircuitBreaker.Enabled)
	if preset.CircuitBreaker.Enabled {
		fmt.Fprintf(cmdOut, "  Failure Threshold: %.2f\n", preset.CircuitBreaker.FailureThreshold)
		fmt.Fprintf(cmdOut, "  Min Requests: %d\n", preset.CircuitBreaker.MinRequests)
		fmt.Fprintf(cmdOut, "  Max Requests: %d\n", preset.CircuitBreaker.MaxRequests)
		fmt.Fprintf(cmdOut, "  Interval: %s\n", preset.CircuitBreaker.Interval)
		fmt.Fprintf(cmdOut, "  Timeout: %s\n", preset.CircuitBreaker.Timeout)
	}

	if len(preset.ProviderConfig) > 0 {
		fmt.Fprintln(cmdOut, "Provider Configuration:")
		sanitizedConfig := make(map[string]any)
		maps.Copy(sanitizedConfig, preset.ProviderConfig)

		if apiKey, ok := sanitizedConfig["api_key"].(string); ok {
			sanitizedConfig["api_key"] = util.MaskAPIKey(apiKey)
		}

		configJSON, _ := json.MarshalIndent(sanitizedConfig, "  ", "  ")
		fmt.Fprintf(cmdOut, "  %s", string(configJSON))
	}

	printOperationPreset(cmdOut, cfg, presetName)

	return nil
}

func printOperationPreset(cmdOut io.Writer, cfg *config.Config, presetName string) {
	var usingOps []string
	for opName, op := range cfg.AI.Operations {
		if op.Preset == presetName {
			usingOps = append(usingOps, opName)
		}
	}

	if len(usingOps) > 0 {
		fmt.Fprintln(cmdOut, "Used by operations:")
		for _, opName := range usingOps {
			op := cfg.AI.Operations[opName]
			fmt.Fprintf(cmdOut, "  %s", opName)
			if op.Temperature != nil {
				fmt.Fprintf(cmdOut, " (temp override: %.2f)", *op.Temperature)
			}
			if op.MaxTokens != nil {
				fmt.Fprintf(cmdOut, " (tokens override: %d)", *op.MaxTokens)
			}
			if op.Timeout != "" {
				fmt.Fprintf(cmdOut, " (timeout override: %s)", op.Timeout)
			}
			fmt.Fprintln(cmdOut)
		}
	}
}

func runServe(cmdOut io.Writer, configFile string, verbose bool) error {
	// 1. Load Config
	cfg, err := loadConfig(configFile)
	if err != nil {
		return fmt.Errorf("error loading configuration: %w", err)
	}

	// 2. Initialize Dependencies
	appLogger := logger.New(&logger.Config{
		Level:  logger.ParseLevel(cfg.Logging.Level),
		Format: logger.ParseFormat(cfg.Logging.Format),
		Output: os.Stderr,
	}).Named("server")

	ctx := context.Background()

	// Initialize Telemetry
	tel, err := initializeTelemetry(ctx, cfg, appLogger, cmdOut, verbose)
	if err != nil {
		return err
	}
	if tel != nil {
		defer shutdownTelemetry(tel, appLogger)
	}

	printVerboseConfig(cmdOut, cfg, verbose)

	// Initialize Rate Limiter
	var rateLimiter *server.RateLimiter
	if cfg.RateLimit.Enabled {
		rateLimiter, err = server.NewRateLimiter(cfg)
		if err != nil {
			if cfg.RateLimit.Required {
				return fmt.Errorf("failed to create required rate limiter: %w", err)
			}
			appLogger.Warn(ctx, "failed to create rate limiter, rate limiting will be disabled", logger.Err(err))
		}
	}

	// Initialize AI Clients
	aiClients := make(map[string]ai.Client)
	for _, opName := range ai.ListRegisteredOperations() {
		client, err := ai.NewClient(ctx, cfg, opName)
		if err != nil {
			return fmt.Errorf("failed to create AI client for operation '%s': %w", opName, err)
		}
		aiClients[opName] = client
	}

	// 3. Assemble Dependencies
	deps := &server.Dependencies{
		Config:      cfg,
		Logger:      appLogger,
		RateLimiter: rateLimiter,
		AIClients:   aiClients,
	}

	// 4. Create Gin Engine and Register Routes
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	router := gin.New()
	server.RegisterRoutes(router, deps)

	// 5. Create the Server
	srv, err := server.New(cfg, router, deps)
	if err != nil {
		return fmt.Errorf("failed to create server: %w", err)
	}

	// 6. Register config change callbacks
	err = registerConfigChangeCallback(cfg, srv, appLogger, ctx, &tel)
	if err != nil {
		// Log the error but don't prevent the server from starting
		appLogger.Error(ctx, "Failed to register config change callback", logger.Err(err))
	}

	fmt.Fprintln(cmdOut, "Starting server...")
	if err := srv.Run(); err != nil {
		return fmt.Errorf("server error: %w", err)
	}

	return nil
}

func initializeTelemetry(ctx context.Context, cfg *config.Config, appLogger logger.Logger, cmdOut io.Writer, verbose bool) (*telemetry.Telemetry, error) {
	if !cfg.Observability.TracingEnabled && !cfg.Observability.MetricsEnabled {
		return nil, nil
	}

	if verbose {
		fmt.Fprintln(cmdOut, "Initializing observability stack...")
	}

	tel, err := telemetry.Initialize(ctx, telemetry.Config{
		Observability: cfg.Observability,
		Logger:        appLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize telemetry: %w", err)
	}

	telemetry.SetGlobal(tel)

	if verbose {
		fmt.Fprintln(cmdOut, "Observability stack initialized successfully")
	}

	return tel, nil
}

func shutdownTelemetry(tel *telemetry.Telemetry, appLogger logger.Logger) {
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if shutdownErr := tel.Shutdown(shutdownCtx); shutdownErr != nil {
		appLogger.ErrorWithErr(shutdownCtx, "Failed to shutdown telemetry", shutdownErr)
	}
}

func printVerboseConfig(cmdOut io.Writer, cfg *config.Config, verbose bool) {
	if !verbose {
		return
	}

	fmt.Fprintln(cmdOut, "Configuration loaded successfully")
	fmt.Fprintf(cmdOut, "- Server port: %s", cfg.Server.Port)
	fmt.Fprintf(cmdOut, "- Environment: %s", cfg.Server.Environment)
	fmt.Fprintf(cmdOut, "- TLS mode: %s", cfg.TLS.Mode)
	fmt.Fprintf(cmdOut, "- Auth enabled: %t", cfg.Auth.Enabled)
	fmt.Fprintf(cmdOut, "- CORS enabled: %t", cfg.Server.CORS.Enabled)
	fmt.Fprintf(cmdOut, "- Rate limiting enabled: %t", cfg.RateLimit.Enabled)
	fmt.Fprintf(cmdOut, "- Tracing enabled: %t", cfg.Observability.TracingEnabled)
	fmt.Fprintf(cmdOut, "- Metrics enabled: %t", cfg.Observability.MetricsEnabled)
}

func registerConfigChangeCallback(cfg *config.Config, srv *server.Server, appLogger logger.Logger, ctx context.Context, tel **telemetry.Telemetry) error {
	currentObservabilityConfig := cfg.Observability

	return cfg.RegisterConfigChangeCallback("server-reloader", func(event fsnotify.Event, oldCfg, newCfg *config.Config) {
		if event.Op&fsnotify.Write != fsnotify.Write {
			return
		}

		appLogger.Info(ctx, fmt.Sprintf("Configuration file changed (%s), reloading applicable settings.", event.Name))
		srv.ReloadTLSConfig(event, oldCfg, newCfg)
		srv.ReloadRateLimit(event, oldCfg, newCfg)

		if !reflect.DeepEqual(currentObservabilityConfig, newCfg.Observability) {
			handleObservabilityConfigChange(ctx, appLogger, newCfg, tel, &currentObservabilityConfig)
		}
	})
}

func handleObservabilityConfigChange(ctx context.Context, appLogger logger.Logger, newCfg *config.Config, tel **telemetry.Telemetry, currentConfig *config.ObservabilityConfig) {
	appLogger.Info(ctx, "Observability configuration changed, re-initializing telemetry.")

	shutdownCurrentTelemetry(ctx, appLogger, tel)

	if newCfg.Observability.TracingEnabled || newCfg.Observability.MetricsEnabled {
		initializeNewTelemetry(ctx, appLogger, newCfg, tel)
	} else {
		*tel = nil
		appLogger.Info(ctx, "Telemetry disabled.")
	}

	telemetry.SetGlobal(*tel)
	*currentConfig = newCfg.Observability
}

func shutdownCurrentTelemetry(ctx context.Context, appLogger logger.Logger, tel **telemetry.Telemetry) {
	if *tel == nil {
		return
	}

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if shutdownErr := (*tel).Shutdown(shutdownCtx); shutdownErr != nil {
		appLogger.ErrorWithErr(ctx, "Failed to shutdown old telemetry instance", shutdownErr)
	}
}

func initializeNewTelemetry(ctx context.Context, appLogger logger.Logger, newCfg *config.Config, tel **telemetry.Telemetry) {
	newTel, initErr := telemetry.Initialize(ctx, telemetry.Config{
		Observability: newCfg.Observability,
		Logger:        appLogger,
	})
	if initErr != nil {
		appLogger.ErrorWithErr(ctx, "Failed to re-initialize telemetry", initErr)
		*tel = nil
	} else {
		appLogger.Info(ctx, "Telemetry re-initialized successfully.")
		*tel = newTel
	}
}

func runVersion(cmdOut io.Writer, version, date, commit string) {
	fmt.Fprintf(cmdOut, "Version: %s\n", version)
	fmt.Fprintf(cmdOut, "Build Date: %s\n", date)
	fmt.Fprintf(cmdOut, "Git Commit: %s\n", commit)
}
