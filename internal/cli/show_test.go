package cli

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_runShow(t *testing.T) {
	t.Run("shows config", func(t *testing.T) {
		var out bytes.Buffer
		cp := getConfigPath()

		err := runShow(&out, cp, false)
		require.NoError(t, err)
		assert.Contains(t, out.String(), `"Port": "8380"`)
	})

	t.Run("invalid config", func(t *testing.T) {
		var out bytes.Buffer
		err := runShow(&out, "non-existent-config.yaml", false)
		assert.Error(t, err)
	})
}
