package cli

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

// showCmd represents the show command
var showCmd = &cobra.Command{
	Use:   "show",
	Short: "Show the parsed configuration",
	Long: `Show the complete parsed configuration in JSON format.
This is useful for debugging and understanding how the configuration
is being interpreted by the application.`,
	Run: func(cmd *cobra.Command, args []string) {
		if err := runShow(cmd.OutOrStdout(), configFile, verbose); err != nil {
			fmt.Fprintf(os.Stderr, "%v\n", err)
			os.Exit(1)
		}
	},
}
