package cli

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

// serveCmd represents the serve command
var serveCmd = &cobra.Command{
	Use:   "serve",
	Short: "Start the HTTP server",
	Long: `Start the HTTP server with the configured settings.
This command will:
- Load the configuration file
- Start the HTTP server on the configured port
- Enable TLS if configured
- Apply middleware (CORS, auth, rate limiting) based on configuration
- Gracefully handle shutdown signals`,
	Run: func(cmd *cobra.Command, args []string) {
		if err := runServe(cmd.OutOrStdout(), configFile, verbose); err != nil {
			fmt.Fprintf(os.Stderr, "%v\n", err)
			os.Exit(1)
		}
	},
}
