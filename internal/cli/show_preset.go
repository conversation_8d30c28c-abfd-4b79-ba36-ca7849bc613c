package cli

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

// showPresetCmd represents the show-preset command
var showPresetCmd = &cobra.Command{
	Use:   "show-preset [preset-name]",
	Short: "Show detailed information about a specific preset",
	Long: `Show detailed information about a specific AI preset including
all configuration options and provider-specific settings.`,
	Args: cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		if err := runShowPreset(cmd.OutOrStdout(), configFile, args[0], verbose); err != nil {
			fmt.Fprintf(os.Stderr, "%v\n", err)
			os.Exit(1)
		}
	},
}
