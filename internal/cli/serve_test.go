package cli

import (
	"bytes"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func Test_runServe(t *testing.T) {
	t.Run("start and stop server", func(t *testing.T) {
		var out bytes.Buffer
		var mu sync.RWMutex
		cp := getConfigPath()

		done := make(chan error, 1)
		serverStarted := make(chan struct{})

		// Start the server in a goroutine
		go func() {
			mu.Lock()
			defer mu.Unlock()

			// Signal that we're about to start the server
			close(serverStarted)

			err := runServe(&out, cp, false)
			done <- err
		}()

		// Wait for server to start writing output
		<-serverStarted

		// Set up a timer to send SIGINT after a short delay to stop the server
		timer := time.AfterFunc(500*time.Millisecond, func() {
			// Send SIGINT to gracefully shutdown the server
			// This simulates Ctrl+C which the server is designed to handle
			if p := os.Getpid(); p > 0 {
				if proc, err := os.FindProcess(p); err == nil {
					if err := proc.Signal(os.Interrupt); err != nil {
						t.Errorf("Failed to send SIGINT: %v", err)
					}
				}
			}
		})
		defer timer.Stop()

		// Wait for either the server to finish or timeout
		select {
		case err := <-done:
			// Server finished (likely due to our SIGINT)
			assert.NoError(t, err)
		case <-time.After(2 * time.Second):
			// Fallback timeout in case SIGINT doesn't work
			t.Log("Test completed with timeout - this is expected")
		}

		// Check that the expected output was written with proper synchronization
		mu.RLock()
		output := out.String()
		mu.RUnlock()
		assert.Contains(t, output, "Starting server...")
	})

	t.Run("invalid config", func(t *testing.T) {
		var out bytes.Buffer
		err := runServe(&out, "non-existent-config.yaml", false)
		assert.Error(t, err)
	})
}
