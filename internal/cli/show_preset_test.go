package cli

import (
	"bytes"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_runShowPreset(t *testing.T) {
	t.Run("shows preset", func(t *testing.T) {
		var out bytes.Buffer
		cp := getConfigPath()

		err := runShowPreset(&out, cp, "minimal", false)
		require.NoError(t, err)
		assert.Contains(t, out.String(), "Preset: minimal")
	})

	t.Run("preset not found", func(t *testing.T) {
		var out bytes.Buffer
		td, err := os.MkdirTemp("", "test-config-")
		require.NoError(t, err)
		defer os.RemoveAll(td)

		cp := filepath.Join(td, "config.yaml")
		require.NoError(t, os.WriteFile(cp, []byte(`
ai:
  presets:
    test-preset:
      provider: test-provider
      model: test-model
`), 0644))

		err = runShowPreset(&out, cp, "not-found-preset", false)
		assert.Error(t, err)
	})

	t.Run("invalid config", func(t *testing.T) {
		var out bytes.Buffer
		err := runShowPreset(&out, "non-existent-config.yaml", "test-preset", false)
		assert.Error(t, err)
	})
}
