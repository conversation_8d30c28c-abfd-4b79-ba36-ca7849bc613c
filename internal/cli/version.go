package cli

import (
	"github.com/spf13/cobra"
)

var (
	// version will be set by linker flags during build
	version = "dev"
	// date will be set by linker flags during build
	date = "unknown"
	// commit will be set by linker flags during build
	commit = "unknown"
)

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Show version information",
	Long:  `Display version, build date, and git commit information.`,
	Run: func(cmd *cobra.Command, args []string) {
		runVersion(cmd.OutOrStdout(), version, date, commit)
	},
}
