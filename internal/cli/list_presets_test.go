package cli

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_runListPresets(t *testing.T) {
	t.Run("lists presets", func(t *testing.T) {
		var out bytes.Buffer
		cp := getConfigPath()

		err := runListPresets(&out, cp, false)
		require.NoError(t, err)
		assert.Contains(t, out.String(), "Available AI Presets (Default: minimal):")
		assert.Contains(t, out.String(), "* minimal:")
	})

	t.Run("invalid config", func(t *testing.T) {
		var out bytes.Buffer
		err := runListPresets(&out, "non-existent-config.yaml", false)
		assert.Error(t, err)
	})
}
