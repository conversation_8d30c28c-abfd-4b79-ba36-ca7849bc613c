package server

import (
	"github.com/ajiwo/resumatter/telemetry"
	"github.com/gin-gonic/gin"
)

// RegisterRoutes sets up all the application routes.
func RegisterRoutes(router *gin.Engine, deps *Dependencies) {
	// Add telemetry middleware first
	router.Use(telemetry.HTTPMiddleware())
	router.Use(telemetry.RecoveryMiddleware())

	// Health check endpoint (outside of middleware group)
	router.GET("/health", HealthHandler)

	// API v1 routes
	v1 := router.Group("/api/v1")
	setupMiddleware(v1, deps) // Pass dependencies to middleware setup
	{
		aiGroup := v1.Group("/ai")
		{
			// Create and register a handler for each AI operation
			tailorHandler := NewAIOperationHandler("tailor", deps.Config, deps.AIClients["tailor"], deps.Logger)
			aiGroup.POST("/tailor", tailorHandler.Handle)

			evaluateHandler := NewAIOperationHandler("evaluate", deps.Config, deps.AIClients["evaluate"], deps.Logger)
			aiGroup.POST("/evaluate", evaluateHandler.Handle)

			analyzeHandler := NewAIOperationHandler("analyze", deps.Config, deps.AIClients["analyze"], deps.Logger)
			aiGroup.POST("/analyze", analyzeHandler.Handle)

			gitCommitHandler := NewAIOperationHandler("git-commit", deps.Config, deps.AIClients["git-commit"], deps.Logger)
			aiGroup.POST("/git-commit", gitCommitHandler.Handle)
		}
	}
}

// setupMiddleware configures middleware based on configuration.
func setupMiddleware(router *gin.RouterGroup, deps *Dependencies) {
	router.Use(CORSMiddleware(deps.Config))
	router.Use(AuthMiddleware(deps.Config))
	router.Use(rateLimitMiddleware(deps)) // Use the wrapper for reload-safe rate limiting
}

// rateLimitMiddleware returns a Gin middleware that safely handles rate limiting,
// even when the rate limiter is reloaded.
func rateLimitMiddleware(deps *Dependencies) gin.HandlerFunc {
	return func(c *gin.Context) {
		deps.RateLimitMutex.RLock()
		limiter := deps.RateLimiter
		deps.RateLimitMutex.RUnlock()

		if limiter == nil {
			c.Next()
			return
		}
		limiter.Middleware()(c)
	}
}
