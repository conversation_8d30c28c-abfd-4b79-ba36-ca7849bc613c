package server

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/ajiwo/resumatter/ai"
	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/ajiwo/resumatter/ratelimit/backends"
	"github.com/ajiwo/resumatter/ratelimit/strategies"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestRegisterRoutes(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	deps := &Dependencies{
		Config: &config.Config{
			Server: config.ServerConfig{
				Environment: "production",
			},
		},
		Logger:      logger.NewDefault(),
		RateLimiter: nil,
		AIClients:   make(map[string]ai.Client),
	}

	RegisterRoutes(router, deps)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/health", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	t.Run("test nil ratelimit middleware", func(t *testing.T) {
		router := gin.New()
		deps := &Dependencies{
			RateLimiter: nil,
		}
		router.Use(rateLimitMiddleware(deps))
		router.GET("/", func(c *gin.Context) { c.Status(http.StatusOK) })

		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("test ratelimit middleware", func(t *testing.T) {
		mockBe := &mockBackend{}
		mockSt := &mockStrategy{
			allowFunc: func(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyResult, error) {
				return &strategies.StrategyResult{Allowed: true, Remaining: 0}, nil
			},
		}
		rl := &RateLimiter{
			backend:  mockBe,
			strategy: mockSt,
			config: &config.Config{
				RateLimit: config.RateLimitConfig{
					Enabled: true,
				},
			},
			logger: &mockLogger{},
		}
		deps := &Dependencies{
			RateLimiter: rl,
		}

		router := gin.New()
		router.Use(rateLimitMiddleware(deps))
		router.GET("/", func(c *gin.Context) { c.Status(http.StatusOK) })

		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})
}
