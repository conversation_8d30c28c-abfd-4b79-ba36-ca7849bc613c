package server

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/ajiwo/resumatter/ai"
	"github.com/ajiwo/resumatter/logger"
	"github.com/ajiwo/resumatter/ratelimit/backends"
	"github.com/ajiwo/resumatter/ratelimit/strategies"
)

var _ logger.Logger = (*mockLogger)(nil)

// mockLogger is a thread-safe mock for the logger.Logger interface.
type mockLogger struct {
	mu    sync.Mutex
	infos []string
	errs  []string
	level logger.Level
}

func (m *mockLogger) Debug(ctx context.Context, msg string, fields ...logger.Field) {}
func (m *mockLogger) Info(ctx context.Context, msg string, fields ...logger.Field) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.infos = append(m.infos, msg)
}
func (m *mockLogger) Warn(ctx context.Context, msg string, fields ...logger.Field) {}
func (m *mockLogger) Error(ctx context.Context, msg string, fields ...logger.Field) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.errs = append(m.errs, msg)
}
func (m *mockLogger) ErrorWithErr(ctx context.Context, msg string, err error, fields ...logger.Field) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.errs = append(m.errs, fmt.Sprintf("%s: %v", msg, err))
}
func (m *mockLogger) SetLevel(level logger.Level)               { m.level = level }
func (m *mockLogger) GetLevel() logger.Level                    { return m.level }
func (m *mockLogger) With(fields ...logger.Field) logger.Logger { return m }
func (m *mockLogger) Named(name string) logger.Logger           { return m }

func (m *mockLogger) getErrors() []string {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.errs
}

// mockAIClient is a mock for the ai.Client interface.
type mockAIClient struct {
	ai.Client
	generateContentFunc func(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error)
}

func (m *mockAIClient) GenerateContent(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error) {
	if m.generateContentFunc != nil {
		return m.generateContentFunc(ctx, req)
	}
	return &ai.GenerateResponse{Content: `{"result": "default success"}`}, nil
}

// mockBackend is a mock for the ratelimit backends.Backend interface.
var _ backends.Backend = (*mockBackend)(nil)

type mockBackend struct {
	counts map[string]int64
	tokens map[string]float64
}

func (m *mockBackend) Get(ctx context.Context, key string) (*backends.BackendData, error) {
	return nil, nil
}

func (m *mockBackend) Set(ctx context.Context, key string, data *backends.BackendData, ttl time.Duration) error {
	return nil
}

func (m *mockBackend) Increment(ctx context.Context, key string, window time.Duration, limit int64) (int64, bool, error) {
	if m.counts == nil {
		m.counts = make(map[string]int64)
	}

	if m.counts[key] >= limit {
		return m.counts[key], false, nil
	}

	m.counts[key]++
	return m.counts[key], true, nil
}

func (m *mockBackend) ConsumeToken(ctx context.Context, key string, bucketSize int64, refillRate time.Duration, refillAmount int64, window time.Duration) (float64, bool, int64, error) {
	if m.tokens == nil {
		m.tokens = make(map[string]float64)
	}

	if _, ok := m.tokens[key]; !ok {
		m.tokens[key] = float64(bucketSize)
	}

	if m.tokens[key] < 1 {
		return m.tokens[key], false, 0, nil
	}

	m.tokens[key]--
	return m.tokens[key], true, 0, nil
}

func (m *mockBackend) Delete(ctx context.Context, key string) error {
	return nil
}

func (m *mockBackend) Close() error { return nil }

var _ strategies.Strategy = (*mockStrategy)(nil)

// mockStrategy is a mock implementation of the ratelimit strategies.Strategy interface for testing
// It mocks the Allow method to return predefined results.
type mockStrategy struct {
	allowFunc func(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyResult, error)
}

func (m *mockStrategy) Allow(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyResult, error) {
	return m.allowFunc(ctx, backend, key, limit, window)
}

func (m *mockStrategy) GetStatus(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyStatus, error) {
	return nil, nil
}

func (m *mockStrategy) Reset(ctx context.Context, backend backends.Backend, key string) error {
	return nil
}

func (m *mockStrategy) Name() string {
	return "mockStrategy"
}
