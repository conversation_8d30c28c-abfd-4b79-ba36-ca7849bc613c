package server

import (
	"net/http"
	"net/url"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/ajiwo/resumatter/config"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestServer(t *testing.T) {
	deps := &Dependencies{
		Logger: &mockLogger{},
	}
	t.Run("New", func(t *testing.T) {
		router := gin.New()
		srv, err := New(&config.Config{}, router, deps)
		require.NoError(t, err)
		assert.NotNil(t, srv)
	})

	t.Run("Run and Stop", func(t *testing.T) {
		router := gin.New()
		router.GET("/", func(ctx *gin.Context) {
			ctx.String(200, "OK")
		})

		cfg := &config.Config{
			Server: config.ServerConfig{Port: "7731"},
		}
		srv, err := New(cfg, router, deps)
		require.NoError(t, err)
		assert.NotNil(t, srv)

		var mu sync.RWMutex
		done := make(chan error, 1)
		serverStarted := make(chan struct{})

		// Run the server in a goroutine
		go func() {
			mu.Lock()
			defer mu.Unlock()
			// Signal that we're about to start the server
			close(serverStarted)

			// Run the server
			err := srv.Run()
			done <- err
		}()

		// wait for server to start
		<-serverStarted

		client := &http.Client{
			Timeout: 50 * time.Millisecond,
		}
		var resp *http.Response
		for range 10 {
			resp, err = client.Get("http://localhost:7731")
			if err == nil {
				break
			}
			time.Sleep(50 * time.Millisecond)
		}
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.NoError(t, resp.Body.Close())

		// Set up a timer to send SIGINT after a short delay to stop the server
		timer := time.AfterFunc(500*time.Millisecond, func() {
			// Send SIGINT to gracefully shutdown the server
			// This simulates Ctrl+C which the server is designed to handle
			if p := os.Getpid(); p > 0 {
				if proc, err := os.FindProcess(p); err == nil {
					if err := proc.Signal(os.Interrupt); err != nil {
						t.Errorf("Failed to send SIGINT: %v", err)
					}
				}
			}
		})
		defer timer.Stop()

		// Wait for either the server to finish or timeout
		select {
		case err := <-done:
			// Server finished (likely due to our SIGINT)
			assert.NoError(t, err)
		case <-time.After(3 * time.Second):
			// Fallback timeout in case SIGINT doesn't work
			t.Log("Test completed with timeout - this is expected")
		}

		resp, err = client.Get("http://localhost:7731")
		require.Error(t, err)
		assert.IsType(t, &url.Error{}, err)
		if resp != nil {
			assert.NoError(t, resp.Body.Close())
		}
	})
}
