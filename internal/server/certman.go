package server

import (
	"context"
	"crypto/tls"
	"errors"
	"sync"
	"time"

	"github.com/ajiwo/resumatter/logger"
	"github.com/fsnotify/fsnotify"
)

// certificateManager handles dynamic certificate loading
type certificateManager struct {
	certFile    string
	keyFile     string
	certificate *tls.Certificate
	mutex       sync.RWMutex
	logger      logger.Logger
}

// newCertificateManager creates a new certificate manager
func newCertificateManager(certFile, keyFile string, log logger.Logger) (*certificateManager, error) {
	if log == nil {
		return nil, errors.New("logger cannot be nil")
	}
	m := &certificateManager{
		certFile: certFile,
		keyFile:  keyFile,
		logger:   log,
	}
	if err := m.loadCertificate(); err != nil {
		return nil, err
	}
	return m, nil
}

// loadCertificate loads the TLS certificate from file
func (m *certificateManager) loadCertificate() error {
	cert, err := tls.LoadX509KeyPair(m.certFile, m.keyFile)
	if err != nil {
		return err
	}
	m.mutex.Lock()
	m.certificate = &cert
	m.mutex.Unlock()
	m.logger.Info(context.Background(), "Successfully loaded TLS certificate")
	return nil
}

// getCertificate returns the current certificate
func (m *certificateManager) getCertificate(*tls.ClientHelloInfo) (*tls.Certificate, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.certificate, nil
}

// watchCertificateFiles watches for changes in the certificate and key files
func (m *certificateManager) watchCertificateFiles(ctx context.Context) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		m.logger.Error(context.Background(), "Failed to create file watcher for TLS certs", logger.Err(err))
		return
	}
	defer func() {
		if closeErr := watcher.Close(); closeErr != nil {
			m.logger.Error(context.Background(), "Failed to close file watcher for TLS certs", logger.Err(closeErr))
		}
	}()

	// Debounce timer
	var debounceTimer *time.Timer
	debounceDuration := 500 * time.Millisecond

	go m.watchLoop(ctx, watcher, &debounceTimer, debounceDuration)

	err = watcher.Add(m.certFile)
	if err != nil {
		m.logger.Error(context.Background(), "Failed to add cert file to watcher", logger.Err(err))
	}
	err = watcher.Add(m.keyFile)
	if err != nil {
		m.logger.Error(context.Background(), "Failed to add key file to watcher", logger.Err(err))
	}

	// Wait for context cancellation
	<-ctx.Done()
}

// watchLoop handles the file watching loop in a separate goroutine
func (m *certificateManager) watchLoop(ctx context.Context, watcher *fsnotify.Watcher, debounceTimer **time.Timer, debounceDuration time.Duration) {
	for {
		select {
		case <-ctx.Done():
			return
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}
			if event.Op&fsnotify.Write == fsnotify.Write {
				m.handleFileChange(debounceTimer, debounceDuration)
			}
		case watchErr, ok := <-watcher.Errors:
			if !ok {
				return
			}
			m.logger.Error(context.Background(), "File watcher error", logger.Err(watchErr))
		}
	}
}

// handleFileChange handles certificate file changes with debouncing
func (m *certificateManager) handleFileChange(debounceTimer **time.Timer, debounceDuration time.Duration) {
	// Reset or create a timer
	if *debounceTimer != nil {
		(*debounceTimer).Stop()
	}
	*debounceTimer = time.AfterFunc(debounceDuration, func() {
		m.logger.Info(context.Background(), "TLS certificate or key file modified. Reloading...")
		if loadErr := m.loadCertificate(); loadErr != nil {
			m.logger.Error(context.Background(), "Failed to reload TLS certificate", logger.Err(loadErr))
		}
	})
}
