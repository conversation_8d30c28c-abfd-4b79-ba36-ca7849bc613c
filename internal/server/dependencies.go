package server

import (
	"sync"

	"github.com/ajiwo/resumatter/ai"
	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
)

// Dependencies holds the shared dependencies for the application.
type Dependencies struct {
	Config         *config.Config
	Logger         logger.Logger
	RateLimiter    *RateLimiter
	RateLimitMutex sync.RWMutex // To protect RateLimiter during reloads
	// Pre-initialized AI clients for each operation.
	AIClients map[string]ai.Client
}
