package server

import (
	"crypto/tls"
	"os"
	"testing"

	"github.com/ajiwo/resumatter/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func newTestServerWithTLS(t *testing.T, tlsCfg config.TLSConfig) *Server {
	t.Helper()
	cfg := &config.Config{
		TLS: tlsCfg,
	}
	// Minimal server instance; certificateManager can be zero-value
	return &Server{
		config:             cfg,
		certificateManager: &certificateManager{},
		deps:               &Dependencies{Logger: &mockLogger{}},
	}
}

func TestCreateTLSConfig_MinVersionEnforced(t *testing.T) {
	// Empty -> defaults to 1.2
	s := newTestServerWithTLS(t, config.TLSConfig{MinVersion: ""})
	tc, err := s.createTLSConfig()
	require.NoError(t, err)
	assert.Equal(t, uint16(tls.VersionTLS12), tc.MinVersion)

	// Explicit 1.2 -> 1.2
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.2"})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	assert.Equal(t, uint16(tls.VersionTLS12), tc.MinVersion)

	// Explicit 1.3 -> 1.3
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.3"})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	assert.Equal(t, uint16(tls.VersionTLS13), tc.MinVersion)

	// Lower than 1.2 (invalid) -> coerced to 1.2
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.1"})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	assert.Equal(t, uint16(tls.VersionTLS12), tc.MinVersion)
}

func TestCreateTLSConfig_CipherSuitesHandling(t *testing.T) {
	// Empty list -> use defaults (CipherSuites field should be nil)
	s := newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.2", CipherSuites: []string{}})
	tc, err := s.createTLSConfig()
	require.NoError(t, err)
	assert.Nil(t, tc.CipherSuites)

	// Contains a valid TLS 1.2 suite -> should be set
	validTLS12 := "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.2", CipherSuites: []string{validTLS12}})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	require.NotNil(t, tc.CipherSuites)
	require.Len(t, tc.CipherSuites, 1)
	assert.Equal(t, uint16(tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256), tc.CipherSuites[0])

	// Includes insecure suite -> ignored; result only contains the valid one
	insecure := "TLS_RSA_WITH_AES_128_GCM_SHA256"
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.2", CipherSuites: []string{validTLS12, insecure}})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	require.NotNil(t, tc.CipherSuites)
	require.Len(t, tc.CipherSuites, 1)
	assert.Equal(t, uint16(tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256), tc.CipherSuites[0])

	// Only insecure -> fall back to defaults (nil list)
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.2", CipherSuites: []string{insecure}})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	assert.Nil(t, tc.CipherSuites)

	// TLS 1.3 names are ignored (not configurable)
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.3", CipherSuites: []string{"TLS_AES_128_GCM_SHA256"}})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	assert.Nil(t, tc.CipherSuites)

	// Unknown name ignored; with a valid one still results in only the valid one
	s = newTestServerWithTLS(t, config.TLSConfig{MinVersion: "1.2", CipherSuites: []string{"FOO_BAR", validTLS12}})
	tc, err = s.createTLSConfig()
	require.NoError(t, err)
	require.NotNil(t, tc.CipherSuites)
	require.Len(t, tc.CipherSuites, 1)
	assert.Equal(t, uint16(tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256), tc.CipherSuites[0])
}

func TestCreateTLSConfig_MutualTLS_NoCAFile_UsesSystemOrEmptyPool(t *testing.T) {
	s := newTestServerWithTLS(t, config.TLSConfig{Mode: "mutual", MinVersion: "1.2", CAFile: ""})
	tc, err := s.createTLSConfig()
	require.NoError(t, err)
	assert.Equal(t, tls.RequireAndVerifyClientCert, tc.ClientAuth)
	// Either system roots or an empty pool (never nil)
	require.NotNil(t, tc.ClientCAs)
}

func TestCreateTLSConfig_MutualTLS_InvalidCAFile_ReturnsError(t *testing.T) {
	f, err := os.CreateTemp(t.TempDir(), "badca-*.pem")
	require.NoError(t, err)
	t.Cleanup(func() { _ = os.Remove(f.Name()) })
	_, err = f.WriteString("not a pem")
	require.NoError(t, err)
	require.NoError(t, f.Close())

	s := newTestServerWithTLS(t, config.TLSConfig{Mode: "mutual", MinVersion: "1.2", CAFile: f.Name()})
	tc, err := s.createTLSConfig()
	assert.Nil(t, tc)
	require.Error(t, err)
}
