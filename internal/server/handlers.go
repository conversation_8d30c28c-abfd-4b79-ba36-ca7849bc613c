package server

import (
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/ajiwo/resumatter/ai"
	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ContextKeyOperation is the key used to store the operation name in Gin context
const ContextKeyOperation = "operation"

// HealthHandler handles health check requests
func HealthHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"service":   "github.com/ajiwo/resumatter",
	})
}

// AIOperationHandler handles a specific AI operation.
type AIOperationHandler struct {
	operationName string
	aiClient      ai.Client
	log           logger.Logger
	cfg           *config.Config
}

// NewAIOperationHandler creates a new handler for a specific AI operation.
func NewAIOperationHandler(operationName string, cfg *config.Config, client ai.Client, log logger.Logger) *AIOperationHandler {
	return &AIOperationHandler{
		operationName: operationName,
		aiClient:      client,
		log:           log,
		cfg:           cfg,
	}
}

// Handle is the Gin handler function.
func (h *AIOperationHandler) Handle(c *gin.Context) {
	// Set operation name in context for rate limiting middleware
	c.Set(ContextKeyOperation, h.operationName)

	opDef, ok := ai.GetOperationDefinition(h.operationName)
	if !ok {
		handleError(c, h.log, fmt.Errorf("AI operation %s not found", h.operationName), http.StatusInternalServerError, "Internal server configuration error.")
		return
	}

	input := opDef.NewInput()
	if err := c.ShouldBindJSON(input); err != nil {
		handleError(c, h.log, err, http.StatusBadRequest, "Invalid request body. Please check the format and required fields.")
		return
	}

	// Resolve operation-specific configuration
	opCfg, err := h.cfg.ResolveOperationConfig(h.operationName)
	if err != nil {
		handleError(c, h.log, err, http.StatusInternalServerError, "Internal server configuration error.")
		return
	}

	// Call the generic AI service execution
	result, err := ai.ExecuteAIOperation(c.Request.Context(), h.aiClient, opCfg, h.operationName, input)
	if err != nil {
		handleAIError(c, h.log, err, h.operationName)
		return
	}

	// Get the preset name from the operation config
	presetName := "default"
	if op, exists := h.cfg.AI.Operations[h.operationName]; exists {
		presetName = op.Preset
	} else if h.cfg.AI.DefaultPreset != "" {
		presetName = h.cfg.AI.DefaultPreset
	}

	// Construct the response dynamically
	response := gin.H{
		"operation": h.operationName,
		"status":    "success",
		"preset":    presetName,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"data":      result,
	}

	c.JSON(http.StatusOK, response)
}

// handleError logs the detailed error and returns a generic JSON response
func handleError(c *gin.Context, log logger.Logger, internalErr error, statusCode int, publicMsg string) {
	errorID := uuid.New().String()
	log.ErrorWithErr(c.Request.Context(), "Request failed", internalErr,
		logger.String("error_id", errorID),
		logger.String("path", c.Request.URL.Path),
	)

	c.JSON(statusCode, gin.H{
		"error":     publicMsg,
		"error_id":  errorID,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	})
}

// handleAIError handles AI operation errors and returns appropriate HTTP status codes
func handleAIError(c *gin.Context, log logger.Logger, err error, operation string) {
	// Check if it's a circuit breaker error using AI package's custom error types
	if errors.Is(err, ai.ErrCircuitBreakerOpen) {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":       "Service temporarily unavailable",
			"details":     "Circuit breaker is open - too many recent failures",
			"operation":   operation,
			"timestamp":   time.Now().UTC().Format(time.RFC3339),
			"retry_after": "Please try again later",
		})
		return
	}

	if errors.Is(err, ai.ErrCircuitBreakerTooManyRequests) {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":       "Service temporarily unavailable",
			"details":     "Circuit breaker is half-open - too many concurrent requests",
			"operation":   operation,
			"timestamp":   time.Now().UTC().Format(time.RFC3339),
			"retry_after": "Please try again in a few moments",
		})
		return
	}

	// Use the new generic error handler for other AI failures
	handleError(c, log, err, http.StatusInternalServerError, "An unexpected error occurred during the AI operation.")
}
