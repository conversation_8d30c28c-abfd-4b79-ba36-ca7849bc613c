package server

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/ajiwo/resumatter/config"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupRouter(t *testing.T, cfg *config.Config) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(CORSMiddleware(cfg))
	router.Use(AuthMiddleware(cfg))
	router.GET("/test", func(c *gin.Context) {
		c.Status(http.StatusOK)
	})
	router.OPTIONS("/test", func(c *gin.Context) {
		c.Status(http.StatusOK)
	})
	return router
}

func TestCORSMiddleware(t *testing.T) {
	t.Run("CORS Disabled", func(t *testing.T) {
		cfg := &config.Config{Server: config.ServerConfig{CORS: config.CORSConfig{Enabled: false}}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Origin", "http://example.com")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Empty(t, w.Header().Get("Access-Control-Allow-Origin"))
	})

	t.Run("CORS Enabled Allow All", func(t *testing.T) {
		cfg := &config.Config{Server: config.ServerConfig{CORS: config.CORSConfig{
			Enabled:        true,
			AllowedOrigins: []string{"*"},
			AllowedMethods: []string{"GET", "POST"},
			AllowedHeaders: []string{"Content-Type"},
		}}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Origin", "http://example.com")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Equal(t, "GET, POST", w.Header().Get("Access-Control-Allow-Methods"))
		assert.Equal(t, "Content-Type", w.Header().Get("Access-Control-Allow-Headers"))
	})

	t.Run("CORS Enabled Specific Origin Match", func(t *testing.T) {
		cfg := &config.Config{Server: config.ServerConfig{CORS: config.CORSConfig{
			Enabled:        true,
			AllowedOrigins: []string{"http://example.com"},
		}}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Origin", "http://example.com")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "http://example.com", w.Header().Get("Access-Control-Allow-Origin"))
	})

	t.Run("CORS Enabled Specific Origin No Match", func(t *testing.T) {
		cfg := &config.Config{Server: config.ServerConfig{CORS: config.CORSConfig{
			Enabled:        true,
			AllowedOrigins: []string{"http://example.com"},
		}}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Origin", "http://another.com")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Empty(t, w.Header().Get("Access-Control-Allow-Origin"))
	})

	t.Run("CORS Preflight Request", func(t *testing.T) {
		cfg := &config.Config{Server: config.ServerConfig{CORS: config.CORSConfig{
			Enabled:        true,
			AllowedOrigins: []string{"*"},
		}}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("OPTIONS", "/test", nil)
		req.Header.Set("Origin", "http://example.com")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNoContent, w.Code)
	})
}

func TestAuthMiddleware(t *testing.T) {
	t.Run("Auth Disabled", func(t *testing.T) {
		cfg := &config.Config{Auth: config.AuthConfig{Enabled: false}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Auth Enabled No Header", func(t *testing.T) {
		cfg := &config.Config{Auth: config.AuthConfig{Enabled: true}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "Authorization header required")
	})

	t.Run("Auth Enabled Invalid Format", func(t *testing.T) {
		cfg := &config.Config{Auth: config.AuthConfig{Enabled: true}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "InvalidKey")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid authorization header format")
	})

	t.Run("Auth Enabled Invalid Key", func(t *testing.T) {
		cfg := &config.Config{Auth: config.AuthConfig{Enabled: true, APIKeys: []string{"valid-key"}}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer invalid-key")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid API key")
	})

	t.Run("Auth Enabled Valid Key", func(t *testing.T) {
		cfg := &config.Config{Auth: config.AuthConfig{Enabled: true, APIKeys: []string{"valid-key"}}}
		router := setupRouter(t, cfg)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer valid-key")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})
}
