package server

import (
	"net/http"
	"slices"
	"strings"

	"github.com/ajiwo/resumatter/config"

	"github.com/gin-gonic/gin"
)

// CORSMiddleware handles CORS configuration
func CORSMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		if cfg.Server.CORS.Enabled {
			// Set CORS headers
			if len(cfg.Server.CORS.AllowedOrigins) > 0 {
				origin := c.Request.Header.Get("Origin")
				for _, allowedOrigin := range cfg.Server.CORS.AllowedOrigins {
					if allowedOrigin == "*" || allowedOrigin == origin {
						c.<PERSON>er("Access-Control-Allow-Origin", allowedOrigin)
						break
					}
				}
			}

			if len(cfg.Server.CORS.AllowedMethods) > 0 {
				c.<PERSON><PERSON>("Access-Control-Allow-Methods", strings.Join(cfg.Server.CORS.AllowedMethods, ", "))
			}

			if len(cfg.Server.CORS.AllowedHeaders) > 0 {
				c.<PERSON><PERSON>("Access-Control-Allow-Headers", strings.Join(cfg.Server.CORS.AllowedHeaders, ", "))
			}

			// Handle preflight requests
			if c.Request.Method == "OPTIONS" {
				c.AbortWithStatus(http.StatusNoContent)
				return
			}
		}

		c.Next()
	}
}

// AuthMiddleware handles API key authentication
func AuthMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		if cfg.Auth.Enabled {
			// Get API key from Authorization header
			authHeader := c.GetHeader("Authorization")
			if authHeader == "" {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "Authorization header required",
				})
				c.Abort()
				return
			}

			// Extract API key (expecting "Bearer <api-key>" format)
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 || parts[0] != "Bearer" {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "Invalid authorization header format",
				})
				c.Abort()
				return
			}

			apiKey := parts[1]

			// Validate API key
			validKey := slices.Contains(cfg.Auth.APIKeys, apiKey)

			if !validKey {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "Invalid API key",
				})
				c.Abort()
				return
			}

			// Store API key in context for potential use in handlers
			c.Set("api_key", apiKey)
		}

		c.Next()
	}
}
