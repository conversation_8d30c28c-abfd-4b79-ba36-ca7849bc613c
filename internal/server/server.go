package server

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"reflect"
	"sync"
	"syscall"
	"time"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/fsnotify/fsnotify"
	"github.com/gin-gonic/gin"
)

// Server represents the HTTP server.
type Server struct {
	config             *config.Config
	httpServer         *http.Server
	certificateManager *certificateManager
	tlsMutex           sync.RWMutex
	deps               *Dependencies
}

// New creates a new HTTP server instance.
func New(cfg *config.Config, router *gin.Engine, deps *Dependencies) (*Server, error) {
	s := &Server{
		config: cfg,
		deps:   deps,
	}

	// Initialize certificate manager if TLS is enabled
	if cfg.TLS.Mode == "https" || cfg.TLS.Mode == "mutual" {
		certManager, err := newCertificateManager(cfg.TLS.CertFile, cfg.TLS.KeyFile, deps.Logger)
		if err != nil {
			return nil, fmt.Errorf("failed to create certificate manager: %w", err)
		}
		s.certificateManager = certManager
	}

	addr := ":" + cfg.Server.Port
	s.httpServer = &http.Server{
		Addr:              addr,
		Handler:           router,
		ReadHeaderTimeout: 5 * time.Second,
	}

	return s, nil
}

// Run starts the server and waits for shutdown signals.
func (s *Server) Run() error {
	// Configure TLS if enabled
	if s.config.TLS.Mode == "https" || s.config.TLS.Mode == "mutual" {
		tlsConfig, err := s.createTLSConfig()
		if err != nil {
			return fmt.Errorf("failed to create TLS config: %w", err)
		}
		s.httpServer.TLSConfig = tlsConfig
	}

	// Start certificate watcher if TLS is enabled
	certmanCtx, certmanCancel := context.WithCancel(context.Background())
	if s.certificateManager != nil {
		go s.certificateManager.watchCertificateFiles(certmanCtx)
	}

	// Start server in a goroutine
	go func() {
		var err error
		if s.config.TLS.Mode == "https" || s.config.TLS.Mode == "mutual" {
			s.deps.Logger.Info(context.Background(), "Starting HTTPS server", logger.String("address", s.httpServer.Addr))
			err = s.httpServer.ListenAndServeTLS("", "")
		} else {
			s.deps.Logger.Info(context.Background(), "Starting HTTP server", logger.String("address", s.httpServer.Addr))
			err = s.httpServer.ListenAndServe()
		}
		if err != nil && err != http.ErrServerClosed {
			s.deps.Logger.Error(context.Background(), "Server failed to start", logger.Err(err))
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// Stop certificate watcher
	certmanCancel()

	s.deps.Logger.Info(context.Background(), "Shutting down server...")
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	return s.httpServer.Shutdown(shutdownCtx)
}

// ReloadRateLimit is a callback function that reloads the rate limiting configuration on change
func (s *Server) ReloadRateLimit(event fsnotify.Event, oldCfg, newCfg *config.Config) {
	// Only proceed if the RateLimit section has actually changed.
	if reflect.DeepEqual(oldCfg.RateLimit, newCfg.RateLimit) {
		return // No changes to rate limit config, do nothing.
	}

	s.deps.Logger.Info(context.Background(), "Rate limit configuration changed, attempting to reload...", logger.String("file", event.Name))

	s.deps.RateLimitMutex.Lock()
	defer s.deps.RateLimitMutex.Unlock()

	// Update the config in dependencies
	s.deps.Config = newCfg
	s.config = newCfg

	// If rate limiting is disabled in new config, disable the rate limiter
	if !newCfg.RateLimit.Enabled {
		if s.deps.RateLimiter != nil {
			s.deps.Logger.Info(context.Background(), "Rate limiting disabled in new config, cleaning up existing rate limiter")
			if err := s.deps.RateLimiter.backend.Close(); err != nil {
				s.deps.Logger.Error(context.Background(), "Failed to close rate limiter backend", logger.Err(err))
			}
			s.deps.RateLimiter = nil
		}
		return
	}

	// Create new rate limiter with updated config
	newRateLimiter, err := NewRateLimiter(newCfg)
	if err != nil {
		s.deps.Logger.Error(context.Background(), "Failed to create new rate limiter, keeping existing one", logger.Err(err))
		// Revert the config pointer if the new config is invalid for this component
		s.deps.Config = oldCfg
		s.config = oldCfg
		return
	}

	// Clean up old rate limiter if it exists
	if s.deps.RateLimiter != nil {
		if err := s.deps.RateLimiter.backend.Close(); err != nil {
			s.deps.Logger.Error(context.Background(), "Failed to close rate limiter backend", logger.Err(err))
		}
	}

	s.deps.RateLimiter = newRateLimiter
	s.deps.Logger.Info(context.Background(), "Successfully reloaded and applied new rate limiting configuration.")
}
