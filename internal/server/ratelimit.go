package server

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/ajiwo/resumatter/ratelimit/backends"
	"github.com/ajiwo/resumatter/ratelimit/strategies"

	"github.com/gin-gonic/gin"
)

// RateLimiter manages rate limiting for the server
type RateLimiter struct {
	backend  backends.Backend
	strategy strategies.Strategy
	config   *config.Config // Change to pointer to full config
	logger   logger.Logger
}

// NewRateLimiter creates a new rate limiter based on the provided configuration
func NewRateLimiter(cfg *config.Config) (*RateLimiter, error) {
	backend, err := createBackend(cfg.RateLimit)
	if err != nil {
		return nil, fmt.Errorf("failed to create rate limit backend: %w", err)
	}

	strategy, err := createStrategy(cfg.RateLimit)
	if err != nil {
		return nil, fmt.Errorf("failed to create rate limit strategy: %w", err)
	}

	return &RateLimiter{
		backend:  backend,
		strategy: strategy,
		config:   cfg, // Store the full config
		logger: logger.New(&logger.Config{
			Level:  logger.ParseLevel(cfg.Logging.Level),
			Format: logger.ParseFormat(cfg.Logging.Format),
			Output: os.Stderr,
		}).Named("server.RateLimiter"),
	}, nil
}

// Middleware returns the Gin middleware for rate limiting
func (rl *RateLimiter) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		if !rl.config.RateLimit.Enabled {
			rl.logger.Debug(ctx, "Rate limiting disabled, skipping")
			c.Next()
			return
		}

		// Determine client identifier
		clientID := rl.getClientID(c)
		if clientID == "" {
			if rl.config.RateLimit.Required {
				rl.logger.Warn(ctx,
					"No client ID determined, rate limiting required, denying request",
				)
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
					"error": "Unauthorized",
				})
				return
			}
			// If no client ID can be determined and rate limiting is not required,
			// skip rate limiting
			rl.logger.Debug(ctx, "No client ID determined, skipping rate limit")
			c.Next()
			return
		}

		// Determine rate limit settings for this request
		rateLimits := rl.getRateLimitSettings(c)

		// Perform multi-tier rate limit check
		result, err := rl.checkMultiTierLimits(c, clientID, rateLimits)
		if err != nil {
			if rl.config.RateLimit.Required {
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
					"error": "Internal server error",
				})
				return
			}
			rl.logger.Warn(ctx,
				"Rate limit check failed, allowing request per 'rate_limit.required=false'",
			)
			c.Next()
			return
		}

		// Set rate limit headers (use the most restrictive limit for headers)
		c.Header("X-RateLimit-Limit", strconv.Itoa(result.PrimaryLimit))
		c.Header("X-RateLimit-Remaining", strconv.FormatInt(result.Remaining, 10))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(result.ResetTime.Unix(), 10))

		if !result.Allowed {
			rl.logger.Info(ctx, "Rate limit hit, denying request",
				logger.String("limit_type", result.LimitType),
				logger.Int64("remaining", result.Remaining),
				logger.Int64("retry_after", int64(result.RetryAfter.Seconds())),
			)

			c.Header("Retry-After", strconv.FormatInt(int64(result.RetryAfter.Seconds()), 10))
			c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
				"error":       "Too many requests",
				"limit_type":  result.LimitType,
				"retry_after": int64(result.RetryAfter.Seconds()),
			})
			return
		}

		rl.logger.Debug(ctx,
			"Rate limit passed, allowing request",
			logger.String("limit_type", result.LimitType),
			logger.Int64("remaining", result.Remaining),
		)
		c.Next()
	}
}

// getClientID determines the client identifier for rate limiting
func (rl *RateLimiter) getClientID(c *gin.Context) string {
	switch rl.config.RateLimit.KeyBy {
	case "api_key":
		// Assumes AuthMiddleware runs before this and sets the API key
		return c.GetString("api_key")
	case "header":
		return c.GetHeader(rl.config.RateLimit.HeaderName)
	case "ip":
		return c.ClientIP()
	default:
		return c.ClientIP()
	}
}

// RateLimitSettings holds all rate limit configurations for a request
type RateLimitSettings struct {
	RequestsPerMinute int
	RequestsPerHour   int
	RequestsPerDay    int
	Window            time.Duration
}

// MultiTierResult holds the result of multi-tier rate limit checking
type MultiTierResult struct {
	Allowed      bool
	Remaining    int64
	ResetTime    time.Time
	RetryAfter   time.Duration
	LimitType    string // "minute", "hour", or "day" - which limit was hit
	PrimaryLimit int    // The limit value for headers
}

// getRateLimitSettings determines all rate limit settings for the current request
func (rl *RateLimiter) getRateLimitSettings(c *gin.Context) *RateLimitSettings {
	operationName := c.GetString(ContextKeyOperation) // Get operation name from context

	// Resolve rate limit config for the specific operation, or use defaults
	resolvedConfig, err := rl.config.ResolveRateLimitConfig(operationName)
	if err != nil {
		// Log error and fallback to defaults if resolution fails
		rl.logger.Warn(c.Request.Context(),
			"Failed to resolve rate limit config for operation, using defaults",
			logger.String("operation", operationName),
			logger.Err(err),
		)
		resolvedConfig, _ = rl.config.ResolveRateLimitConfig("") // Get defaults
	}

	// Extract all rate limits
	settings := &RateLimitSettings{
		RequestsPerMinute: resolvedConfig.RequestsPerMinute,
		RequestsPerHour:   resolvedConfig.RequestsPerHour,
		RequestsPerDay:    resolvedConfig.RequestsPerDay,
		Window:            resolvedConfig.Window,
	}

	// If window is still zero (e.g., not set in config or parsing failed), default to 1 minute
	if settings.Window == 0 {
		settings.Window = time.Minute
	}

	return settings
}

// checkMultiTierLimits checks all applicable rate limits (minute/hour/day) for a client
// In internal/server/ratelimit.go
func (rl *RateLimiter) checkMultiTierLimits(c *gin.Context, clientID string, settings *RateLimitSettings) (*MultiTierResult, error) {
	ctx := c.Request.Context()
	var finalResult *MultiTierResult

	// Check limits in order. The first one to fail is the one we report.
	// The one with the lowest remaining requests is used for headers if all pass.
	limitChecks := []struct {
		limit     int
		duration  time.Duration
		limitType string
	}{
		{settings.RequestsPerMinute, time.Minute, "minute"},
		{settings.RequestsPerHour, time.Hour, "hour"},
		{settings.RequestsPerDay, 24 * time.Hour, "day"},
	}

	for _, check := range limitChecks {
		if check.limit <= 0 {
			continue
		}

		key := fmt.Sprintf("%s:%s", clientID, check.limitType)
		result, err := rl.strategy.Allow(ctx, rl.backend, key, int64(check.limit), check.duration)
		if err != nil {
			return nil, fmt.Errorf("rate limit check for %s failed: %w", check.limitType, err)
		}

		tierResult := &MultiTierResult{
			Allowed:      result.Allowed,
			Remaining:    result.Remaining,
			ResetTime:    result.ResetTime,
			RetryAfter:   result.RetryAfter,
			LimitType:    check.limitType,
			PrimaryLimit: check.limit,
		}

		if !tierResult.Allowed {
			return tierResult, nil // A limit was hit, return immediately.
		}

		// If allowed, track the result with the lowest remaining count.
		if finalResult == nil || tierResult.Remaining < finalResult.Remaining {
			finalResult = tierResult
		}
	}

	if finalResult == nil { // No limits were configured/checked
		return rl.createDefaultAllowResult(), nil
	}

	return finalResult, nil
}

// createDefaultAllowResult creates a default result when no limits are configured
func (rl *RateLimiter) createDefaultAllowResult() *MultiTierResult {
	return &MultiTierResult{
		Allowed:      true,
		Remaining:    1000, // Arbitrary high number
		ResetTime:    time.Now().Add(time.Hour),
		RetryAfter:   0,
		LimitType:    "none",
		PrimaryLimit: 1000,
	}
}

// createBackend creates a new backend instance from the configuration
func createBackend(cfg config.RateLimitConfig) (backends.Backend, error) {
	backendConfig := backends.BackendConfig{
		Type: cfg.Backend,
	}

	if cfg.Backend == "redis" {
		backendConfig.RedisAddress = cfg.Redis.Address
		backendConfig.RedisPassword = cfg.Redis.Password
		backendConfig.RedisDB = cfg.Redis.DB
		backendConfig.RedisPoolSize = cfg.Redis.PoolSize
	}

	return backends.NewBackend(backendConfig)
}

// createStrategy creates a new strategy instance from the configuration
func createStrategy(cfg config.RateLimitConfig) (strategies.Strategy, error) {
	strategyConfig := strategies.StrategyConfig{
		Type: cfg.Strategy,
	}

	switch cfg.Strategy {
	case "token_bucket":
		rate, amount := strategies.CalculateOptimalRefillRate(int64(cfg.Defaults.RequestsPerMinute), int64(cfg.Defaults.BurstSize))
		strategyConfig.RefillRate = rate
		strategyConfig.RefillAmount = amount
		strategyConfig.BucketSize = int64(cfg.Defaults.BurstSize)
	case "fixed_window":
		window, err := time.ParseDuration(cfg.Defaults.Window)
		if err != nil {
			window = time.Minute
		}
		strategyConfig.WindowDuration = window
	}

	return strategies.NewStrategy(strategyConfig)
}
