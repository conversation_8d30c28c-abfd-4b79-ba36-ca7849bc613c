package server

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/ratelimit/backends"
	"github.com/ajiwo/resumatter/ratelimit/strategies"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewRateLimiter(t *testing.T) {
	t.Run("Valid Config", func(t *testing.T) {
		cfg := &config.Config{
			RateLimit: config.RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "fixed_window",
			},
		}
		rl, err := NewRateLimiter(cfg)
		require.NoError(t, err)
		assert.NotNil(t, rl)
		assert.NotNil(t, rl.backend)
		assert.NotNil(t, rl.strategy)
	})

	t.Run("Invalid Backend", func(t *testing.T) {
		cfg := &config.Config{
			RateLimit: config.RateLimitConfig{
				Enabled:  true,
				Backend:  "invalid-backend",
				Strategy: "fixed_window",
			},
		}
		_, err := NewRateLimiter(cfg)
		require.Error(t, err)
	})

	t.Run("Invalid Strategy", func(t *testing.T) {
		cfg := &config.Config{
			RateLimit: config.RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "invalid-strategy",
			},
		}
		_, err := NewRateLimiter(cfg)
		require.Error(t, err)
	})
}

func TestRateLimiter_Middleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("Request Allowed", func(t *testing.T) {
		cfg := &config.Config{
			RateLimit: config.RateLimitConfig{
				Backend:  "memory",
				Strategy: "fixed_window",
				Enabled:  true,
				KeyBy:    "ip",
				Defaults: config.RateLimitDefaults{RequestsPerMinute: 5},
			},
		}
		rl, err := NewRateLimiter(cfg)
		require.NoError(t, err)
		// Replace backend with mock for predictable results
		rl.backend = &mockBackend{}

		router := gin.New()
		router.Use(rl.Middleware())
		router.GET("/", func(c *gin.Context) { c.Status(http.StatusOK) })

		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/", nil)
		req.RemoteAddr = "127.0.0.1:12345"
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "5", w.Header().Get("X-RateLimit-Limit"))
		assert.Equal(t, "4", w.Header().Get("X-RateLimit-Remaining"))
		assert.NotEmpty(t, w.Header().Get("X-RateLimit-Reset"))
	})

	t.Run("Request Denied", func(t *testing.T) {
		cfg := &config.Config{
			RateLimit: config.RateLimitConfig{
				Backend:  "memory",
				Strategy: "fixed_window",
				Enabled:  true,
				KeyBy:    "ip",
				Defaults: config.RateLimitDefaults{RequestsPerMinute: 1},
			},
		}
		rl, err := NewRateLimiter(cfg)
		require.NoError(t, err)
		rl.backend = &mockBackend{} // Use a fresh mock backend

		router := gin.New()
		router.Use(rl.Middleware())
		router.GET("/", func(c *gin.Context) { c.Status(http.StatusOK) })

		// First request should be allowed
		w1 := httptest.NewRecorder()
		req1, _ := http.NewRequest("GET", "/", nil)
		req1.RemoteAddr = "127.0.0.1:12345"
		router.ServeHTTP(w1, req1)
		assert.Equal(t, http.StatusOK, w1.Code)

		// Second request should be denied
		w2 := httptest.NewRecorder()
		req2, _ := http.NewRequest("GET", "/", nil)
		req2.RemoteAddr = "127.0.0.1:12345"
		router.ServeHTTP(w2, req2)

		assert.Equal(t, http.StatusTooManyRequests, w2.Code)
		assert.Equal(t, "1", w2.Header().Get("X-RateLimit-Limit"))
		assert.Equal(t, "0", w2.Header().Get("X-RateLimit-Remaining"))
		assert.NotEmpty(t, w2.Header().Get("Retry-After"))
		assert.Contains(t, w2.Body.String(), "Too many requests")
	})
}

func TestGetClientID(t *testing.T) {
	rl := &RateLimiter{}
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request, _ = http.NewRequest("GET", "/", nil)

	// IP
	rl.config = &config.Config{RateLimit: config.RateLimitConfig{KeyBy: "ip"}}
	c.Request.RemoteAddr = "127.0.0.1:1234"
	assert.Equal(t, "127.0.0.1", rl.getClientID(c))

	// Header
	rl.config = &config.Config{RateLimit: config.RateLimitConfig{KeyBy: "header", HeaderName: "X-Client-ID"}}
	c.Request.Header.Set("X-Client-ID", "client-123")
	assert.Equal(t, "client-123", rl.getClientID(c))

	// API Key
	rl.config = &config.Config{RateLimit: config.RateLimitConfig{KeyBy: "api_key"}}
	c.Set("api_key", "key-abc")
	assert.Equal(t, "key-abc", rl.getClientID(c))
}

func TestGetRateLimitSettings(t *testing.T) {
	minuteValue := 10
	hourValue := 50
	cfg := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "fixed_window",
			Defaults: config.RateLimitDefaults{
				RequestsPerMinute: 100,
				RequestsPerHour:   1000,
			},
			Operations: map[string]config.RateLimitOperation{
				"special-op": {
					Enabled:           true,
					RequestsPerMinute: &minuteValue,
					RequestsPerHour:   &hourValue,
				},
			},
		},
	}
	rl, err := NewRateLimiter(cfg)
	require.NoError(t, err)
	c, _ := gin.CreateTestContext(httptest.NewRecorder())

	// Test default settings
	c.Set(ContextKeyOperation, "some-other-op")
	settings := rl.getRateLimitSettings(c)
	assert.Equal(t, 100, settings.RequestsPerMinute)
	assert.Equal(t, 1000, settings.RequestsPerHour)
	assert.Equal(t, 0, settings.RequestsPerDay)

	// Test operation-specific override
	c.Set(ContextKeyOperation, "special-op")
	settings = rl.getRateLimitSettings(c)
	assert.Equal(t, 10, settings.RequestsPerMinute)
	assert.Equal(t, 50, settings.RequestsPerHour)
	assert.Equal(t, 0, settings.RequestsPerDay)
}

func TestCheckMultiTierLimits(t *testing.T) {
	rl := &RateLimiter{}
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request, _ = http.NewRequest("GET", "/", nil)

	t.Run("All Allowed", func(t *testing.T) {
		mockStrat := &mockStrategy{
			allowFunc: func(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyResult, error) {
				return &strategies.StrategyResult{Allowed: true, Remaining: 4}, nil
			},
		}
		rl.strategy = mockStrat
		rl.backend = &mockBackend{}
		settings := &RateLimitSettings{RequestsPerMinute: 5, RequestsPerHour: 10}
		result, err := rl.checkMultiTierLimits(c, "client1", settings)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, int64(4), result.Remaining)
		assert.Equal(t, "minute", result.LimitType)
		assert.Equal(t, 5, result.PrimaryLimit)
	})

	t.Run("Minute Limit Hit", func(t *testing.T) {
		mockStrat := &mockStrategy{
			allowFunc: func(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyResult, error) {
				if key == "client2:minute" {
					return &strategies.StrategyResult{Allowed: false, Remaining: 0}, nil
				}
				return &strategies.StrategyResult{Allowed: true, Remaining: 9}, nil
			},
		}
		rl.strategy = mockStrat
		rl.backend = &mockBackend{}
		settings := &RateLimitSettings{RequestsPerMinute: 5, RequestsPerHour: 10}
		result, err := rl.checkMultiTierLimits(c, "client2", settings)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.Equal(t, "minute", result.LimitType)
	})

	t.Run("Hour Limit Hit", func(t *testing.T) {
		mockStrat := &mockStrategy{
			allowFunc: func(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyResult, error) {
				if key == "client3:hour" {
					return &strategies.StrategyResult{Allowed: false, Remaining: 0}, nil
				}
				return &strategies.StrategyResult{Allowed: true, Remaining: 4}, nil
			},
		}
		rl.strategy = mockStrat
		rl.backend = &mockBackend{}
		settings := &RateLimitSettings{RequestsPerMinute: 5, RequestsPerHour: 10}
		result, err := rl.checkMultiTierLimits(c, "client3", settings)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.Equal(t, "hour", result.LimitType)
	})

	t.Run("No Limits Configured", func(t *testing.T) {
		mockStrat := &mockStrategy{
			allowFunc: func(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*strategies.StrategyResult, error) {
				return &strategies.StrategyResult{Allowed: true, Remaining: 0}, nil
			},
		}
		rl.strategy = mockStrat
		rl.backend = &mockBackend{}
		settings := &RateLimitSettings{}
		result, err := rl.checkMultiTierLimits(c, "client4", settings)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, "none", result.LimitType)
	})
}
