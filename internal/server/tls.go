package server

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"os"
	"reflect"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/fsnotify/fsnotify"
)

// ReloadTLSConfig is a callback function that reloads the TLS configuration on change
func (s *Server) ReloadTLSConfig(event fsnotify.Event, oldCfg, newCfg *config.Config) {
	// Only proceed if the TLS section has actually changed.
	if reflect.DeepEqual(oldCfg.TLS, newCfg.TLS) {
		return // No changes to TLS config, do nothing.
	}

	s.deps.Logger.Info(context.Background(), "TLS configuration changed, attempting to reload...", logger.String("file", event.Name))

	// No need to reload if the server is not running in a TLS mode
	if s.httpServer.TLSConfig == nil {
		return
	}

	s.tlsMutex.Lock()
	defer s.tlsMutex.Unlock()

	// Update the server's config pointer to the newly loaded one
	s.config = newCfg // Use the new config

	newTLSConfig, err := s.createTLSConfig()
	if err != nil {
		s.deps.Logger.Error(context.Background(), "Failed to create new TLS config, keeping existing one", logger.Err(err))
		// Revert the config pointer if the new config is invalid for this component
		s.config = oldCfg
		return
	}

	s.httpServer.TLSConfig = newTLSConfig
	s.deps.Logger.Info(context.Background(), "Successfully reloaded and applied new TLS configuration.")
}

// createTLSConfig creates TLS configuration based on config settings
func (s *Server) createTLSConfig() (*tls.Config, error) {
	tlsConfig := &tls.Config{
		GetCertificate: s.certificateManager.getCertificate,
		CurvePreferences: []tls.CurveID{
			tls.X25519,
			tls.CurveP256,
			tls.CurveP384,
			tls.CurveP521,
		},
		MinVersion: tls.VersionTLS12, // Set minimum TLS version to TLS 1.2
	}

	// Set minimum TLS version: enforce minimum of TLS 1.2
	switch s.config.TLS.MinVersion {
	case "1.2":
		tlsConfig.MinVersion = tls.VersionTLS12
	case "1.3":
		tlsConfig.MinVersion = tls.VersionTLS13
	default:
		// Any other value (including empty) defaults to 1.2
		tlsConfig.MinVersion = tls.VersionTLS12
	}

	s.configureCipherSuites(tlsConfig)

	if s.config.TLS.Mode == "mutual" {
		if err := s.configureMutualTLS(tlsConfig); err != nil {
			return nil, err
		}
	}

	return tlsConfig, nil
}

func (s *Server) configureCipherSuites(tlsConfig *tls.Config) {
	if len(s.config.TLS.CipherSuites) == 0 {
		return
	}

	// Build maps of supported (secure) and insecure cipher suites from stdlib
	supportedByName := make(map[string]uint16)
	for _, cs := range tls.CipherSuites() {
		supportedByName[cs.Name] = cs.ID
	}

	insecureByName := make(map[string]uint16)
	for _, cs := range tls.InsecureCipherSuites() {
		insecureByName[cs.Name] = cs.ID
	}

	isTLS13Name := func(name string) bool {
		switch name {
		case "TLS_AES_128_GCM_SHA256", "TLS_AES_256_GCM_SHA384", "TLS_CHACHA20_POLY1305_SHA256":
			return true
		default:
			return false
		}
	}

	cipherSuites := make([]uint16, 0, len(s.config.TLS.CipherSuites))
	for _, name := range s.config.TLS.CipherSuites {
		// TLS 1.3 cipher suites are not configurable via Config.CipherSuites
		if isTLS13Name(name) {
			s.deps.Logger.Info(context.Background(), "TLS 1.3 cipher suite is not configurable and will be ignored", logger.String("cipher_suite", name))
			continue
		}

		if id, ok := supportedByName[name]; ok {
			cipherSuites = append(cipherSuites, id)
			continue
		}
		if _, ok := insecureByName[name]; ok {
			s.deps.Logger.Warn(context.Background(), "Insecure cipher suite requested and ignored", logger.String("cipher_suite", name))
			continue
		}
		s.deps.Logger.Warn(context.Background(), "Unknown cipher suite requested and ignored", logger.String("cipher_suite", name))
	}

	if len(cipherSuites) > 0 {
		tlsConfig.CipherSuites = cipherSuites
	} else {
		s.deps.Logger.Warn(context.Background(), "No valid TLS 1.0–1.2 cipher suites specified; using Go's defaults")
	}
}

func (s *Server) configureMutualTLS(tlsConfig *tls.Config) error {
	tlsConfig.ClientAuth = tls.RequireAndVerifyClientCert

	if s.config.TLS.CAFile != "" {
		caCertPool, err := loadCACertificates(s.config.TLS.CAFile)
		if err != nil {
			return fmt.Errorf("failed to load CA certificates: %w", err)
		}
		tlsConfig.ClientCAs = caCertPool
		s.deps.Logger.Info(context.Background(), "Mutual TLS enabled with CA file", logger.String("ca_file", s.config.TLS.CAFile))
	} else {
		systemRoots, err := x509.SystemCertPool()
		if err != nil {
			s.deps.Logger.Warn(context.Background(), "Failed to load system root CAs, creating empty pool", logger.Err(err))
			systemRoots = x509.NewCertPool()
		}
		tlsConfig.ClientCAs = systemRoots
		s.deps.Logger.Info(context.Background(), "Mutual TLS enabled with system root CAs")
	}

	return nil
}

// Removed hardcoded cipher suite maps in favor of stdlib-derived lists

// loadCACertificates loads CA certificates from a file
func loadCACertificates(caFile string) (*x509.CertPool, error) {
	// Read the CA certificate file
	caCert, err := os.ReadFile(caFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read CA certificate file %s: %w", caFile, err)
	}

	// Create a new certificate pool
	caCertPool := x509.NewCertPool()

	// Parse and add the CA certificate(s) to the pool
	if !caCertPool.AppendCertsFromPEM(caCert) {
		return nil, fmt.Errorf("failed to parse CA certificate from %s", caFile)
	}

	// Note: logger is not available here; responsibility of caller to log success if needed
	return caCertPool, nil
}
