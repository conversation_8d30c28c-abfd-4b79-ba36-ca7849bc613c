package server

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/ajiwo/resumatter/ai"
	"github.com/ajiwo/resumatter/config"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHealthHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	HealthHandler(c)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, "healthy", response["status"])
	assert.Contains(t, response, "timestamp")
	assert.Equal(t, "github.com/ajiwo/resumatter", response["service"])
}

func TestAIOperationHandler(t *testing.T) {
	mockLog := &mockLogger{}
	mockCfg := &config.Config{
		AI: config.AIConfig{
			Operations: map[string]config.OperationConfig{
				"analyze": {Preset: "test-preset"},
			},
		},
	}
	// This is needed because ResolveOperationConfig is called.
	// We can simplify this by having a more focused config.
	mockCfg.AI.Presets = map[string]config.PresetConfig{
		"test-preset": {
			Provider:       "mock",
			Model:          "test-model",
			Timeout:        "5s",
			Temperature:    0.7,
			MaxTokens:      1000,
			ProviderConfig: map[string]any{},
		},
	}

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	t.Run("Successful Operation", func(t *testing.T) {
		mockClient := &mockAIClient{
			generateContentFunc: func(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error) {
				output := ai.AnalyzeJobOutput{
					JobQualityScore:       80,
					Clarity:               ai.JobQualityScore{Score: 80, Analysis: "Good clarity", Improvements: []string{}},
					Inclusivity:           ai.InclusivityAnalysis{Score: 85, Analysis: "Inclusive", FlaggedTerms: []string{}, Suggestions: []string{}},
					CandidateAttraction:   ai.CandidateAttraction{Score: 80, Strengths: []string{}, Weaknesses: []string{}},
					MarketCompetitiveness: ai.MarketCompetitiveness{SalaryTransparency: "High", RequirementsRealism: "Realistic", IndustryAlignment: "Aligned"},
					Recommendations:       []string{},
				}
				jsonBytes, _ := json.Marshal(output)
				return &ai.GenerateResponse{
					Content: string(jsonBytes),
				}, nil
			},
		}

		handler := NewAIOperationHandler("analyze", mockCfg, mockClient, mockLog)
		router.POST("/test-success", handler.Handle)

		w := httptest.NewRecorder()
		reqBody := `{"job_description": "test"}`
		req, _ := http.NewRequest(http.MethodPost, "/test-success", bytes.NewBufferString(reqBody))
		req.Header.Set("Content-Type", "application/json")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response struct {
			Status    string              `json:"status"`
			Operation string              `json:"operation"`
			Data      ai.AnalyzeJobOutput `json:"data"`
		}

		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "success", response.Status)
		assert.Equal(t, "analyze", response.Operation)
		assert.Equal(t, 80, response.Data.JobQualityScore)
	})

	t.Run("Invalid JSON", func(t *testing.T) {
		handler := NewAIOperationHandler("analyze", mockCfg, &mockAIClient{}, mockLog)
		router.POST("/test-invalid-json", handler.Handle)

		w := httptest.NewRecorder()
		reqBody := `{"job_description": "test"` // Invalid JSON
		req, _ := http.NewRequest(http.MethodPost, "/test-invalid-json", bytes.NewBufferString(reqBody))
		req.Header.Set("Content-Type", "application/json")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid request body")
	})

	t.Run("AI Operation Not Found", func(t *testing.T) {
		handler := NewAIOperationHandler("non-existent-op", mockCfg, &mockAIClient{}, mockLog)
		router.POST("/test-not-found", handler.Handle)

		w := httptest.NewRecorder()
		reqBody := `{"field": "test"}`
		req, _ := http.NewRequest(http.MethodPost, "/test-not-found", bytes.NewBufferString(reqBody))
		req.Header.Set("Content-Type", "application/json")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
		assert.Contains(t, w.Body.String(), "Internal server configuration error")
	})

	t.Run("AI Execution Error", func(t *testing.T) {
		mockClient := &mockAIClient{
			generateContentFunc: func(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error) {
				return nil, errors.New("AI client failed")
			},
		}
		handler := NewAIOperationHandler("analyze", mockCfg, mockClient, mockLog)
		router.POST("/test-ai-error", handler.Handle)

		w := httptest.NewRecorder()
		reqBody := `{"job_description": "test"}`
		req, _ := http.NewRequest(http.MethodPost, "/test-ai-error", bytes.NewBufferString(reqBody))
		req.Header.Set("Content-Type", "application/json")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
		assert.Contains(t, w.Body.String(), "An unexpected error occurred")
	})

	t.Run("Circuit Breaker Open Error", func(t *testing.T) {
		mockClient := &mockAIClient{
			generateContentFunc: func(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error) {
				return nil, ai.ErrCircuitBreakerOpen
			},
		}
		handler := NewAIOperationHandler("analyze", mockCfg, mockClient, mockLog)
		router.POST("/test-cb-open", handler.Handle)

		w := httptest.NewRecorder()
		reqBody := `{"job_description": "test"}`
		req, _ := http.NewRequest(http.MethodPost, "/test-cb-open", bytes.NewBufferString(reqBody))
		req.Header.Set("Content-Type", "application/json")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusServiceUnavailable, w.Code)
		assert.Contains(t, w.Body.String(), "Circuit breaker is open")
	})

	t.Run("Circuit Breaker Too Many Requests Error", func(t *testing.T) {
		mockClient := &mockAIClient{
			generateContentFunc: func(ctx context.Context, req *ai.GenerateRequest) (*ai.GenerateResponse, error) {
				return nil, ai.ErrCircuitBreakerTooManyRequests
			},
		}
		handler := NewAIOperationHandler("analyze", mockCfg, mockClient, mockLog)
		router.POST("/test-cb-requests", handler.Handle)

		w := httptest.NewRecorder()
		reqBody := `{"job_description": "test"}`
		req, _ := http.NewRequest(http.MethodPost, "/test-cb-requests", bytes.NewBufferString(reqBody))
		req.Header.Set("Content-Type", "application/json")

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusServiceUnavailable, w.Code)
		assert.Contains(t, w.Body.String(), "too many concurrent requests")
	})
}

func TestHandleError(t *testing.T) {
	mockLog := &mockLogger{}
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest(http.MethodGet, "/test-path", nil)

	internalErr := errors.New("internal database error")
	publicMsg := "Something went wrong."
	statusCode := http.StatusInternalServerError

	handleError(c, mockLog, internalErr, statusCode, publicMsg)

	assert.Equal(t, statusCode, w.Code)

	var response map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, publicMsg, response["error"])
	assert.NotEmpty(t, response["error_id"])
	assert.NotEmpty(t, response["timestamp"])

	// Check logs
	errorLogs := mockLog.getErrors()
	require.Len(t, errorLogs, 1)
	assert.True(t, strings.HasPrefix(errorLogs[0], "Request failed: internal database error"))
}
