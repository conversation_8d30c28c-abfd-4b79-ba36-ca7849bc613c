name: Docker

permissions:
  contents: read
  packages: write

on:
  workflow_run:
    workflows: ["CI"]
    types:
      - completed
    branches:
      - main

jobs:
  build-and-publish:
    name: Build and Publish
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.workflow_run.head_sha }}

      - name: Generate version info
        id: version
        run: |
          echo "VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo 'dev')" >> $GITHUB_OUTPUT
          echo "COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" >> $GITHUB_OUTPUT
          echo "DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $GITHUB_OUTPUT

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ghcr.io/${{ github.repository }}:main
          build-args: |
            VERSION=${{ steps.version.outputs.VERSION }}
            COMMIT=${{ steps.version.outputs.COMMIT }}
            DATE=${{ steps.version.outputs.DATE }}
