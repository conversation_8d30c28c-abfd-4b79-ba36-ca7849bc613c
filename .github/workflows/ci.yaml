name: CI

on:
  push:
    branches: [master]
  pull_request:

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        include:
          - goos: js
            goarch: wasm
          - goos: aix
            goarch: ppc64

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5.2.0
        with:
          go-version: "1.24"

      - name: Build
        run: go build .
        env:
          GOOS: ${{ matrix.goos }}
          GOARCH: ${{ matrix.goarch }}

  test:
    name: Test
    runs-on: ${{ matrix.os }}

    strategy:
      # Fail fast is disabled because there are Go version specific features and tests
      # that should be able to fail independently.
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        go: ["1.21", "1.22", "1.23", "1.24"]
        tags: ["", "viper_finder", "viper_bind_struct"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5.2.0
        with:
          go-version: ${{ matrix.go }}

      - name: Test
        run: go test -race -v -tags '${{ matrix.tags }}' -shuffle=on ./...
        if: runner.os != 'Windows'

      - name: Test (without race detector)
        run: go test -v -tags '${{ matrix.tags }}' -shuffle=on ./...
        if: runner.os == 'Windows'

  lint:
    name: Lint
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5.2.0
        with:
          go-version: "1.24"

      - name: Lint
        uses: golangci/golangci-lint-action@971e284b6050e8a5849b72094c50ab08da042db8 # v6.1.1
        with:
          version: v1.64.5

  dev:
    name: Developer environment
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Nix
        uses: cachix/install-nix-action@3715ab1a11cac9e991980d7b4a28d80c7ebdd8f9 # v27
        with:
          extra_nix_config: |
            access-tokens = github.com=${{ secrets.GITHUB_TOKEN }}

      - name: Check
        run: nix flake check --impure

      - name: Dev shell
        run: nix develop --impure

  dependency-review:
    name: Dependency review
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Dependency Review
        uses: actions/dependency-review-action@3b139cfc5fae8b618d3eae3675e383bb1769c019 # v4.5.0
