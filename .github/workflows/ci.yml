name: CI
permissions:
  contents: read

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  ci:
    name: CI
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis/redis-stack-server:7.2.0-v18
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping" 
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.5"

      - name: Install golangci-lint
        run: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest 

      - name: Run CI
        run: make ci

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          files: coverage/coverage.out
          token: ${{ secrets.CODECOV_TOKEN }}
