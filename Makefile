# Go variables
GO        ?= go
GOFMT     ?= gofmt
GOLINT    ?= golangci-lint
GOIMPORTS ?= goimports
PROJECT_NAME := resumatter
BINARY_NAME  := $(PROJECT_NAME)

# Directories
CMD_DIR   := ./cmd/$(PROJECT_NAME)
BUILD_DIR := ./build
COVERAGE_DIR := ./coverage

# Version and build info
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
COMMIT  ?= $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
DATE    ?= $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")

# Build flags - inject directly into cli package to match Dockerfile
LDFLAGS := -ldflags "-w -s -X 'github.com/ajiwo/resumatter/internal/cli.version=$(VERSION)' -X 'github.com/ajiwo/resumatter/internal/cli.commit=$(COMMIT)' -X 'github.com/ajiwo/resumatter/internal/cli.date=$(DATE)'"

.PHONY: all build build-all test test-unit test-integration test-coverage test-coverage-html clean lint lint-fix format format-check deps deps-update deps-verify install uninstall run dev help

all: clean deps format lint test build

## Build targets
build: ## Build the application
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(CMD_DIR)/main.go
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

build-all: ## Build for all platforms
	@echo "Building for all platforms..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(CMD_DIR)/main.go
	GOOS=darwin GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(CMD_DIR)/main.go
	GOOS=darwin GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 $(CMD_DIR)/main.go
	GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(CMD_DIR)/main.go
	@echo "Cross-platform build complete."

## Test targets
test: test-unit ## Run all tests (alias for test-unit)

test-unit: ## Run unit tests
	@echo "Running unit tests..."
	$(GO) test -v -race -short ./...

test-integration: ## Run integration tests
	@echo "Running integration tests..."
	$(GO) test -v -race -tags integration ./...

test-all: ## Run all tests including integration
	@echo "Running all tests..."
	$(GO) test -v -race -tags integration ./...

test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	@mkdir -p $(COVERAGE_DIR)
	$(GO) test -v -race -coverprofile=$(COVERAGE_DIR)/coverage.out -covermode=atomic ./...
	$(GO) tool cover -func=$(COVERAGE_DIR)/coverage.out
	@echo "Coverage report saved to $(COVERAGE_DIR)/coverage.out"

test-coverage-html: test-coverage ## Generate HTML coverage report
	@echo "Generating HTML coverage report..."
	$(GO) tool cover -html=$(COVERAGE_DIR)/coverage.out -o $(COVERAGE_DIR)/coverage.html
	@echo "HTML coverage report saved to $(COVERAGE_DIR)/coverage.html"

## Code quality targets
lint: ## Run golangci-lint
	@echo "Running golangci-lint..."
	@# Install golangci-lint if not already installed: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOLINT) run ./...

lint-fix: ## Run golangci-lint with auto-fix
	@echo "Running golangci-lint with auto-fix..."
	$(GOLINT) run --fix ./...

format: ## Format Go code
	@echo "Formatting Go code..."
	$(GOFMT) -s -w .
	@if command -v $(GOIMPORTS) >/dev/null 2>&1; then \
		echo "Running goimports..."; \
		$(GOIMPORTS) -w .; \
	else \
		echo "goimports not found, install with: go install golang.org/x/tools/cmd/goimports@latest"; \
	fi

format-check: ## Check if code is formatted
	@echo "Checking code formatting..."
	@if [ -n "$$($(GOFMT) -l .)" ]; then \
		echo "Code is not formatted. Run 'make format' to fix."; \
		$(GOFMT) -l .; \
		exit 1; \
	fi
	@echo "Code is properly formatted."

## Dependency targets
deps: ## Download and tidy Go modules
	@echo "Downloading and tidying Go modules..."
	$(GO) mod download
	$(GO) mod tidy

deps-update: ## Update Go modules
	@echo "Updating Go modules..."
	$(GO) get -u ./...
	$(GO) mod tidy

deps-verify: ## Verify Go modules
	@echo "Verifying Go modules..."
	$(GO) mod verify

## Installation targets
install: build ## Install the binary to GOPATH/bin
	@echo "Installing $(BINARY_NAME)..."
	$(GO) install $(LDFLAGS) $(CMD_DIR)/main.go

uninstall: ## Remove the binary from GOPATH/bin
	@echo "Uninstalling $(BINARY_NAME)..."
	rm -f $(shell $(GO) env GOPATH)/bin/$(BINARY_NAME)

## Development targets
run: ## Run the application
	@echo "Running $(BINARY_NAME)..."
	$(GO) run $(CMD_DIR)/main.go

dev: ## Run in development mode with live reload (requires air)
	@if command -v air >/dev/null 2>&1; then \
		echo "Starting development server with air..."; \
		air; \
	else \
		echo "air not found, install with: go install github.com/cosmtrek/air@latest"; \
		echo "Falling back to regular run..."; \
		$(MAKE) run; \
	fi

## Cleanup targets
clean: ## Clean build artifacts and coverage reports
	@echo "Cleaning build artifacts..."
	$(GO) clean
	rm -rf $(BUILD_DIR)
	rm -rf $(COVERAGE_DIR)
	@echo "Clean complete."

## Utility targets
version: ## Show version information
	@echo "Version: $(VERSION)"
	@echo "Commit:  $(COMMIT)"
	@echo "Date:    $(DATE)"

check: format-check lint test ## Run all checks (format, lint, test)

ci: deps format-check lint test-coverage ## Run CI pipeline

help: ## Display this help screen
	@echo "Available targets:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Examples:"
	@echo "  make build          # Build the application"
	@echo "  make test-coverage  # Run tests with coverage report"
	@echo "  make check          # Run all quality checks"
	@echo "  make ci             # Run full CI pipeline"
