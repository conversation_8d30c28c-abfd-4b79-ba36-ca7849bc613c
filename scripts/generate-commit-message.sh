#!/bin/bash

# Script for generating a git commit message for local development.
# It calls the /api/v1/ai/git-commit endpoint.
# Usage: ./scripts/generate-git-commit.sh [server_port]

set -e

SERVER_PORT=${1:-8780}
BASE_URL="http://localhost:${SERVER_PORT}"
ENDPOINT="${BASE_URL}/api/v1/ai/git-commit"
OUTPUT_FILE="tmp_commit_message.txt"

echo "=== Generating Git Commit Message ==="
echo "Endpoint: ${ENDPOINT}"
echo "Timestamp: $(date)"
echo ""

# Get recent commit history and staged diff
echo "Fetching git history and staged changes..."
RECENT_COMMITS=$(git log -n 3)
GIT_DIFF=$(git diff --staged)

if [ -z "$GIT_DIFF" ]; then
    echo "No staged changes found. Aborting."
    exit 0
fi

echo "Git data prepared:"
echo "Recent commits: fetched"
echo "Git diff size: $(echo "$GIT_DIFF" | wc -c) characters"
echo ""

# Create JSON payload
PAYLOAD_FILE=$(mktemp)
trap 'rm -f "$PAYLOAD_FILE"' EXIT

jq -n --arg history "$RECENT_COMMITS" --arg diff "$GIT_DIFF" \
  '{"recent_commit_history": $history, "git_diff": $diff}' > "$PAYLOAD_FILE"

echo "Payload created ($(stat -c%s "$PAYLOAD_FILE") bytes)"
echo ""

# Make the request
echo "Making request to git-commit endpoint..."
echo "Timeout: 60 seconds"
echo ""

START_TIME=$(date +%s)

RESPONSE=$(curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @"$PAYLOAD_FILE" \
  --max-time 60 \
  --silent \
  --show-error)

if [ $? -ne 0 ]; then
    echo "Curl request failed."
    echo "Raw response:"
    echo "$RESPONSE"
    exit 1
fi

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

# Extract commit message from the JSON response
COMMIT_MESSAGE=$(echo "$RESPONSE" | jq -r '.data.commit_message')

if [ -z "$COMMIT_MESSAGE" ] || [ "$COMMIT_MESSAGE" == "null" ]; then
    echo "Failed to generate commit message from the response."
    echo "Raw response:"
    echo "$RESPONSE" | jq '.'
    exit 1
fi

# Save the commit message to the output file in the project root
echo "$COMMIT_MESSAGE" > "$OUTPUT_FILE"

echo ""
echo "================================================================================"
echo "Generated Commit Message (saved to ${OUTPUT_FILE}):"
echo "--------------------------------------------------------------------------------"
cat "$OUTPUT_FILE"
echo "================================================================================"
echo ""
echo "Please check, adjust/edit the generated git commit message in '$OUTPUT_FILE'"
echo "before running: 'git commit -F $OUTPUT_FILE'"
echo ""
echo "Generation completed in ${DURATION} seconds."
