#!/bin/bash

# Test script for /api/v1/ai/git-commit endpoint
# Usage: ./scripts/test-git-commit.sh [server_port]

set -e

SERVER_PORT=${1:-8780}
BASE_URL="http://localhost:${SERVER_PORT}"
ENDPOINT="${BASE_URL}/api/v1/ai/git-commit"

echo "=== Testing Git Commit Generation Endpoint ==="
echo "Endpoint: ${ENDPOINT}"
echo "Timestamp: $(date)"
echo ""

# Sample recent commit history
RECENT_COMMITS="feat: add user authentication system
fix: resolve database connection timeout issues
docs: update API documentation with new endpoints
refactor: improve error handling in user service
test: add integration tests for payment processing
chore: update dependencies to latest versions"

# Sample git diff
GIT_DIFF="diff --git a/internal/server/handler.go b/internal/server/handler.go
index 1234567..abcdefg 100644
--- a/internal/server/handler.go
+++ b/internal/server/handler.go
@@ -34,15 +34,72 @@ import (
 
 // AI Operation Handlers
 
+// EvaluateRequest represents the request body for resume evaluation
+type EvaluateRequest struct {
+	BaseResume     string \`json:\"base_resume\" binding:\"required\"\`
+	TailoredResume string \`json:\"tailored_resume\" binding:\"required\"\`
+}
+
 // EvaluateHandler handles resume evaluation requests
-func EvaluateHandler(c *gin.Context) {
-	c.JSON(http.StatusOK, gin.H{
-		\"operation\": \"evaluate\",
-		\"status\":    \"success\",
-		\"message\":   \"Resume evaluation completed\",
-		\"timestamp\": time.Now().UTC().Format(time.RFC3339),
-		\"preset\":    \"enterprise-vertex\",
-		\"score\":     85.5,
-	})
+func EvaluateHandler(cfg *config.Config) gin.HandlerFunc {
+	return func(c *gin.Context) {
+		var req EvaluateRequest
+		if err := c.ShouldBindJSON(&req); err != nil {
+			c.JSON(http.StatusBadRequest, gin.H{
+				\"error\":     \"Invalid request body\",
+				\"details\":   err.Error(),
+				\"timestamp\": time.Now().UTC().Format(time.RFC3339),
+			})
+			return
+		}
+
+		// Create AI client for evaluate operation
+		ctx := c.Request.Context()
+		client, err := ai.NewClient(ctx, cfg, \"evaluate\")
+		if err != nil {
+			c.JSON(http.StatusInternalServerError, gin.H{
+				\"error\":     \"Failed to create AI client\",
+				\"details\":   err.Error(),
+				\"timestamp\": time.Now().UTC().Format(time.RFC3339),
+			})
+			return
+		}
+
+		// Call the AI service
+		result, err := ai.EvaluateResume(ctx, client, evaluateInput)
+		if err != nil {
+			c.JSON(http.StatusInternalServerError, gin.H{
+				\"error\":     \"AI evaluation failed\",
+				\"details\":   err.Error(),
+				\"timestamp\": time.Now().UTC().Format(time.RFC3339),
+			})
+			return
+		}
+
+		// Return successful response
+		c.JSON(http.StatusOK, gin.H{
+			\"operation\": \"evaluate\",
+			\"status\":    \"success\",
+			\"preset\":    presetName,
+			\"timestamp\": time.Now().UTC().Format(time.RFC3339),
+			\"summary\":   result.Summary,
+			\"findings\":  result.Findings,
+		})
+	}
+}"

echo "Sample data prepared:"
echo "Recent commits: $(echo "$RECENT_COMMITS" | wc -l) commits"
echo "Git diff size: $(echo "$GIT_DIFF" | wc -c) characters"
echo ""

# Create JSON payload
echo "Creating JSON payload..."
cat > /tmp/git_commit_payload.json << EOF
{
  "recent_commit_history": $(echo "$RECENT_COMMITS" | jq -R -s .),
  "git_diff": $(echo "$GIT_DIFF" | jq -R -s .)
}
EOF

echo "Payload created ($(stat -c%s /tmp/git_commit_payload.json) bytes)"
echo ""

# Make the request
echo "Making request to git-commit endpoint..."
echo "Timeout: 60 seconds"
echo ""

START_TIME=$(date +%s)

RESPONSE=$(curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @/tmp/git_commit_payload.json \
  --max-time 60 \
  --silent \
  --show-error \
  --write-out "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\nResponse Size: %{size_download} bytes\n")

echo "$RESPONSE" | head -n -4 | jq '.' 2>/dev/null || {
    echo "Request failed or invalid JSON response"
    echo "Raw response:"
    echo "$RESPONSE"
    exit 1
}

# Extract and display metrics
echo "$RESPONSE" | tail -n 4

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "Test completed successfully!"
echo "Total duration: ${DURATION} seconds"

# Show commit message summary
echo ""
echo "Generated Commit Message:"
COMMIT_MESSAGE=$(echo "$RESPONSE" | head -n -4 | jq -r '.data.commit_message // "No commit message generated"' 2>/dev/null)
COMMIT_TYPE=$(echo "$RESPONSE" | head -n -4 | jq -r '.data.type // "unknown"' 2>/dev/null)
COMMIT_SCOPE=$(echo "$RESPONSE" | head -n -4 | jq -r '.data.scope // "none"' 2>/dev/null)
IS_BREAKING=$(echo "$RESPONSE" | head -n -4 | jq -r '.data.breaking // false' 2>/dev/null)

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "$COMMIT_MESSAGE"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "Commit Analysis:"
echo "• Type: $COMMIT_TYPE"
echo "• Scope: $COMMIT_SCOPE"
echo "• Breaking Change: $IS_BREAKING"

# Cleanup
rm -f /tmp/git_commit_payload.json

echo ""
echo "=== Test Summary ==="
echo "• Endpoint: POST /api/v1/ai/git-commit"
echo "• Status: PASSED"
echo "• Purpose: Generate conventional commit messages from git diffs"
echo "• Response: Structured commit with type, scope, and breaking change info"
