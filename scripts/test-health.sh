#!/bin/bash

# Test script for server health and basic endpoints
# Usage: ./scripts/test-health.sh [server_port]

set -e

SERVER_PORT=${1:-8780}
BASE_URL="http://localhost:${SERVER_PORT}"

echo "=== Server Health Check ==="
echo "Server URL: ${BASE_URL}"
echo "Timestamp: $(date)"
echo ""

# Test health endpoint
echo "Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s "${BASE_URL}/health" --max-time 10 || echo "FAILED")

if [[ "$HEALTH_RESPONSE" == "FAILED" ]]; then
    echo "Health check failed - server not responding"
    exit 1
fi

echo "Health endpoint responding"
echo "$HEALTH_RESPONSE" | jq '.' 2>/dev/null || echo "Response: $HEALTH_RESPONSE"
echo ""

# Test info endpoint
echo "Testing info endpoint..."
INFO_RESPONSE=$(curl -s "${BASE_URL}/api/v1/info" --max-time 10 || echo "FAILED")

if [[ "$INFO_RESPONSE" == "FAILED" ]]; then
    echo "Info endpoint failed"
else
    echo "Info endpoint responding"
    echo "$INFO_RESPONSE" | jq '.' 2>/dev/null || echo "Response: $INFO_RESPONSE"
fi
echo ""

# Test config endpoints
echo "Testing config endpoints..."

echo "Available presets:"
curl -s "${BASE_URL}/api/v1/config/presets" --max-time 10 | jq '.total, .presets[0].name' 2>/dev/null || echo "Failed to get presets"

echo ""
echo "Available operations:"
curl -s "${BASE_URL}/api/v1/config/operations" --max-time 10 | jq '.total, .operations[].name' 2>/dev/null || echo "Failed to get operations"

echo ""
echo "Server health check completed successfully!"
echo ""
echo "=== Available AI Endpoints ==="
echo "• POST ${BASE_URL}/api/v1/ai/tailor"
echo "• POST ${BASE_URL}/api/v1/ai/evaluate"
echo "• POST ${BASE_URL}/api/v1/ai/analyze"
echo "• POST ${BASE_URL}/api/v1/ai/git-commit"
