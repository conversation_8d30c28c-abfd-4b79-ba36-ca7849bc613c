#!/bin/bash
#
# Generates a self-signed Certificate Authority (CA).
#
# This script creates a private key (ca-key.pem) and a self-signed CA
# certificate (ca.pem). This CA can then be used to sign server and
# client certificates.
#
# Outputs:
#  - ca-key.pem: The private key for the CA.
#  - ca.pem: The self-signed CA certificate.

set -e

# Configuration
KEY_FILE="ca-key.pem"
CERT_FILE="ca.pem"
DAYS_VALID=3650 # 10 years
COUNTRY="US"
STATE="California"
LOCALITY="San Francisco"
ORGANIZATION="Development CA"
COMMON_NAME="Dev CA"

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo "Error: openssl is not installed." >&2
    exit 1
fi

# Generate CA private key
echo "Generating CA private key..."
openssl genpkey -algorithm RSA -out "${KEY_FILE}" -aes256 -pass pass:development

# Generate self-signed CA certificate
echo "Generating self-signed CA certificate..."
openssl req -new -x509 -days "${DAYS_VALID}" \
    -key "${KEY_FILE}" \
    -out "${CERT_FILE}" \
    -subj "/C=${COUNTRY}/ST=${STATE}/L=${LOCALITY}/O=${ORGANIZATION}/CN=${COMMON_NAME}" \
    -passin pass:development

echo "CA certificate and key generated successfully:"
echo "  - Key: ${KEY_FILE}"
echo "  - Certificate: ${CERT_FILE}"
echo
echo "IMPORTANT: Keep your CA key secure!"
