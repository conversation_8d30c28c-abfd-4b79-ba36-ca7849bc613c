#!/bin/bash
#
# Generates a server certificate and private key, signed by the local CA.
#
# Usage:
#   ./02-generate-certs.sh [domain]
#
# Example:
#   ./02-generate-certs.sh localhost
#
# This script requires the CA key (ca-key.pem) and certificate (ca.pem)
# to be present in the same directory.
#
# Arguments:
#  - domain: The domain name for the certificate (e.g., localhost, myapp.dev).
#            Defaults to "localhost".
#
# Outputs:
#  - server-key.pem: The private key for the server.
#  - server.pem: The server certificate, signed by the CA.

set -e

# Configuration
DOMAIN=${1:-localhost}
KEY_FILE="server-key.pem"
CERT_FILE="server.pem"
CSR_FILE="server.csr"
EXT_FILE="ext.cnf"
CA_KEY="ca-key.pem"
CA_CERT="ca.pem"
DAYS_VALID=365

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo "Error: openssl is not installed." >&2
    exit 1
fi

# Check for CA files
if [ ! -f "${CA_KEY}" ] || [ ! -f "${CA_CERT}" ]; then
    echo "Error: CA key ('${CA_KEY}') or certificate ('${CA_CERT}') not found." >&2
    echo "Please run '01-generate-ca.sh' first." >&2
    exit 1
fi

# Generate server private key
echo "Generating server private key for '${DOMAIN}'..."
openssl genpkey -algorithm RSA -out "${KEY_FILE}"

# Create Certificate Signing Request (CSR)
echo "Creating Certificate Signing Request (CSR)..."
openssl req -new -key "${KEY_FILE}" -out "${CSR_FILE}" \
    -subj "/C=US/ST=California/L=San Francisco/O=Development/CN=${DOMAIN}"

# Create a temporary extension file for Subject Alternative Name (SAN)
echo "Creating SAN extension file..."
cat > "${EXT_FILE}" <<-EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = ${DOMAIN}
EOF

# Sign the CSR with the CA, creating the server certificate
echo "Signing the server certificate with the CA..."
openssl x509 -req -in "${CSR_FILE}" \
    -CA "${CA_CERT}" \
    -CAkey "${CA_KEY}" \
    -CAcreateserial \
    -out "${CERT_FILE}" \
    -days "${DAYS_VALID}" \
    -sha256 \
    -extfile "${EXT_FILE}" \
    -passin pass:development

# Clean up temporary files
rm "${CSR_FILE}" "${EXT_FILE}"

echo
echo "Server certificate and key generated successfully for '${DOMAIN}':"
echo "  - Key: ${KEY_FILE}"
echo "  - Certificate: ${CERT_FILE}"
echo
echo "You can now use these files in your web server configuration."
