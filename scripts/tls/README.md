# TLS Certificate Generation for Development

This directory contains scripts to generate a self-signed Certificate Authority (CA) and server certificates for local development.

## Prerequisites

- [OpenSSL](https://www.openssl.org/) must be installed on your system.

## Usage

### 1. Generate the Certificate Authority (CA)

First, create your local CA. This only needs to be done once.

```bash
bash ./01-generate-ca.sh
```

This will create two files:
- `ca-key.pem`: The private key for your CA. **Keep this secure.**
- `ca.pem`: The public CA certificate.

You may need to trust the `ca.pem` file in your browser or operating system to avoid security warnings.

### 2. Generate Server Certificates

Next, generate a certificate for your development server.

```bash
# For localhost
bash ./02-generate-certs.sh localhost

# For a custom domain
bash ./02-generate-certs.sh myapp.dev
```

This will create two files:
- `server-key.pem`: The private key for your server.
- `server.pem`: The server certificate, signed by your CA.

You can now use `server.pem` and `server-key.pem` in your web server's TLS/SSL configuration.

### Hot-Reloading

The server is configured to watch for changes to `server.pem` and `server-key.pem`. If you regenerate them, the server will automatically reload the new certificate without a restart.
