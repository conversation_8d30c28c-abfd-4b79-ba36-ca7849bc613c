# AI Endpoints Test Scripts

This directory contains comprehensive test scripts for all AI endpoints in the resumatter application.

## Available Scripts

| Script | Purpose | Usage |
|--------|---------|-------|
| `test-all.sh` | **Run all tests** | `./scripts/test-all.sh [port]` |
| `test-health.sh` | Server health check | `./scripts/test-health.sh [port]` |
| `test-tailor.sh` | Resume tailoring endpoint | `./scripts/test-tailor.sh [port]` |
| `test-evaluate.sh` | Resume evaluation endpoint | `./scripts/test-evaluate.sh [port]` |
| `test-analyze.sh` | Job analysis endpoint | `./scripts/test-analyze.sh [port]` |
| `test-git-commit.sh` | Git commit generation | `./scripts/test-git-commit.sh [port]` |
| `test-tailor-evaluate.sh` | **End-to-end workflow** | `./scripts/test-tailor-evaluate.sh [port]` |

## Quick Start

### 1. Configure API Keys
The server requires an AI provider API key to function. The recommended way to provide it is through an environment variable. This method is simple and secure.

**Set the Environment Variable:**
```bash
export AI_GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
```
The application will automatically detect this variable. All other settings will use sensible defaults, making it easy to get started.

**(Optional) Using a Configuration File:**
If you need to customize other settings, you can still create a `config.yaml` file. The application will load it if it exists. Environment variables will override any values set in the file.

### 2. Start the Server
```bash
# Build the application
make build

# Start the server
# It will use the AI_GEMINI_API_KEY environment variable if no config file is found.
./build/resumatter serve
```

### 3. Run All Tests
```bash
# Test against default port 8780
./scripts/test-all.sh

# Test against custom port
./scripts/test-all.sh 8080
```

### 4. Run Individual Tests
```bash
# Test specific endpoints
./scripts/test-tailor.sh
./scripts/test-evaluate.sh
./scripts/test-analyze.sh
./scripts/test-git-commit.sh
```

## Test Coverage

### `/api/v1/ai/tailor`
- **Input**: Base resume + job description
- **Output**: Tailored resume + ATS analysis + job posting analysis
- **Test Data**: Uses `./scripts/examples/tech/01-*.txt`
- **Validates**: JSON structure, response time, AI analysis quality

### `/api/v1/ai/evaluate`
- **Input**: Base resume + tailored resume
- **Output**: Evaluation summary + findings list
- **Test Data**: Sample resumes with realistic differences
- **Validates**: Accuracy assessment, finding detection

### `/api/v1/ai/analyze`
- **Input**: Job description
- **Output**: Quality scores + recommendations
- **Test Data**: Tech job posting or custom file
- **Validates**: Scoring system, inclusivity analysis

### `/api/v1/ai/git-commit`
- **Input**: Recent commit history + git diff
- **Output**: Conventional commit message
- **Test Data**: Sample code changes and commit history
- **Validates**: Commit format, type detection, scope identification

### **End-to-End Workflow: Tailor → Evaluate**
- **Input**: Base resume + job description
- **Process**: 
  1. Tailor resume to job (get optimized version)
  2. Evaluate optimized resume vs original (check accuracy)
- **Output**: Complete workflow analysis with quality metrics
- **Test Data**: Uses `./scripts/examples/tech/01-*.txt`
- **Validates**: 
  - Resume optimization effectiveness
  - AI consistency between operations
  - End-to-end workflow performance
  - Quality assessment (ATS score + evaluation findings)

## Script Features

### Error Handling
- Server connectivity checks
- JSON validation
- HTTP status code verification
- Timeout protection (60s default)
- Graceful failure reporting

### Output Formatting
- Colored output for better readability
- Response time and size metrics
- Structured test summaries
- Detailed error messages

### Data Sources
- Real resume and job data from `./scripts/examples/`
- Realistic git diffs and commit histories
- Sample data when files are missing

## Customization

### Using Custom Test Data

**For job analysis:**
```bash
./scripts/test-analyze.sh 8780 ./path/to/your/job.txt
```

**For tailor endpoint:**
Edit the script to point to your own resume and job files:
```bash
RESUME_FILE="./path/to/your/resume.txt"
JOB_FILE="./path/to/your/job.txt"
```

### Changing Timeouts
Edit the `--max-time` parameter in any script:
```bash
curl ... --max-time 120  # 2 minutes timeout
```

### Adding Custom Headers
Add authentication or other headers:
```bash
curl ... -H "Authorization: Bearer your-token"
```

## Expected Results

### Successful Test Output
```
Test completed successfully!
HTTP Status: 200
Response Time: 4.5s
Response Size: 2048 bytes
```

### Performance Benchmarks
- **Tailor**: 3-6 seconds (complex AI analysis)
- **Evaluate**: 2-4 seconds (comparison analysis)
- **Analyze**: 3-5 seconds (job quality assessment)
- **Git-commit**: 2-3 seconds (commit generation)

## Troubleshooting

### Server Not Responding
```bash
# Check if server is running
curl http://localhost:8780/health

# Check server logs
./build/resumatter serve --verbose
```

### Invalid JSON Response
- Verify AI provider API keys are configured
- Check server logs for AI client errors
- Ensure operations are defined in config

### Timeout Issues
- Increase timeout in scripts
- Check network connectivity
- Verify AI provider service status

## Debugging

### Verbose Mode
Add `-v` flag to curl commands for detailed request/response info:
```bash
curl -v -X POST http://localhost:8780/api/v1/ai/tailor ...
```

### Raw Response
Remove `| jq '.'` to see raw JSON response:
```bash
curl ... | cat  # Show raw response
```

### Test Specific Scenarios
Modify test data in scripts to test edge cases:
- Empty inputs
- Very long text
- Special characters
- Invalid JSON

## Contributing

To add new test scripts:
1. Follow the naming pattern: `test-{endpoint}.sh`
2. Include error handling and metrics
3. Add colored output for consistency
4. Update this README
5. Add the test to `test-all.sh`

## Integration

These scripts can be integrated into:
- **CI/CD pipelines** for automated testing
- **Development workflows** for local validation
- **Monitoring systems** for health checks
- **Load testing** with parallel execution
