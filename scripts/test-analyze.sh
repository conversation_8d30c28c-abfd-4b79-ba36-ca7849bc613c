#!/bin/bash

# Test script for /api/v1/ai/analyze endpoint
# Usage: ./scripts/test-analyze.sh [server_port] [job_file]

set -e

SERVER_PORT=${1:-8780}
JOB_FILE=${2:-'./scripts/examples/tech/01-job.txt'}
BASE_URL="http://localhost:${SERVER_PORT}"
ENDPOINT="${BASE_URL}/api/v1/ai/analyze"

echo "=== Testing Job Analysis Endpoint ==="
echo "Endpoint: ${ENDPOINT}"
echo "Job file: ${JOB_FILE}"
echo "Timestamp: $(date)"
echo ""

# Load job description
if [[ -f "$JOB_FILE" ]]; then
    echo "Loading job description from file..."
    JOB_CONTENT=$(cat "$JOB_FILE")
    echo "Job description loaded ($(echo "$JOB_CONTENT" | wc -c) characters)"
else
    echo "Using sample job description..."
    JOB_CONTENT="Senior Backend Engineer - Cloud Infrastructure

Company: CloudTech Solutions
Location: Remote
Salary: \$120,000 - \$160,000

About the Role:
We are seeking a Senior Backend Engineer to join our cloud infrastructure team. You will be responsible for designing and implementing scalable backend services that power our cloud platform used by thousands of customers worldwide.

Key Responsibilities:
• Design and develop high-performance backend services using Go
• Build and maintain microservices architecture on AWS
• Implement robust APIs with proper authentication and rate limiting
• Optimize database performance and design efficient data models
• Collaborate with DevOps team on CI/CD pipelines and deployment strategies

Required Qualifications:
• 4+ years of experience in backend development
• Strong proficiency in Go programming language
• Experience with microservices architecture and containerization (Docker/Kubernetes)
• Solid understanding of RESTful API design principles
• Experience with cloud platforms, preferably AWS
• Knowledge of database systems (PostgreSQL, Redis)

What We Offer:
• Competitive salary and equity package
• Comprehensive health, dental, and vision insurance
• Flexible work arrangements and unlimited PTO
• Professional development budget"
    echo "Sample job description prepared ($(echo "$JOB_CONTENT" | wc -c) characters)"
fi

echo ""

# Create JSON payload
echo "Creating JSON payload..."
cat > /tmp/analyze_payload.json << EOF
{
  "job_description": $(echo "$JOB_CONTENT" | jq -R -s .)
}
EOF

echo "Payload created ($(stat -c%s /tmp/analyze_payload.json) bytes)"
echo ""

# Make the request
echo "Making request to analyze endpoint..."
echo "Timeout: 60 seconds"
echo ""

START_TIME=$(date +%s)

RESPONSE=$(curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @/tmp/analyze_payload.json \
  --max-time 60 \
  --silent \
  --show-error \
  --write-out "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\nResponse Size: %{size_download} bytes\n")

echo "$RESPONSE" | head -n -4 | jq '.' 2>/dev/null || {
    echo "Request failed or invalid JSON response"
    echo "Raw response:"
    echo "$RESPONSE"
    exit 1
}

# Extract and display metrics
echo "$RESPONSE" | tail -n 4

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "Test completed successfully!"
echo "Total duration: ${DURATION} seconds"

# Show analysis summary
echo ""
echo "Job Analysis Summary:"
QUALITY_SCORE=$(echo "$RESPONSE" | head -n -4 | jq '.data.job_quality_score // 0' 2>/dev/null)
RECOMMENDATIONS_COUNT=$(echo "$RESPONSE" | head -n -4 | jq '.data.recommendations | length' 2>/dev/null || echo "0")
CLARITY_SCORE=$(echo "$RESPONSE" | head -n -4 | jq '.data.clarity.score // 0' 2>/dev/null)
INCLUSIVITY_SCORE=$(echo "$RESPONSE" | head -n -4 | jq '.data.inclusivity.score // 0' 2>/dev/null)

echo "• Overall Quality Score: $QUALITY_SCORE/100"
echo "• Clarity Score: $CLARITY_SCORE/100"
echo "• Inclusivity Score: $INCLUSIVITY_SCORE/100"
echo "• Recommendations: $RECOMMENDATIONS_COUNT items"

# Cleanup
rm -f /tmp/analyze_payload.json

echo ""
echo "=== Test Summary ==="
echo "• Endpoint: POST /api/v1/ai/analyze"
echo "• Status: PASSED"
echo "• Purpose: Analyze job posting quality and inclusivity"
echo "• Response: Comprehensive analysis with scores and recommendations"
