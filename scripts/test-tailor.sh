#!/bin/bash

# Test script for /api/v1/ai/tailor endpoint
# Usage: ./scripts/test-tailor.sh [server_port]

set -e

SERVER_PORT=${1:-8780}
BASE_URL="http://localhost:${SERVER_PORT}"
ENDPOINT="${BASE_URL}/api/v1/ai/tailor"

echo "=== Testing Resume Tailor Endpoint ==="
echo "Endpoint: ${ENDPOINT}"
echo "Timestamp: $(date)"
echo ""

# Test with tech resume and job description
echo "Loading test data from scripts/examples/tech/..."
RESUME_FILE="./scripts/examples/tech/01-resume.txt"
JOB_FILE="./scripts/examples/tech/01-job.txt"

if [[ ! -f "$RESUME_FILE" || ! -f "$JOB_FILE" ]]; then
    echo "Error: Test files not found!"
    echo "Expected: $RESUME_FILE and $JOB_FILE"
    exit 1
fi

RESUME_CONTENT=$(cat "$RESUME_FILE")
JOB_CONTENT=$(cat "$JOB_FILE")

echo "Test files loaded successfully"
echo "Resume length: $(echo "$RESUME_CONTENT" | wc -c) characters"
echo "Job description length: $(echo "$JOB_CONTENT" | wc -c) characters"
echo ""

# Create JSON payload
echo "Creating JSON payload..."
cat > /tmp/tailor_payload.json << EOF
{
  "base_resume": $(echo "$RESUME_CONTENT" | jq -R -s .),
  "job_description": $(echo "$JOB_CONTENT" | jq -R -s .)
}
EOF

echo "Payload created ($(stat -c%s /tmp/tailor_payload.json) bytes)"
echo ""

# Make the request
echo "Making request to tailor endpoint..."
echo "Timeout: 60 seconds"
echo ""

START_TIME=$(date +%s)

RESPONSE=$(curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @/tmp/tailor_payload.json \
  --max-time 60 \
  --silent \
  --show-error \
  --write-out "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\nResponse Size: %{size_download} bytes\n")

echo "$RESPONSE" | head -n -4 | jq '.' 2>/dev/null || {
    echo "Request failed or invalid JSON response"
    echo "Raw response:"
    echo "$RESPONSE"
    exit 1
}

# Extract and display metrics
echo "$RESPONSE" | tail -n 4

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "Test completed successfully!"
echo "Total duration: ${DURATION} seconds"

# Show tailor results summary
echo ""
echo "Tailor Results Summary:"
ATS_SCORE=$(echo "$RESPONSE" | head -n -4 | jq -r '.data.ats_analysis.score // "N/A"' 2>/dev/null)
TAILORED_LENGTH=$(echo "$RESPONSE" | head -n -4 | jq -r '.data.tailored_resume | length // 0' 2>/dev/null)
echo "• ATS Score: ${ATS_SCORE}/100"
echo "• Tailored resume length: ${TAILORED_LENGTH} characters"
echo "• Processing time: ${DURATION} seconds"

# Cleanup
rm -f /tmp/tailor_payload.json

echo ""
echo "=== Test Summary ==="
echo "• Endpoint: POST /api/v1/ai/tailor"
echo "• Status: PASSED"
echo "• Data source: Tech resume + job description"
echo "• Response: Structured JSON with tailored resume and analysis"
