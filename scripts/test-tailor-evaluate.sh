#!/bin/bash

# Test script for chained /api/v1/ai/tailor -> /api/v1/ai/evaluate operations
# This script tests the complete workflow: tailor a resume, then evaluate the result
# Usage: ./scripts/test-tailor-evaluate.sh [server_port] [resume_file] [job_file]

set -e

SERVER_PORT=${1:-8780}
RESUME_FILE=${2:-'./scripts/examples/tech/01-resume.txt'}
JOB_FILE=${3:-'./scripts/examples/tech/01-job.txt'}
BASE_URL="http://localhost:${SERVER_PORT}"
TAILOR_ENDPOINT="${BASE_URL}/api/v1/ai/tailor"
EVALUATE_ENDPOINT="${BASE_URL}/api/v1/ai/evaluate"

echo "=== Testing Tailor -> Evaluate Workflow ==="
echo "Tailor Endpoint: ${TAILOR_ENDPOINT}"
echo "Evaluate Endpoint: ${EVALUATE_ENDPOINT}"
echo "Resume file: ${RESUME_FILE}"
echo "Job file: ${JOB_FILE}"
echo "Timestamp: $(date)"
echo ""

# Load test data
echo "Loading test data..."
if [[ ! -f "$RESUME_FILE" || ! -f "$JOB_FILE" ]]; then
    echo "Error: Test files not found!"
    echo "Expected: $RESUME_FILE and $JOB_FILE"
    exit 1
fi

BASE_RESUME=$(cat "$RESUME_FILE")
JOB_DESCRIPTION=$(cat "$JOB_FILE")

echo "Test files loaded successfully"
echo "Base resume length: $(echo "$BASE_RESUME" | wc -c) characters"
echo "Job description length: $(echo "$JOB_DESCRIPTION" | wc -c) characters"
echo ""

# Step 1: Tailor the resume
echo "STEP 1: Tailoring resume to job description"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Create tailor payload
cat > /tmp/tailor_payload.json << EOF
{
  "base_resume": $(echo "$BASE_RESUME" | jq -R -s .),
  "job_description": $(echo "$JOB_DESCRIPTION" | jq -R -s .)
}
EOF

echo "Making tailor request..."
START_TIME_TAILOR=$(date +%s)

TAILOR_RESPONSE=$(curl -X POST "$TAILOR_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @/tmp/tailor_payload.json \
  --max-time 60 \
  --silent \
  --show-error \
  --write-out "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\nResponse Size: %{size_download} bytes\n")

END_TIME_TAILOR=$(date +%s)
DURATION_TAILOR=$((END_TIME_TAILOR - START_TIME_TAILOR))

# Extract tailored resume from response
TAILOR_JSON=$(echo "$TAILOR_RESPONSE" | head -n -4)
TAILOR_METRICS=$(echo "$TAILOR_RESPONSE" | tail -n 4)

# Validate tailor response
if ! echo "$TAILOR_JSON" | jq '.' > /dev/null 2>&1; then
    echo "Tailor request failed - invalid JSON response"
    echo "Raw response:"
    echo "$TAILOR_RESPONSE"
    exit 1
fi

TAILORED_RESUME=$(echo "$TAILOR_JSON" | jq -r '.data.tailored_resume // empty')
TAILOR_STATUS=$(echo "$TAILOR_JSON" | jq -r '.status // "unknown"')
ATS_SCORE=$(echo "$TAILOR_JSON" | jq -r '.data.ats_analysis.score // 0')

if [[ -z "$TAILORED_RESUME" || "$TAILOR_STATUS" != "success" ]]; then
    echo "Tailor operation failed"
    echo "$TAILOR_JSON" | jq '.'
    exit 1
fi

echo "Tailor operation completed successfully!"
echo "$TAILOR_METRICS"
echo ""
echo "Tailor Results Summary:"
echo "• Status: $TAILOR_STATUS"
echo "• ATS Score: $ATS_SCORE/100"
echo "• Tailored resume length: $(echo "$TAILORED_RESUME" | wc -c) characters"
echo "• Processing time: ${DURATION_TAILOR} seconds"
echo ""

# Step 2: Evaluate the tailored resume
echo "STEP 2: Evaluating tailored resume against original"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Create evaluate payload
cat > /tmp/evaluate_payload.json << EOF
{
  "base_resume": $(echo "$BASE_RESUME" | jq -R -s .),
  "tailored_resume": $(echo "$TAILORED_RESUME" | jq -R -s .)
}
EOF

echo "Making evaluate request..."
START_TIME_EVALUATE=$(date +%s)

EVALUATE_RESPONSE=$(curl -X POST "$EVALUATE_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @/tmp/evaluate_payload.json \
  --max-time 60 \
  --silent \
  --show-error \
  --write-out "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\nResponse Size: %{size_download} bytes\n")

END_TIME_EVALUATE=$(date +%s)
DURATION_EVALUATE=$((END_TIME_EVALUATE - START_TIME_EVALUATE))

# Extract evaluation results
EVALUATE_JSON=$(echo "$EVALUATE_RESPONSE" | head -n -4)
EVALUATE_METRICS=$(echo "$EVALUATE_RESPONSE" | tail -n 4)

# Validate evaluate response
if ! echo "$EVALUATE_JSON" | jq '.' > /dev/null 2>&1; then
    echo "Evaluate request failed - invalid JSON response"
    echo "Raw response:"
    echo "$EVALUATE_RESPONSE"
    exit 1
fi

EVALUATE_STATUS=$(echo "$EVALUATE_JSON" | jq -r '.status // "unknown"')
EVALUATE_SUMMARY=$(echo "$EVALUATE_JSON" | jq -r '.data.summary // "No summary available"')
FINDINGS_COUNT=$(echo "$EVALUATE_JSON" | jq '.data.findings | length' 2>/dev/null || echo "0")

if [[ "$EVALUATE_STATUS" != "success" ]]; then
    echo "Evaluate operation failed"
    echo "$EVALUATE_JSON" | jq '.'
    exit 1
fi

echo "Evaluate operation completed successfully!"
echo "$EVALUATE_METRICS"
echo ""

# Step 3: Display comprehensive results
echo "COMPREHENSIVE WORKFLOW RESULTS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

echo "TAILOR OPERATION RESULTS:"
echo "• ATS Score: $ATS_SCORE/100"
echo "• Processing Time: ${DURATION_TAILOR}s"
echo "• Resume Length Change: $(echo "$BASE_RESUME" | wc -c) → $(echo "$TAILORED_RESUME" | wc -c) characters"

# Show ATS analysis
echo ""
echo "ATS Analysis:"
ATS_STRENGTHS=$(echo "$TAILOR_JSON" | jq -r '.data.ats_analysis.strengths // "N/A"')
ATS_WEAKNESSES=$(echo "$TAILOR_JSON" | jq -r '.data.ats_analysis.weaknesses // "N/A"')
echo "  Strengths: ${ATS_STRENGTHS:0:100}..."
echo "  Weaknesses: ${ATS_WEAKNESSES:0:100}..."

echo ""
echo "EVALUATE OPERATION RESULTS:"
echo "• Processing Time: ${DURATION_EVALUATE}s"
echo "• Findings Detected: $FINDINGS_COUNT"
echo "• Summary Length: $(echo "$EVALUATE_SUMMARY" | wc -c) characters"

echo ""
echo "Evaluation Summary:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "$EVALUATE_SUMMARY"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Show findings if any
if [[ "$FINDINGS_COUNT" -gt 0 ]]; then
    echo ""
    echo "Evaluation Findings:"
    echo "$EVALUATE_JSON" | jq -r '.data.findings[] | "• \(.type): \(.description)"' 2>/dev/null || echo "Could not parse findings"
fi

# Calculate total workflow time
TOTAL_TIME=$((DURATION_TAILOR + DURATION_EVALUATE))

echo ""
echo "PERFORMANCE SUMMARY:"
echo "• Tailor Operation: ${DURATION_TAILOR}s"
echo "• Evaluate Operation: ${DURATION_EVALUATE}s"
echo "• Total Workflow Time: ${TOTAL_TIME}s"

# Quality assessment
echo ""
echo "WORKFLOW QUALITY ASSESSMENT:"
if [[ "$ATS_SCORE" -ge 80 ]]; then
    echo "• ATS Score: EXCELLENT ($ATS_SCORE/100)"
elif [[ "$ATS_SCORE" -ge 70 ]]; then
    echo "• ATS Score: GOOD ($ATS_SCORE/100)"
else
    echo "• ATS Score: NEEDS IMPROVEMENT ($ATS_SCORE/100)"
fi

if [[ "$FINDINGS_COUNT" -eq 0 ]]; then
    echo "• Evaluation: NO ISSUES DETECTED"
elif [[ "$FINDINGS_COUNT" -le 2 ]]; then
    echo "• Evaluation: MINOR ISSUES ($FINDINGS_COUNT findings)"
else
    echo "• Evaluation: MULTIPLE ISSUES ($FINDINGS_COUNT findings)"
fi

if [[ "$TOTAL_TIME" -le 10 ]]; then
    echo "• Performance: FAST (${TOTAL_TIME}s total)"
elif [[ "$TOTAL_TIME" -le 15 ]]; then
    echo "• Performance: ACCEPTABLE (${TOTAL_TIME}s total)"
else
    echo "• Performance: SLOW (${TOTAL_TIME}s total)"
fi

# Cleanup
rm -f /tmp/tailor_payload.json /tmp/evaluate_payload.json

echo ""
echo "Tailor -> Evaluate workflow test completed successfully!"
echo ""
echo "=== Test Summary ==="
echo "• Workflow: Tailor → Evaluate"
echo "• Status: PASSED"
echo "• Purpose: Test AI resume optimization and accuracy validation"
echo "• Result: Complete end-to-end AI workflow validation"
