DevOps Engineer - Infrastructure Automation

Company: CloudFirst Technologies
Location: Austin, TX / Remote
Salary: $110,000 - $150,000

Job Description:
We're seeking a DevOps Engineer to help scale our infrastructure and improve our deployment processes. You'll work with development teams to automate workflows and ensure reliable, secure deployments.

Responsibilities:
• Design and maintain CI/CD pipelines using Jenkins or GitLab CI
• Manage cloud infrastructure on AWS (EC2, RDS, S3, Lambda)
• Implement Infrastructure as Code using Terraform
• Monitor system performance with Prometheus and Grafana
• Ensure security best practices across all environments
• Troubleshoot production issues and optimize performance
• Collaborate with development teams on deployment strategies

Required Skills:
• 3+ years experience in DevOps or Site Reliability Engineering
• Strong knowledge of Linux/Unix systems
• Experience with containerization (Docker, Kubernetes)
• Proficiency with at least one cloud platform (AWS preferred)
• Scripting skills in Python, Bash, or similar
• Experience with configuration management tools
• Understanding of networking and security principles

Preferred Qualifications:
• AWS certifications
• Experience with monitoring and logging tools
• Knowledge of database administration
• Familiarity with Agile methodologies
• Experience with incident response and on-call duties

What We Offer:
• Competitive salary and stock options
• Remote work flexibility
• Professional development opportunities
• Health, dental, and vision insurance
• 401(k) with company matching
• Unlimited PTO policy