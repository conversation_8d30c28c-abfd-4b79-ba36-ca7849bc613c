<PERSON> Chen
Full Stack Developer
<EMAIL> | (415) 555-0123 | GitHub: github.com/sarahchen

SUMMARY
Creative full stack developer with 3 years of experience building web applications. Passionate about creating user-friendly interfaces and scalable backend systems. Experience with modern JavaScript frameworks and cloud technologies.

EXPERIENCE

Frontend Developer | WebCorp | 2022 - Present
• Built interactive dashboards using React and Redux
• Improved page load times by 25% through code optimization
• Collaborated with UX designers to implement responsive designs
• Maintained legacy jQuery applications

Junior Full Stack Developer | StartupHub | 2021 - 2022
• Developed REST APIs using Node.js and Express
• Created database schemas and queries in PostgreSQL
• Implemented user authentication and authorization
• Worked in agile development environment

Web Development Intern | TechSolutions | 2021 (3 months)
• Assisted with frontend development using HTML, CSS, JavaScript
• Fixed bugs in existing web applications
• Learned about software development lifecycle

EDUCATION
Bachelor of Science in Information Technology | UC Berkeley | 2021

PROJECTS
• Personal Portfolio Website - Built with React and deployed on Netlify
• Task Management App - Full stack application with Node.js backend
• Weather Dashboard - JavaScript app consuming external APIs

SKILLS
• Frontend: React, JavaScript, HTML, CSS, Redux
• Backend: Node.js, Express, Python
• Databases: PostgreSQL, MongoDB
• Tools: Git, npm, Webpack, VS Code