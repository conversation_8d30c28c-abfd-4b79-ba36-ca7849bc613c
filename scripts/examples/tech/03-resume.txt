<PERSON>
Systems Administrator
<EMAIL> | (512) 555-7890 | LinkedIn: linkedin.com/in/mikerodriguez

PROFESSIONAL SUMMARY
Dedicated systems administrator with 4 years of experience managing Linux servers and cloud infrastructure. Strong background in automation and monitoring. Looking to transition into DevOps role.

WORK EXPERIENCE

Systems Administrator | DataCenter Pro | 2021 - Present
• Manage 50+ Linux servers running CentOS and Ubuntu
• Automated server provisioning using Ansible playbooks
• Implemented backup strategies and disaster recovery procedures
• Monitor system performance using Nagios and custom scripts
• Reduced server downtime by 30% through proactive monitoring

IT Support Specialist | TechSupport Inc | 2020 - 2021
• Provided technical support for Windows and Mac environments
• Troubleshot network connectivity issues
• Managed user accounts in Active Directory
• Created documentation for common procedures

Network Technician | LocalISP | 2019 - 2020
• Installed and configured network equipment
• Performed cable management and infrastructure maintenance
• Assisted with customer technical issues
• Learned about routing and switching protocols

EDUCATION
Associate Degree in Computer Science | Austin Community College | 2019

CERTIFICATIONS
• CompTIA Linux+ (2020)
• AWS Cloud Practitioner (2022)

TECHNICAL SKILLS
• Operating Systems: Linux (CentOS, Ubuntu), Windows Server
• Scripting: Bash, Python (basic)
• Tools: Ansible, Git, Docker (learning)
• Cloud: AWS (EC2, S3), basic Terraform
• Monitoring: Nagios, basic Prometheus
• Networking: TCP/IP, DNS, DHCP, VPN