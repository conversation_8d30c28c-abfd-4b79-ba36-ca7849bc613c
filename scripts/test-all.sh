#!/bin/bash

# Comprehensive test runner for all AI endpoints
# Usage: ./scripts/test-all.sh [server_port]

set -e

SERVER_PORT=${1:-8780}
BASE_URL="http://localhost:${SERVER_PORT}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                           AI ENDPOINTS COMPREHENSIVE TEST SUITE                  ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${BLUE}Server URL: ${BASE_URL}${NC}"
echo -e "${BLUE}Test started: $(date)${NC}"
echo -e "${BLUE}Script directory: ${SCRIPT_DIR}${NC}"
echo ""

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_script="$2"
    local description="$3"
    
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║ TEST: ${test_name}${NC}"
    echo -e "${PURPLE}║ ${description}${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ -f "$test_script" ]]; then
        if bash "$test_script" "$SERVER_PORT"; then
            echo ""
            echo -e "${GREEN} ${test_name} PASSED${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            echo ""
            echo -e "${RED} ${test_name} FAILED${NC}"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        echo -e "${RED} Test script not found: $test_script${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    echo ""
    echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo ""
}

# Check if server is running
echo -e "${BLUE}Checking server health...${NC}"
if curl -s "${BASE_URL}/health" > /dev/null; then
    echo -e "${GREEN}Server is running and responding${NC}"
else
    echo -e "${RED}Server is not responding at ${BASE_URL}${NC}"
    echo -e "${YELLOW}Make sure the server is running with: ./build/resumatter serve${NC}"
    exit 1
fi
echo ""

# Run all tests
run_test "TAILOR" "${SCRIPT_DIR}/test-tailor.sh" "Resume tailoring with ATS analysis"
run_test "EVALUATE" "${SCRIPT_DIR}/test-evaluate.sh" "Resume evaluation for accuracy"
run_test "ANALYZE" "${SCRIPT_DIR}/test-analyze.sh" "Job posting quality analysis"
run_test "GIT-COMMIT" "${SCRIPT_DIR}/test-git-commit.sh" "Git commit message generation"
run_test "TAILOR-EVALUATE" "${SCRIPT_DIR}/test-tailor-evaluate.sh" "End-to-end tailor → evaluate workflow"

# Test summary
echo -e "${CYAN}╔══════════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                                  TEST RESULTS SUMMARY                            ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${BLUE}Test completed: $(date)${NC}"
echo -e "${BLUE}Total tests: ${TOTAL_TESTS}${NC}"
echo -e "${GREEN}Tests passed: ${TESTS_PASSED}${NC}"
echo -e "${RED}Tests failed: ${TESTS_FAILED}${NC}"

if [[ $TESTS_FAILED -eq 0 ]]; then
    echo ""
    echo -e "${GREEN}ALL TESTS PASSED!${NC}"
    echo -e "${GREEN}Your AI endpoints are working perfectly!${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}Some tests failed. Please check the output above for details.${NC}"
    exit 1
fi
