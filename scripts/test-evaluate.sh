#!/bin/bash

# Test script for /api/v1/ai/evaluate endpoint
# Usage: ./scripts/test-evaluate.sh [server_port]

set -e

SERVER_PORT=${1:-8780}
BASE_URL="http://localhost:${SERVER_PORT}"
ENDPOINT="${BASE_URL}/api/v1/ai/evaluate"

echo "=== Testing Resume Evaluate Endpoint ==="
echo "Endpoint: ${ENDPOINT}"
echo "Timestamp: $(date)"
echo ""

# Sample base resume
BASE_RESUME="John <PERSON>e
Software Engineer
<EMAIL> | (555) 123-4567

EXPERIENCE
Senior Software Engineer | TechCorp Inc. | 2022 - Present
• Developed microservices using Go and Docker
• Built RESTful APIs serving 10,000+ users
• Implemented automated testing strategies

Software Engineer | StartupXYZ | 2020 - 2022
• Created web applications using React and Node.js
• Optimized database queries for 30% performance improvement
• Worked with PostgreSQL and MongoDB

SKILLS
• Programming: Go, JavaScript, Python
• Tools: Docker, Git, AWS
• Databases: PostgreSQL, MongoDB"

# Sample tailored resume (enhanced version)
TAILORED_RESUME="<PERSON>
Senior Software Engineer - Cloud Infrastructure
<EMAIL> | (555) 123-4567

SUMMARY
Experienced software engineer with 5+ years in backend development and cloud infrastructure. 
Expert in Go programming, microservices architecture, and scalable system design.

EXPERIENCE
Senior Software Engineer | TechCorp Inc. | 2022 - Present
• Architected and developed high-performance microservices using Go and Docker
• Built robust RESTful APIs serving 10,000+ daily active users with 99.9% uptime
• Implemented comprehensive automated testing strategies reducing bug reports by 40%
• Led cloud migration initiatives using AWS services

Software Engineer | StartupXYZ | 2020 - 2022
• Developed scalable web applications using React and Node.js
• Optimized database queries and indexing strategies resulting in 30% performance improvement
• Collaborated with DevOps team on CI/CD pipeline implementation
• Managed PostgreSQL and MongoDB databases in production environments

TECHNICAL SKILLS
• Programming Languages: Go (Expert), JavaScript, Python
• Cloud Platforms: AWS (EC2, S3, RDS, Lambda)
• Containerization: Docker, Kubernetes
• Databases: PostgreSQL, MongoDB, Redis
• Tools: Git, Jenkins, Terraform"

echo "Sample data prepared:"
echo "Base resume length: $(echo "$BASE_RESUME" | wc -c) characters"
echo "Tailored resume length: $(echo "$TAILORED_RESUME" | wc -c) characters"
echo ""

# Create JSON payload
echo "Creating JSON payload..."
cat > /tmp/evaluate_payload.json << EOF
{
  "base_resume": $(echo "$BASE_RESUME" | jq -R -s .),
  "tailored_resume": $(echo "$TAILORED_RESUME" | jq -R -s .)
}
EOF

echo "Payload created ($(stat -c%s /tmp/evaluate_payload.json) bytes)"
echo ""

# Make the request
echo "Making request to evaluate endpoint..."
echo "Timeout: 60 seconds"
echo ""

START_TIME=$(date +%s)

RESPONSE=$(curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @/tmp/evaluate_payload.json \
  --max-time 60 \
  --silent \
  --show-error \
  --write-out "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\nResponse Size: %{size_download} bytes\n")

echo "$RESPONSE" | head -n -4 | jq '.' 2>/dev/null || {
    echo "Request failed or invalid JSON response"
    echo "Raw response:"
    echo "$RESPONSE"
    exit 1
}

# Extract and display metrics
echo "$RESPONSE" | tail -n 4

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "Test completed successfully!"
echo "Total duration: ${DURATION} seconds"

# Show summary of findings
echo ""
echo "Evaluation Summary:"
SUMMARY=$(echo "$RESPONSE" | head -n -4 | jq -r '.data.summary // "No summary available"' 2>/dev/null)
FINDINGS_COUNT=$(echo "$RESPONSE" | head -n -4 | jq '.data.findings | length' 2>/dev/null || echo "0")

echo "• Summary: ${SUMMARY:0:100}..."
echo "• Findings detected: $FINDINGS_COUNT"

# Cleanup
rm -f /tmp/evaluate_payload.json

echo ""
echo "=== Test Summary ==="
echo "• Endpoint: POST /api/v1/ai/evaluate"
echo "• Status: PASSED"
echo "• Purpose: Compare base vs tailored resume for accuracy"
echo "• Response: Summary + detailed findings list"
