package config

import (
	"fmt"

	"github.com/ajiwo/resumatter/util"
)

// OpenAIConfig holds OpenAI specific configuration.
// This struct is used to validate the provider_config section when the provider is "openai".
//
// YAML path: ai.presets.<preset-name>.provider_config
// Used when: ai.presets.<preset-name>.provider == "openai"
type OpenAIConfig struct {
	APIKey         string `mapstructure:"api_key"`
	OrganizationID string `mapstructure:"organization_id"`
	BaseURL        string `mapstructure:"base_url"`
	ProjectID      string `mapstructure:"project_id"`
}

func validateOpenAIConfig(presetName string, preset *PresetConfig, fullConfig *Config) error {
	var openaiConfig OpenAIConfig

	// Convert map[string]any to OpenAIConfig
	if preset.ProviderConfig != nil {
		if apiKey, ok := preset.ProviderConfig["api_key"].(string); ok {
			openaiConfig.APIKey = apiKey
		}
		if orgID, ok := preset.ProviderConfig["organization_id"].(string); ok {
			openaiConfig.OrganizationID = orgID
		}
		if baseURL, ok := preset.ProviderConfig["base_url"].(string); ok {
			openaiConfig.BaseURL = baseURL
		}
		if projectID, ok := preset.ProviderConfig["project_id"].(string); ok {
			openaiConfig.ProjectID = projectID
		}
	}

	// If no API key in provider_config, check top-level ai.openai_api_key field
	if openaiConfig.APIKey == "" && fullConfig != nil {
		openaiConfig.APIKey = fullConfig.AI.OpenAIAPIKey
	}

	// API key is typically required for OpenAI
	if openaiConfig.APIKey == "" {
		return fmt.Errorf("preset '%s': API key is required for OpenAI backend", presetName)
	}

	// BaseURL is optional, but if provided, it should be a valid URL (basic check)
	// For now, we'll just check if it's not empty. More robust URL validation can be added later.
	if openaiConfig.BaseURL != "" && !util.IsValidURL(openaiConfig.BaseURL) {
		return fmt.Errorf("preset '%s': invalid base_url format for OpenAI backend", presetName)
	}

	return nil
}
