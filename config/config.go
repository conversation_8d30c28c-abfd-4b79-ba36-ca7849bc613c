package config

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

const (
	// MaxPresets defines the maximum number of AI presets allowed
	MaxPresets = 24
	// MaxCallbacks defines the maximum number of config change callbacks
	MaxCallbacks = 16
)

// ConfigChangeCallback represents a callback function for config changes
type ConfigChangeCallback func(event fsnotify.Event, oldConfig, newConfig *Config)

// Config represents the complete application configuration
type Config struct {
	AI            AIConfig            `mapstructure:"ai" yaml:"ai"`
	Server        ServerConfig        `mapstructure:"server" yaml:"server"`
	TLS           TLSConfig           `mapstructure:"tls" yaml:"tls"`
	Auth          AuthConfig          `mapstructure:"auth" yaml:"auth"`
	RateLimit     RateLimitConfig     `mapstructure:"rate_limit" yaml:"rate_limit"`
	Logging       LoggingConfig       `mapstructure:"logging" yaml:"logging"`
	Observability ObservabilityConfig `mapstructure:"observability" yaml:"observability"`

	// Internal fields for config watching
	viper     *viper.Viper
	callbacks map[string]ConfigChangeCallback
	mu        sync.RWMutex
	watching  bool
}

// AIConfig represents AI provider configuration
type AIConfig struct {
	GeminiAPIKey  string                     `mapstructure:"gemini_api_key" yaml:"gemini_api_key"`
	GeminiModel   string                     `mapstructure:"gemini_model" yaml:"gemini_model"`
	OpenAIAPIKey  string                     `mapstructure:"openai_api_key" yaml:"openai_api_key"`
	OpenAIBaseURL string                     `mapstructure:"openai_base_url" yaml:"openai_base_url"`
	OpenAIModel   string                     `mapstructure:"openai_model" yaml:"openai_model"`
	Presets       map[string]PresetConfig    `mapstructure:"presets" yaml:"presets"`
	DefaultPreset string                     `mapstructure:"default_preset" yaml:"default_preset"`
	Operations    map[string]OperationConfig `mapstructure:"operations" yaml:"operations"`
}

// PresetConfig represents a provider preset configuration
type PresetConfig struct {
	Provider       string               `mapstructure:"provider" yaml:"provider"`
	Model          string               `mapstructure:"model" yaml:"model"`
	Temperature    float64              `mapstructure:"temperature" yaml:"temperature"`
	MaxTokens      int                  `mapstructure:"max_tokens" yaml:"max_tokens"`
	Timeout        string               `mapstructure:"timeout" yaml:"timeout"`
	CircuitBreaker CircuitBreakerConfig `mapstructure:"circuit_breaker" yaml:"circuit_breaker"`
	ProviderConfig map[string]any       `mapstructure:"provider_config" yaml:"provider_config"`
}

// CircuitBreakerConfig represents circuit breaker configuration
type CircuitBreakerConfig struct {
	Enabled          bool    `mapstructure:"enabled" yaml:"enabled"`
	FailureThreshold float64 `mapstructure:"failure_threshold" yaml:"failure_threshold"`
	MinRequests      int     `mapstructure:"min_requests" yaml:"min_requests"`
	MaxRequests      int     `mapstructure:"max_requests" yaml:"max_requests"`
	Interval         string  `mapstructure:"interval" yaml:"interval"`
	Timeout          string  `mapstructure:"timeout" yaml:"timeout"`
}

// OperationConfig represents operation-specific configuration
type OperationConfig struct {
	Preset      string   `mapstructure:"preset" yaml:"preset"`
	Temperature *float64 `mapstructure:"temperature,omitempty" yaml:"temperature,omitempty"`
	MaxTokens   *int     `mapstructure:"max_tokens,omitempty" yaml:"max_tokens,omitempty"`
	Timeout     string   `mapstructure:"timeout,omitempty" yaml:"timeout,omitempty"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	Port        string     `mapstructure:"port" yaml:"port"`
	Environment string     `mapstructure:"environment" yaml:"environment"`
	CORS        CORSConfig `mapstructure:"cors" yaml:"cors"`
}

// CORSConfig represents CORS configuration
type CORSConfig struct {
	Enabled        bool     `mapstructure:"enabled" yaml:"enabled"`
	AllowedOrigins []string `mapstructure:"allowed_origins" yaml:"allowed_origins"`
	AllowedMethods []string `mapstructure:"allowed_methods" yaml:"allowed_methods"`
	AllowedHeaders []string `mapstructure:"allowed_headers" yaml:"allowed_headers"`
}

// TLSConfig represents TLS configuration
type TLSConfig struct {
	Mode         string   `mapstructure:"mode" yaml:"mode"`
	MinVersion   string   `mapstructure:"min_version" yaml:"min_version"`
	CipherSuites []string `mapstructure:"cipher_suites" yaml:"cipher_suites"`
	CertFile     string   `mapstructure:"cert_file" yaml:"cert_file"`
	KeyFile      string   `mapstructure:"key_file" yaml:"key_file"`
	CAFile       string   `mapstructure:"ca_file" yaml:"ca_file"`
}

// AuthConfig represents authentication configuration
type AuthConfig struct {
	Enabled bool     `mapstructure:"enabled" yaml:"enabled"`
	APIKeys []string `mapstructure:"api_keys" yaml:"api_keys"`
}

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	Enabled    bool                          `mapstructure:"enabled" yaml:"enabled"`
	Required   bool                          `mapstructure:"required" yaml:"required"` // If true, server fails to start if rate limiter cannot be initialized
	Backend    string                        `mapstructure:"backend" yaml:"backend"`
	Strategy   string                        `mapstructure:"strategy" yaml:"strategy"`
	KeyBy      string                        `mapstructure:"key_by" yaml:"key_by"`
	HeaderName string                        `mapstructure:"header_name" yaml:"header_name"`
	Defaults   RateLimitDefaults             `mapstructure:"defaults" yaml:"defaults"`
	Operations map[string]RateLimitOperation `mapstructure:"operations" yaml:"operations"`
	Redis      RedisConfig                   `mapstructure:"redis" yaml:"redis"`
}

// RateLimitDefaults represents default rate limit settings
type RateLimitDefaults struct {
	RequestsPerMinute int    `mapstructure:"requests_per_minute" yaml:"requests_per_minute"`
	RequestsPerHour   int    `mapstructure:"requests_per_hour" yaml:"requests_per_hour"`
	RequestsPerDay    int    `mapstructure:"requests_per_day" yaml:"requests_per_day"`
	BurstSize         int    `mapstructure:"burst_size" yaml:"burst_size"`
	Window            string `mapstructure:"window" yaml:"window"` // Duration of the fixed window (e.g., "1m", "1h")
}

// RateLimitOperation represents operation-specific rate limit settings
type RateLimitOperation struct {
	Enabled           bool   `mapstructure:"enabled" yaml:"enabled"`
	RequestsPerMinute *int   `mapstructure:"requests_per_minute" yaml:"requests_per_minute"`
	RequestsPerHour   *int   `mapstructure:"requests_per_hour" yaml:"requests_per_hour"`
	RequestsPerDay    *int   `mapstructure:"requests_per_day" yaml:"requests_per_day"`
	BurstSize         int    `mapstructure:"burst_size" yaml:"burst_size"`
	Window            string `mapstructure:"window" yaml:"window"` // Duration of the fixed window (e.g., "1m", "1h")
}

// RedisConfig represents Redis configuration
type RedisConfig struct {
	Address  string `mapstructure:"address" yaml:"address"`
	Password string `mapstructure:"password" yaml:"password"`
	DB       int    `mapstructure:"db" yaml:"db"`
	PoolSize int    `mapstructure:"pool_size" yaml:"pool_size"`
}

// LoggingConfig represents logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level" yaml:"level"`
	Format string `mapstructure:"format" yaml:"format"`
}

// ObservabilityConfig represents observability configuration
type ObservabilityConfig struct {
	ServiceName         string            `mapstructure:"service_name" yaml:"service_name"`
	ServiceVersion      string            `mapstructure:"service_version" yaml:"service_version"`
	Environment         string            `mapstructure:"environment" yaml:"environment"`
	TracingEnabled      bool              `mapstructure:"tracing_enabled" yaml:"tracing_enabled"`
	TraceExporter       string            `mapstructure:"trace_exporter" yaml:"trace_exporter"`
	TraceFilePath       string            `mapstructure:"trace_file_path" yaml:"trace_file_path"`
	MetricsEnabled      bool              `mapstructure:"metrics_enabled" yaml:"metrics_enabled"`
	MetricsExporter     string            `mapstructure:"metrics_exporter" yaml:"metrics_exporter"`
	MetricsFilePath     string            `mapstructure:"metrics_file_path" yaml:"metrics_file_path"`
	MetricsInterval     time.Duration     `mapstructure:"metrics_interval" yaml:"metrics_interval"`
	LoggingEnabled      bool              `mapstructure:"logging_enabled" yaml:"logging_enabled"`
	LogExporter         string            `mapstructure:"log_exporter" yaml:"log_exporter"`
	LogFilePath         string            `mapstructure:"log_file_path" yaml:"log_file_path"`
	OTLPTraceEndpoint   string            `mapstructure:"otlp_trace_endpoint" yaml:"otlp_trace_endpoint"`
	OTLPTraceProtocol   string            `mapstructure:"otlp_trace_protocol" yaml:"otlp_trace_protocol"`
	OTLPTraceURLPath    string            `mapstructure:"otlp_trace_url_path" yaml:"otlp_trace_url_path"`
	OTLPTraceHeaders    map[string]string `mapstructure:"otlp_trace_headers" yaml:"otlp_trace_headers"`
	OTLPTraceInsecure   bool              `mapstructure:"otlp_trace_insecure" yaml:"otlp_trace_insecure"`
	OTLPMetricsEndpoint string            `mapstructure:"otlp_metrics_endpoint" yaml:"otlp_metrics_endpoint"`
	OTLPMetricsProtocol string            `mapstructure:"otlp_metrics_protocol" yaml:"otlp_metrics_protocol"`
	OTLPMetricsURLPath  string            `mapstructure:"otlp_metrics_url_path" yaml:"otlp_metrics_url_path"`
	OTLPMetricsHeaders  map[string]string `mapstructure:"otlp_metrics_headers" yaml:"otlp_metrics_headers"`
	OTLPMetricsInsecure bool              `mapstructure:"otlp_metrics_insecure" yaml:"otlp_metrics_insecure"`
	OTLPLogEndpoint     string            `mapstructure:"otlp_log_endpoint" yaml:"otlp_log_endpoint"`
	OTLPLogProtocol     string            `mapstructure:"otlp_log_protocol" yaml:"otlp_log_protocol"`
	OTLPLogURLPath      string            `mapstructure:"otlp_log_url_path" yaml:"otlp_log_url_path"`
	OTLPLogHeaders      map[string]string `mapstructure:"otlp_log_headers" yaml:"otlp_log_headers"`
	OTLPLogInsecure     bool              `mapstructure:"otlp_log_insecure" yaml:"otlp_log_insecure"`
}

// Options represents configuration options for creating a new Config instance
type Options struct {
	// ConfigPath specifies the path to the configuration file
	ConfigPath string
	// ConfigName specifies the name of the config file (without extension)
	ConfigName string
	// ConfigType specifies the type of config file (yaml, json, etc.)
	ConfigType string
	// ConfigPaths specifies additional paths to search for config files
	ConfigPaths []string
	// EnvPrefix specifies the prefix for environment variables
	EnvPrefix string
	// SkipValidation skips configuration validation if true
	SkipValidation bool
	// Apply default values if true
	ApplyDefaults bool
	// Viper allows passing a custom viper instance
	Viper *viper.Viper
}

// New creates a new Config instance with the given options
func New(opts Options) (*Config, error) {
	v := opts.Viper
	if v == nil {
		v = viper.New()
	}

	if err := setupConfigFile(v, opts); err != nil {
		return nil, err
	}

	// Enable environment variable support
	v.AutomaticEnv()
	if opts.EnvPrefix != "" {
		v.SetEnvPrefix(opts.EnvPrefix)
	}
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "__"))

	if opts.ApplyDefaults {
		applyDefaultValues(v)
	}

	config, err := loadAndProcessConfig(v)
	if err != nil {
		return nil, err
	}

	// Apply defaults to existing presets before adding default preset
	if err := applyPresetDefaults(v, config); err != nil {
		return nil, err
	}

	if err := addDefaultPreset(v, config); err != nil {
		return nil, err
	}

	// Validate configuration unless skipped
	if !opts.SkipValidation {
		if err := ValidateConfig(config); err != nil {
			return nil, fmt.Errorf("config validation failed: %w", err)
		}
	}

	return config, nil
}

// setupConfigFile configures the config file settings for viper
func setupConfigFile(v *viper.Viper, opts Options) error {
	if opts.ConfigPath != "" {
		v.SetConfigFile(opts.ConfigPath)
		return nil
	}

	configName := opts.ConfigName
	if configName == "" {
		configName = "config"
	}
	v.SetConfigName(configName)

	configType := opts.ConfigType
	if configType == "" {
		configType = "yaml"
	}
	v.SetConfigType(configType)

	setupConfigPaths(v, opts.ConfigPaths)
	return nil
}

// setupConfigPaths adds configuration search paths to viper
func setupConfigPaths(v *viper.Viper, configPaths []string) {
	v.AddConfigPath("/etc/resumatter")
	v.AddConfigPath("$HOME/.resumatter")
	v.AddConfigPath(".")

	for _, path := range configPaths {
		v.AddConfigPath(path)
	}
}

// loadAndProcessConfig reads, processes and unmarshals the configuration
func loadAndProcessConfig(v *viper.Viper) (*Config, error) {
	// Try to read config file, but don't fail if it doesn't exist
	if err := v.ReadInConfig(); err != nil {
		// Check if it's a "config file not found" error
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// Config file not found is okay - we'll use environment variables and defaults
	}

	expandAndSetEnvVars(v)

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	config.viper = v
	config.callbacks = make(map[string]ConfigChangeCallback)
	config.watching = false

	return &config, nil
}

// NewFromFile creates a new Config instance from a specific file path
func NewFromFile(configPath string) (*Config, error) {
	return New(Options{
		ConfigPath:    configPath,
		ApplyDefaults: true,
	})
}

// RegisterConfigChangeCallback registers a callback function to be called when config changes
// Returns a unique ID that can be used to deregister the callback
// Returns error if maximum number of callbacks (16) is exceeded
func (c *Config) RegisterConfigChangeCallback(id string, callback ConfigChangeCallback) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if len(c.callbacks) >= MaxCallbacks {
		return fmt.Errorf("maximum number of config change callbacks (%d) exceeded", MaxCallbacks)
	}

	if _, exists := c.callbacks[id]; exists {
		return fmt.Errorf("callback with id '%s' already registered", id)
	}

	c.callbacks[id] = callback

	// Start watching if this is the first callback
	if len(c.callbacks) == 1 && !c.watching {
		c.startWatching()
	}

	return nil
}

// DeregisterConfigChangeCallback removes a previously registered callback
// Returns error if the callback ID is not found
func (c *Config) DeregisterConfigChangeCallback(id string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if _, exists := c.callbacks[id]; !exists {
		return fmt.Errorf("callback with id '%s' not found", id)
	}

	delete(c.callbacks, id)

	// Stop watching if no callbacks remain
	if len(c.callbacks) == 0 && c.watching {
		c.stopWatching()
	}

	return nil
}

// GetRegisteredCallbacks returns the IDs of all registered callbacks
func (c *Config) GetRegisteredCallbacks() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	ids := make([]string, 0, len(c.callbacks))
	for id := range c.callbacks {
		ids = append(ids, id)
	}
	return ids
}

// startWatching starts watching the config file for changes
func (c *Config) startWatching() {
	if c.watching {
		return
	}

	c.viper.OnConfigChange(func(e fsnotify.Event) {
		// Create a copy of the current config which preserves the "old" state.
		// We must capture the old state before Viper reloads the file.
		c.mu.RLock()
		oldConfig := &Config{
			AI:            c.AI,
			Server:        c.Server,
			TLS:           c.TLS,
			Auth:          c.Auth,
			RateLimit:     c.RateLimit,
			Logging:       c.Logging,
			Observability: c.Observability,
		}
		c.mu.RUnlock()

		// Create a new config object to reload the configuration
		newConfig, err := NewFromFile(c.viper.ConfigFileUsed())
		if err != nil {
			fmt.Printf("ERROR: Failed to reload configuration from %s: %v.\n", e.Name, err)
			return
		}
		newConfig.viper = nil
		newConfig.callbacks = nil

		c.updateFromReloaded(newConfig)

		// Notify all callbacks
		c.notifyCallbacks(e, oldConfig, newConfig)
	})

	c.viper.WatchConfig()
	c.watching = true
}

// stopWatching stops watching the config file
func (c *Config) stopWatching() {
	// Note: Viper doesn't provide a direct way to stop watching,
	// but we can mark it as not watching and clear the callback
	c.viper.OnConfigChange(nil)
	c.watching = false
}

// notifyCallbacks calls all registered callbacks with the event and both old and new configs.
func (c *Config) notifyCallbacks(event fsnotify.Event, oldConfig, newConfig *Config) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	for _, callback := range c.callbacks {
		// Call each callback in a goroutine to prevent blocking
		go func(cb ConfigChangeCallback) {
			defer func() {
				if r := recover(); r != nil {
					// Log panic but don't crash the application
					fmt.Printf("Config change callback panicked: %v\n", r)
				}
			}()
			cb(event, oldConfig, newConfig)
		}(callback)
	}
}

// updateFromReloaded updates the current config with values from a reloaded config
func (c *Config) updateFromReloaded(newConfig *Config) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.AI = newConfig.AI
	c.Server = newConfig.Server
	c.TLS = newConfig.TLS
	c.Auth = newConfig.Auth
	c.RateLimit = newConfig.RateLimit
	c.Logging = newConfig.Logging
	c.Observability = newConfig.Observability
}
