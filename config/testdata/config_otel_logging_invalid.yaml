# Test configuration with invalid OTLP logging config
ai:
  default_preset: "test"
  presets:
    test:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.8
      max_tokens: 8192
      timeout: "30s"
      provider_config:
        backend: "gemini"
        api_key: "test-key"

server:
  port: "8780"
  environment: "development"

observability:
  service_name: "resumatter-test"
  logging_enabled: true
  log_exporter: "otlp"
  otlp_log_endpoint: ""  # Missing endpoint - should cause validation error
  otlp_log_protocol: "grpc"