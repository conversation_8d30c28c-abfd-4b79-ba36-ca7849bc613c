package config

import (
	"fmt"
	"slices"
	"strings"
)

// GenAIConfig holds unified GenAI configuration for both Gemini API and Vertex AI.
// This struct is used to validate the provider_config section when the provider is
// "genai-gemini" or "genai-vertexai".
//
// YAML path: ai.presets.<preset-name>.provider_config
// Used when: ai.presets.<preset-name>.provider == "genai-gemini" OR "genai-vertexai"
type GenAIConfig struct {
	ApiKey             string `mapstructure:"api_key"`              // API key for Gemini API
	Backend            string `mapstructure:"backend"`              // "gemini", "vertexai"
	CredentialsFile    string `mapstructure:"credentials_file"`     // Path to credentials file
	CredentialsJSON    string `mapstructure:"credentials_json"`     // Credentials JSON string
	GoogleCloudProject string `mapstructure:"google_cloud_project"` // Google Cloud project id
	GoogleCloudRegion  string `mapstructure:"google_cloud_region"`  // Google Cloud region
}

func validateGenAIConfig(presetName string, preset *PresetConfig, fullConfig *Config) error {
	genaiConfig := parseGenAIConfig(preset)

	// If no API key in provider_config, check top-level ai.gemini_api_key field if provider is gemini
	if genaiConfig.ApiKey == "" && preset.Provider == "genai-gemini" && fullConfig != nil {
		genaiConfig.ApiKey = fullConfig.AI.GeminiAPIKey
	}

	// Validate backend is specified and valid
	if err := validateBackend(presetName, genaiConfig.Backend); err != nil {
		return err
	}

	// Validate provider matches backend
	expectedProvider := "genai-" + genaiConfig.Backend
	if preset.Provider != expectedProvider {
		return fmt.Errorf("preset '%s': provider '%s' does not match backend '%s' (expected '%s')",
			presetName, preset.Provider, genaiConfig.Backend, expectedProvider)
	}

	// Backend-specific validation
	switch genaiConfig.Backend {
	case "gemini":
		return validateGeminiConfig(presetName, genaiConfig)
	case "vertexai":
		return validateVertexAIConfig(presetName, genaiConfig)
	}

	return nil
}

func parseGenAIConfig(preset *PresetConfig) *GenAIConfig {
	var genaiConfig GenAIConfig
	if preset.ProviderConfig == nil {
		return &genaiConfig
	}

	if backend, ok := preset.ProviderConfig["backend"].(string); ok {
		genaiConfig.Backend = backend
	}
	if apiKey, ok := preset.ProviderConfig["api_key"].(string); ok {
		genaiConfig.ApiKey = apiKey
	}
	if credFile, ok := preset.ProviderConfig["credentials_file"].(string); ok {
		genaiConfig.CredentialsFile = credFile
	}
	if credJSON, ok := preset.ProviderConfig["credentials_json"].(string); ok {
		genaiConfig.CredentialsJSON = credJSON
	}
	if project, ok := preset.ProviderConfig["google_cloud_project"].(string); ok {
		genaiConfig.GoogleCloudProject = project
	}
	if region, ok := preset.ProviderConfig["google_cloud_region"].(string); ok {
		genaiConfig.GoogleCloudRegion = region
	}

	return &genaiConfig
}

func validateBackend(presetName, backend string) error {
	if backend == "" {
		return fmt.Errorf("preset '%s': GenAI backend is required", presetName)
	}
	validBackends := []string{"gemini", "vertexai"}
	if !slices.Contains(validBackends, backend) {
		return fmt.Errorf("preset '%s': GenAI backend must be one of: %s", presetName, strings.Join(validBackends, ", "))
	}
	return nil
}

func validateGeminiConfig(presetName string, config *GenAIConfig) error {
	// For Gemini API, API key is typically required
	// Check both provider_config.api_key and top-level api_key
	if config.ApiKey == "" {
		return fmt.Errorf("preset '%s': API key is required for Gemini backend", presetName)
	}

	// Note: Google Cloud project/region can be set for Gemini if needed for certain use cases
	// We'll allow them but they're not required for basic Gemini API usage

	// Removed noisy debug print on config change; if needed, handle in higher-level logger
	return nil
}

func validateVertexAIConfig(presetName string, config *GenAIConfig) error {
	// For Vertex AI, we need either credentials file/JSON or default credentials
	hasCredentials := config.CredentialsFile != "" || config.CredentialsJSON != ""

	// Google Cloud project is required for Vertex AI
	if config.GoogleCloudProject == "" {
		return fmt.Errorf("preset '%s': google_cloud_project is required for Vertex AI backend", presetName)
	}

	// Region is typically required for Vertex AI
	if config.GoogleCloudRegion == "" {
		return fmt.Errorf("preset '%s': google_cloud_region is required for Vertex AI backend", presetName)
	}

	// Validate region format (basic check)
	validRegionPrefixes := []string{"us-", "europe-", "asia-"}
	validRegion := false
	for _, prefix := range validRegionPrefixes {
		if strings.HasPrefix(config.GoogleCloudRegion, prefix) {
			validRegion = true
			break
		}
	}
	if !validRegion {
		return fmt.Errorf("preset '%s': google_cloud_region '%s' appears invalid (should start with us-, europe-, or asia-)",
			presetName, config.GoogleCloudRegion)
	}

	// API key should not be set for Vertex AI (uses service account)
	if config.ApiKey != "" {
		return fmt.Errorf("preset '%s': api_key should not be set for Vertex AI backend (uses service account authentication)", presetName)
	}

	// Warn if no explicit credentials are provided (will use default)
	_ = hasCredentials // We'll rely on default credentials if none provided

	return nil
}
