package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestValidateOTLPLogConfig(t *testing.T) {
	tests := []struct {
		name        string
		obs         *ObservabilityConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid OTLP log config with grpc",
			obs: &ObservabilityConfig{
				LogExporter:     "otlp",
				OTLPLogEndpoint: "localhost:4317",
				OTLPLogProtocol: "grpc",
				OTLPLogURLPath:  "",
				OTLPLogHeaders:  map[string]string{},
				OTLPLogInsecure: false,
			},
			expectError: false,
		},
		{
			name: "valid OTLP log config with http",
			obs: &ObservabilityConfig{
				LogExporter:     "otlp",
				OTLPLogEndpoint: "localhost:4318",
				OTLPLogProtocol: "http",
				OTLPLogURLPath:  "/v1/logs",
				OTLPLogHeaders:  map[string]string{},
				OTLPLogInsecure: true,
			},
			expectError: false,
		},
		{
			name: "invalid OTLP log protocol",
			obs: &ObservabilityConfig{
				LogExporter:     "otlp",
				OTLPLogEndpoint: "localhost:4317",
				OTLPLogProtocol: "invalid",
				OTLPLogURLPath:  "",
				OTLPLogHeaders:  map[string]string{},
				OTLPLogInsecure: false,
			},
			expectError: true,
			errorMsg:    "OTLP log protocol must be one of: grpc, http",
		},
		{
			name: "missing OTLP log endpoint",
			obs: &ObservabilityConfig{
				LogExporter:     "otlp",
				OTLPLogEndpoint: "",
				OTLPLogProtocol: "grpc",
				OTLPLogURLPath:  "",
				OTLPLogHeaders:  map[string]string{},
				OTLPLogInsecure: false,
			},
			expectError: true,
			errorMsg:    "otlp_log_endpoint is required when using OTLP log exporter",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateOTLPLogConfig(tt.obs)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateLogging(t *testing.T) {
	tests := []struct {
		name        string
		obs         *ObservabilityConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "logging disabled",
			obs: &ObservabilityConfig{
				LoggingEnabled: false,
				LogExporter:    "",
				LogFilePath:    "",
			},
			expectError: false,
		},
		{
			name: "valid OTLP log config",
			obs: &ObservabilityConfig{
				LoggingEnabled:  true,
				LogExporter:     "otlp",
				OTLPLogEndpoint: "localhost:4317",
				OTLPLogProtocol: "grpc",
			},
			expectError: false,
		},
		{
			name: "valid file log config",
			obs: &ObservabilityConfig{
				LoggingEnabled: true,
				LogExporter:    "file",
				LogFilePath:    "/tmp/logs.json",
			},
			expectError: false,
		},
		{
			name: "valid console log config",
			obs: &ObservabilityConfig{
				LoggingEnabled: true,
				LogExporter:    "console",
			},
			expectError: false,
		},
		{
			name: "missing log exporter when enabled",
			obs: &ObservabilityConfig{
				LoggingEnabled: true,
				LogExporter:    "",
			},
			expectError: true,
			errorMsg:    "log_exporter is required when logging is enabled",
		},
		{
			name: "invalid log exporter",
			obs: &ObservabilityConfig{
				LoggingEnabled: true,
				LogExporter:    "invalid",
			},
			expectError: true,
			errorMsg:    "log exporter must be one of: otlp, file, console",
		},
		{
			name: "missing file path for file exporter",
			obs: &ObservabilityConfig{
				LoggingEnabled: true,
				LogExporter:    "file",
				LogFilePath:    "",
			},
			expectError: true,
			errorMsg:    "log_file_path is required when using file log exporter",
		},
		{
			name: "invalid OTLP log config",
			obs: &ObservabilityConfig{
				LoggingEnabled:  true,
				LogExporter:     "otlp",
				OTLPLogEndpoint: "", // Missing endpoint
				OTLPLogProtocol: "grpc",
			},
			expectError: true,
			errorMsg:    "OTLP log config validation failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateLogging(tt.obs)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
