package config

import (
	"errors"
	"fmt"
	"time"
)

// Error constants for operation resolution
var (
	ErrOperationNotFound = errors.New("operation not found")
	ErrPresetNotFound    = errors.New("preset not found")
	ErrInvalidTimeout    = errors.New("invalid timeout format")
)

// ResolvedOperationConfig represents a fully resolved configuration for an AI operation
type ResolvedOperationConfig struct {
	Provider       string
	Model          string
	Temperature    float64
	MaxTokens      int
	Timeout        time.Duration
	ProviderConfig map[string]any
	CircuitBreaker CircuitBreakerConfig
}

func (c *Config) ResolveOperationConfig(operationName string) (*ResolvedOperationConfig, error) {
	// Get operation config, or create a default one if not found
	op, exists := c.AI.Operations[operationName]
	if !exists {
		// If operation is not defined, create a default operation config using the default preset
		if c.AI.DefaultPreset == "" {
			return nil, fmt.Errorf("operation '%s' not found and no default preset configured", operationName)
		}
		op = OperationConfig{
			Preset: c.AI.DefaultPreset,
		}
	}

	// Get preset config
	preset, exists := c.AI.Presets[op.Preset]
	if !exists {
		return nil, fmt.Errorf("%w: preset '%s' for operation '%s'", ErrPresetNotFound, op.Preset, operationName)
	}

	// Resolve effective values (operation overrides take precedence)
	temperature := preset.Temperature
	if op.Temperature != nil {
		temperature = *op.Temperature
	}

	maxTokens := preset.MaxTokens
	if op.MaxTokens != nil {
		maxTokens = *op.MaxTokens
	}

	timeout := preset.Timeout
	if op.Timeout != "" {
		timeout = op.Timeout
	}

	// Parse timeout duration
	timeoutDuration, err := time.ParseDuration(timeout)
	if err != nil {
		return nil, fmt.Errorf("%w: %s", ErrInvalidTimeout, err.Error())
	}

	return &ResolvedOperationConfig{
		Provider:       preset.Provider,
		Model:          preset.Model,
		Temperature:    temperature,
		MaxTokens:      maxTokens,
		Timeout:        timeoutDuration,
		ProviderConfig: preset.ProviderConfig,
		CircuitBreaker: preset.CircuitBreaker,
	}, nil
}

// GetAPIKey returns the API key for the resolved operation
func (r *ResolvedOperationConfig) GetAPIKey() string {
	if apiKey, ok := r.ProviderConfig["api_key"]; ok {
		if keyStr, ok := apiKey.(string); ok {
			return keyStr
		}
	}
	return ""
}

// GetBaseURL returns the base URL for the provider
func (r *ResolvedOperationConfig) GetBaseURL() string {
	if baseURL, ok := r.ProviderConfig["base_url"]; ok {
		if urlStr, ok := baseURL.(string); ok {
			return urlStr
		}
	}
	return ""
}

// GetProviderConfigValue returns a specific provider config value
func (r *ResolvedOperationConfig) GetProviderConfigValue(key string) (any, bool) {
	value, exists := r.ProviderConfig[key]
	return value, exists
}

// IsCircuitBreakerEnabled returns whether circuit breaker is enabled
func (r *ResolvedOperationConfig) IsCircuitBreakerEnabled() bool {
	return r.CircuitBreaker.Enabled
}

// GetCircuitBreakerTimeout returns the circuit breaker timeout as duration
func (r *ResolvedOperationConfig) GetCircuitBreakerTimeout() (time.Duration, error) {
	if r.CircuitBreaker.Timeout == "" {
		return 0, nil
	}
	return time.ParseDuration(r.CircuitBreaker.Timeout)
}

// GetCircuitBreakerInterval returns the circuit breaker interval as duration
func (r *ResolvedOperationConfig) GetCircuitBreakerInterval() (time.Duration, error) {
	if r.CircuitBreaker.Interval == "" {
		return 0, nil
	}
	return time.ParseDuration(r.CircuitBreaker.Interval)
}

// String returns a string representation of the resolved config
func (r *ResolvedOperationConfig) String() string {
	return fmt.Sprintf("Provider: %s, Model: %s, Temperature: %.2f, MaxTokens: %d, Timeout: %v",
		r.Provider, r.Model, r.Temperature, r.MaxTokens, r.Timeout)
}

// ResolvedRateLimitConfig represents a fully resolved configuration for rate limiting an operation
type ResolvedRateLimitConfig struct {
	Enabled           bool
	RequestsPerMinute int
	RequestsPerHour   int
	RequestsPerDay    int
	BurstSize         int
	Window            time.Duration
}

// ResolveRateLimitConfig resolves the effective rate limit configuration for a given operation.
// It applies operation-specific overrides to the default rate limit settings.
func (c *Config) ResolveRateLimitConfig(operationName string) (*ResolvedRateLimitConfig, error) {
	// Start with default rate limit settings
	resolved := &ResolvedRateLimitConfig{
		Enabled:           c.RateLimit.Enabled,
		RequestsPerMinute: c.RateLimit.Defaults.RequestsPerMinute,
		RequestsPerHour:   c.RateLimit.Defaults.RequestsPerHour,
		RequestsPerDay:    c.RateLimit.Defaults.RequestsPerDay,
		BurstSize:         c.RateLimit.Defaults.BurstSize,
	}

	// Parse default window duration
	if c.RateLimit.Defaults.Window != "" {
		dur, err := time.ParseDuration(c.RateLimit.Defaults.Window)
		if err != nil {
			return nil, fmt.Errorf("invalid default rate limit window format: %w", err)
		}
		resolved.Window = dur
	}

	// Apply operation-specific overrides if they exist and are enabled
	if opLimit, exists := c.RateLimit.Operations[operationName]; exists && opLimit.Enabled {
		resolved.Enabled = opLimit.Enabled
		// If RequestsPerMinute is explicitly set (including to 0), use it to override the default.
		if opLimit.RequestsPerMinute != nil {
			resolved.RequestsPerMinute = *opLimit.RequestsPerMinute
		}
		// If RequestsPerHour is explicitly set (including to 0), use it to override the default.
		if opLimit.RequestsPerHour != nil {
			resolved.RequestsPerHour = *opLimit.RequestsPerHour
		}
		// If RequestsPerDay is explicitly set (including to 0), use it to override the default.
		if opLimit.RequestsPerDay != nil {
			resolved.RequestsPerDay = *opLimit.RequestsPerDay
		}
		if opLimit.BurstSize > 0 {
			resolved.BurstSize = opLimit.BurstSize
		}
		if opLimit.Window != "" {
			dur, err := time.ParseDuration(opLimit.Window)
			if err != nil {
				return nil, fmt.Errorf("invalid rate limit window format for operation '%s': %w", operationName, err)
			}
			resolved.Window = dur
		}
	}

	return resolved, nil
}

// ListAvailableOperations returns all available operation names
func (c *Config) ListAvailableOperations() []string {
	operations := make([]string, 0, len(c.AI.Operations))
	for name := range c.AI.Operations {
		operations = append(operations, name)
	}
	return operations
}

// GetDefaultOperationConfig resolves the configuration for the default preset
func (c *Config) GetDefaultOperationConfig() (*ResolvedOperationConfig, error) {
	if c.AI.DefaultPreset == "" {
		return nil, errors.New("no default preset configured")
	}

	preset, exists := c.AI.Presets[c.AI.DefaultPreset]
	if !exists {
		return nil, fmt.Errorf("default preset '%s' not found", c.AI.DefaultPreset)
	}

	// Parse timeout duration
	timeoutDuration, err := time.ParseDuration(preset.Timeout)
	if err != nil {
		return nil, fmt.Errorf("%w: %s", ErrInvalidTimeout, err.Error())
	}

	return &ResolvedOperationConfig{
		Provider:       preset.Provider,
		Model:          preset.Model,
		Temperature:    preset.Temperature,
		MaxTokens:      preset.MaxTokens,
		Timeout:        timeoutDuration,
		ProviderConfig: preset.ProviderConfig,
		CircuitBreaker: preset.CircuitBreaker,
	}, nil
}
