package config

import (
	"fmt"
	"os"

	"github.com/spf13/viper"
)

func applyDefaultValues(v *viper.Viper) {
	// Server
	v.SetDefault("server.port", "8780")
	v.SetDefault("server.environment", "development")
	v.<PERSON>Default("server.cors.enabled", false)

	// TLS
	v.SetDefault("tls.mode", "off")
	v.SetDefault("tls.min_version", "1.2")
	v.SetDefault("tls.cipher_suites", []string{})
	v.SetDefault("tls.cert_file", "")
	v.SetDefault("tls.key_file", "")
	v.SetDefault("tls.ca_file", "")

	// Auth
	v.SetDefault("auth.enabled", false)
	v.SetDefault("auth.api_keys", []string{})

	// Rate Limit
	v.SetDefault("rate_limit.enabled", false)
	v.SetDefault("rate_limit.required", true)
	v.SetDefault("rate_limit.backend", "memory")
	v.SetDefault("rate_limit.strategy", "token_bucket")
	v.SetDefault("rate_limit.key_by", "ip")
	v.SetDefault("rate_limit.header_name", "X-API-Key")
	v.SetDefault("rate_limit.defaults.requests_per_minute", 60)
	v.SetDefault("rate_limit.defaults.requests_per_hour", 1000)
	v.SetDefault("rate_limit.defaults.requests_per_day", 10000)
	v.SetDefault("rate_limit.defaults.burst_size", 10)
	v.SetDefault("rate_limit.defaults.window", "1m")
	v.SetDefault("rate_limit.operations", map[string]RateLimitOperation{})

	// Logging
	v.SetDefault("logging.level", "info")
	v.SetDefault("logging.format", "json")

	// Observability
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}
	v.SetDefault("observability.service_name", "github.com/ajiwo/resumatter-"+hostname)
	v.SetDefault("observability.service_version", "")
	v.SetDefault("observability.environment", "development")
	v.SetDefault("observability.tracing_enabled", false)
	v.SetDefault("observability.trace_exporter", "otlp")
	v.SetDefault("observability.trace_file_path", "")
	v.SetDefault("observability.metrics_enabled", false)
	v.SetDefault("observability.metrics_exporter", "otlp")
	v.SetDefault("observability.metrics_file_path", "")
	v.SetDefault("observability.metrics_interval", "90s")
	v.SetDefault("observability.otlp_trace_endpoint", "localhost:4317")
	v.SetDefault("observability.otlp_trace_protocol", "grpc")
	v.SetDefault("observability.otlp_trace_url_path", "/v1/traces")
	v.SetDefault("observability.otlp_trace_headers", map[string]string{})
	v.SetDefault("observability.otlp_trace_insecure", false)
	v.SetDefault("observability.otlp_metrics_endpoint", "localhost:4317")
	v.SetDefault("observability.otlp_metrics_protocol", "grpc")
	v.SetDefault("observability.otlp_metrics_url_path", "/v1/metrics")
	v.SetDefault("observability.otlp_metrics_headers", map[string]string{})
	v.SetDefault("observability.otlp_metrics_insecure", false)
	v.SetDefault("observability.logging_enabled", false)
	v.SetDefault("observability.log_exporter", "otlp")
	v.SetDefault("observability.log_file_path", "")
	v.SetDefault("observability.otlp_log_endpoint", "localhost:4317")
	v.SetDefault("observability.otlp_log_protocol", "grpc")
	v.SetDefault("observability.otlp_log_url_path", "/v1/logs")
	v.SetDefault("observability.otlp_log_headers", map[string]string{})
	v.SetDefault("observability.otlp_log_insecure", false)

	v.SetDefault("ai.openai_base_url", "https://api.openai.com/v1")
	v.SetDefault("ai.openai_model", "gpt-4o")
}

// applyPresetDefaults applies default values to all presets for missing parameters
func applyPresetDefaults(v *viper.Viper, config *Config) error {
	// Apply defaults to all existing presets
	for presetName := range config.AI.Presets {
		// Set default values for missing preset parameters
		if !v.IsSet(fmt.Sprintf("ai.presets.%s.temperature", presetName)) {
			v.SetDefault(fmt.Sprintf("ai.presets.%s.temperature", presetName), 0.7)
		}
		if !v.IsSet(fmt.Sprintf("ai.presets.%s.max_tokens", presetName)) {
			v.SetDefault(fmt.Sprintf("ai.presets.%s.max_tokens", presetName), 8192)
		}
		if !v.IsSet(fmt.Sprintf("ai.presets.%s.timeout", presetName)) {
			v.SetDefault(fmt.Sprintf("ai.presets.%s.timeout", presetName), "30s")
		}
	}

	// Re-unmarshal to apply the defaults
	if err := v.Unmarshal(&config); err != nil {
		return fmt.Errorf("applyPresetDefaults failed to unmarshal config: %w", err)
	}
	return nil
}

func addDefaultPreset(v *viper.Viper, config *Config) error {
	// Auto-set default preset if there's only one preset and no default is specified
	presetLen := len(config.AI.Presets)
	if presetLen == 1 && config.AI.DefaultPreset == "" {
		for presetName := range config.AI.Presets {
			config.AI.DefaultPreset = presetName
			// Set default values for temperature, max_tokens, and timeout
			v.SetDefault("ai.presets."+presetName+".temperature", 0.7)
			v.SetDefault("ai.presets."+presetName+".max_tokens", 8192)
			v.SetDefault("ai.presets."+presetName+".timeout", "30s")
			if err := v.Unmarshal(&config); err != nil {
				return fmt.Errorf("addDefaultPreset failed to unmarshal config: %w", err)
			}
			return nil
		}
	}
	if presetLen > 0 {
		return nil
	}

	// Check which API keys are provided
	geminiAPIKey := v.GetString("ai.gemini_api_key")
	openaiAPIKey := v.GetString("ai.openai_api_key")

	// Validate that only one API key is provided
	if geminiAPIKey != "" && openaiAPIKey != "" {
		return fmt.Errorf("cannot specify both ai.gemini_api_key and ai.openai_api_key - choose one provider")
	}

	// Add default preset based on which API key is provided
	if geminiAPIKey != "" {
		return addGeminiDefaultPreset(v, config, geminiAPIKey)
	} else if openaiAPIKey != "" {
		return addOpenAIDefaultPreset(v, config, openaiAPIKey)
	}

	// No API keys provided - this is an error since we need at least one to create a default preset
	return fmt.Errorf("no API key provided - specify either ai.gemini_api_key or ai.openai_api_key to create a default preset")
}

func addGeminiDefaultPreset(v *viper.Viper, config *Config, apiKey string) error {
	model := v.GetString("ai.gemini_model")
	if model == "" {
		model = "gemini-2.0-flash"
	}

	v.Set("ai.default_preset", "default")
	v.Set("ai.presets.default", PresetConfig{
		Provider:    "genai-gemini",
		Model:       model,
		Temperature: 0.7,
		MaxTokens:   8192,
		Timeout:     "30s",
		ProviderConfig: map[string]any{
			"backend": "gemini",
			"api_key": apiKey,
		},
	})

	return setDefaultPresetForOperations(v, config)
}

func addOpenAIDefaultPreset(v *viper.Viper, config *Config, apiKey string) error {
	model := v.GetString("ai.openai_model")
	if model == "" {
		model = "gpt-4o"
	}
	baseURL := v.GetString("ai.openai_base_url")
	if baseURL == "" {
		baseURL = "https://api.openai.com/v1"
	}

	v.Set("ai.default_preset", "default")
	v.Set("ai.presets.default", PresetConfig{
		Provider:    "openai",
		Model:       model,
		Temperature: 0.7,
		MaxTokens:   8192,
		Timeout:     "30s",
		ProviderConfig: map[string]any{
			"api_key":  apiKey,
			"base_url": baseURL,
		},
	})

	return setDefaultPresetForOperations(v, config)
}

func setDefaultPresetForOperations(v *viper.Viper, config *Config) error {
	// Ensure operations map is initialized
	if config.AI.Operations == nil {
		config.AI.Operations = make(map[string]OperationConfig)
	}

	// Define standard operations that should always be available
	standardOperations := []string{"tailor", "evaluate", "analyze", "git-commit"}

	// Set default preset for all operations that don't have one
	for opName, op := range config.AI.Operations {
		if op.Preset == "" {
			v.Set(fmt.Sprintf("ai.operations.%s.preset", opName), "default")
		}
	}

	// Add missing standard operations with default preset
	for _, opName := range standardOperations {
		if _, exists := config.AI.Operations[opName]; !exists {
			v.Set(fmt.Sprintf("ai.operations.%s.preset", opName), "default")
		}
	}

	if err := v.Unmarshal(&config); err != nil {
		return fmt.Errorf("addDefaultPreset failed to unmarshal config: %w", err)
	}
	return nil
}
