package config

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ajiwo/resumatter/util"
)

func TestNew(t *testing.T) {
	t.Run("success with valid config", func(t *testing.T) {
		configPath := filepath.Join("testdata", "config_minimal.yaml")
		cfg, err := New(Options{ConfigPath: configPath})
		require.NoError(t, err)
		require.NotNil(t, cfg)
		assert.Equal(t, "minimal", cfg.AI.DefaultPreset)
		assert.Equal(t, "8380", cfg.Server.Port)
	})

	t.Run("error on non-existent file", func(t *testing.T) {
		_, err := New(Options{ConfigPath: "testdata/non_existent_config.yaml"})
		require.Error(t, err)
	})

	t.Run("error on invalid format", func(t *testing.T) {
		configPath := filepath.Join("testdata", "config_invalid_format.yaml")
		_, err := New(Options{ConfigPath: configPath})
		require.Error(t, err)
		assert.Contains(t, err.Error(), "failed to read config file")
	})

	t.Run("error on validation failure", func(t *testing.T) {
		configPath := filepath.Join("testdata", "config_invalid_validation.yaml")
		_, err := New(Options{ConfigPath: configPath})
		require.Error(t, err)
		assert.Contains(t, err.Error(), "config validation failed")
	})

	t.Run("success with validation skipped", func(t *testing.T) {
		configPath := filepath.Join("testdata", "config_invalid_validation.yaml")
		cfg, err := New(Options{ConfigPath: configPath, SkipValidation: true})
		require.NoError(t, err)
		require.NotNil(t, cfg)
		assert.Equal(t, "invalid", cfg.AI.DefaultPreset)
	})

	t.Run("new from file", func(t *testing.T) {
		configPath := filepath.Join("testdata", "config_minimal.yaml")
		cfg, err := NewFromFile(configPath)
		require.NoError(t, err)
		require.NotNil(t, cfg)
		assert.Equal(t, "minimal", cfg.AI.DefaultPreset)
	})

	t.Run("new empty options", func(t *testing.T) {
		err := os.Chdir("testdata")
		require.NoError(t, err)
		defer func() {
			if chdirErr := os.Chdir(".."); chdirErr != nil {
				t.Logf("failed to change directory back: %v", chdirErr)
			}
		}()
		cfg, err := New(Options{})
		require.NoError(t, err)
		require.NotNil(t, cfg)
		assert.Equal(t, "8780", cfg.Server.Port)
	})

	t.Run("new with empty extra paths", func(t *testing.T) {
		err := os.Chdir("testdata")
		require.NoError(t, err)
		defer func() {
			if chdirErr := os.Chdir(".."); chdirErr != nil {
				t.Logf("failed to change directory back: %v", chdirErr)
			}
		}()
		cfg, err := New(Options{ConfigPaths: []string{}})
		require.NoError(t, err)
		require.NotNil(t, cfg)
		assert.Equal(t, "8780", cfg.Server.Port)
	})

	t.Run("new with extra paths", func(t *testing.T) {
		cfg, err := New(Options{ConfigPaths: []string{"testdata"}})
		require.NoError(t, err)
		require.NotNil(t, cfg)
		assert.Equal(t, "8780", cfg.Server.Port)
	})

}

func TestNew_WithEnvVarOverride(t *testing.T) {
	configFile := filepath.Join("testdata", "config_with_env_override.yaml")

	t.Setenv("TESTAPP_SERVER_PORT", "9999")
	t.Setenv("TESTAPP_AI_DEFAULT_PRESET", "env_preset")

	cfg, err := New(Options{
		ConfigPath:     configFile,
		EnvPrefix:      "TESTAPP",
		SkipValidation: true,
	})

	require.NoError(t, err)
	require.NotNil(t, cfg)

	assert.Equal(t, "9999", cfg.Server.Port)
	assert.Equal(t, "env_preset", cfg.AI.DefaultPreset)
	// This one was not overridden
	assert.Equal(t, "development", cfg.Server.Environment)
}

func TestNew_WithEnvVarExpansion(t *testing.T) {
	configFile := filepath.Join("testdata", "config_with_env_expansion.yaml")
	expectedAPIKey := "my-secret-api-key"

	t.Setenv("TEST_API_KEY", expectedAPIKey)

	cfg, err := New(Options{
		ConfigPath: configFile,
	})

	require.NoError(t, err)
	require.NotNil(t, cfg)

	preset, ok := cfg.AI.Presets["env_expansion"]
	require.True(t, ok)

	apiKey, ok := preset.ProviderConfig["api_key"].(string)
	require.True(t, ok)
	assert.Equal(t, expectedAPIKey, apiKey)
}

func TestValidateAIConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *AIConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid config",
			config: &AIConfig{
				DefaultPreset: "default",
				Presets: map[string]PresetConfig{
					"default": {
						Provider:  "openai",
						Model:     "test-model",
						MaxTokens: 100,
						ProviderConfig: map[string]any{
							"api_key": "test-key",
						},
					},
				},
			},
			expectError: false,
		},
		{
			name:        "no presets",
			config:      &AIConfig{DefaultPreset: "default"},
			expectError: true,
			errorMsg:    "at least one AI preset must be defined",
		},
		{
			name: "too many presets",
			config: &AIConfig{
				DefaultPreset: "p0",
				Presets:       make(map[string]PresetConfig, MaxPresets+1),
			},
			expectError: true,
			errorMsg:    "too many AI presets defined",
		},
		{
			name: "missing default preset",
			config: &AIConfig{
				Presets: map[string]PresetConfig{"p1": {}},
			},
			expectError: true,
			errorMsg:    "default_preset must be specified",
		},
		{
			name: "default preset not found",
			config: &AIConfig{
				DefaultPreset: "not-found",
				Presets:       map[string]PresetConfig{"p1": {}},
			},
			expectError: true,
			errorMsg:    "default_preset 'not-found' not found in presets",
		},
		{
			name: "invalid preset",
			config: &AIConfig{
				DefaultPreset: "default",
				Presets:       map[string]PresetConfig{"default": {Provider: ""}},
			},
			expectError: true,
			errorMsg:    "preset 'default': provider is required",
		},
		{
			name: "operation with unknown preset",
			config: &AIConfig{
				DefaultPreset: "default",
				Presets: map[string]PresetConfig{
					"default": {
						Provider:  "openai",
						Model:     "m",
						MaxTokens: 1,
						ProviderConfig: map[string]any{
							"api_key": "test-key",
						},
					},
				},
				Operations: map[string]OperationConfig{"op1": {Preset: "unknown"}},
			},
			expectError: true,
			errorMsg:    "operation 'op1' references unknown preset 'unknown'",
		},
	}

	// Setup for "too many presets" test case
	for i := range MaxPresets + 1 {
		tests[2].config.Presets[fmt.Sprintf("p%d", i)] = PresetConfig{
			Provider:  "openai",
			Model:     "m",
			MaxTokens: 1,
			ProviderConfig: map[string]any{
				"api_key": "test-key",
			},
		}
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a dummy config for validation
			dummyConfig := &Config{}
			err := validateAIConfig(tt.config, dummyConfig)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidatePreset(t *testing.T) {
	// ... More validation tests can be added here
	t.Run("invalid temperature", func(t *testing.T) {
		p := &PresetConfig{Provider: "p", Model: "m", MaxTokens: 1, Temperature: 3.0}
		err := validatePreset("test", p, &Config{})
		require.Error(t, err)
		assert.Contains(t, err.Error(), "temperature must be between 0 and 2")
	})

	t.Run("invalid max_tokens", func(t *testing.T) {
		p := &PresetConfig{Provider: "p", Model: "m", MaxTokens: 0}
		err := validatePreset("test", p, &Config{})
		require.Error(t, err)
		assert.Contains(t, err.Error(), "max_tokens must be positive")
	})

	t.Run("invalid timeout", func(t *testing.T) {
		p := &PresetConfig{Provider: "p", Model: "m", MaxTokens: 1, Timeout: "invalid"}
		err := validatePreset("test", p, &Config{})
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid timeout format")
	})
}

func TestValidateCircuitBreaker(t *testing.T) {
	tests := []struct {
		name        string
		cb          *CircuitBreakerConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid circuit breaker config",
			cb: &CircuitBreakerConfig{
				Enabled:          true,
				FailureThreshold: 0.5,
				MinRequests:      10,
				MaxRequests:      100,
				Interval:         "60s",
				Timeout:          "5s",
			},
			expectError: false,
		},
		{
			name:        "invalid failure threshold high",
			cb:          &CircuitBreakerConfig{FailureThreshold: 1.1},
			expectError: true,
			errorMsg:    "failure_threshold must be between 0 and 1",
		},
		{
			name:        "invalid failure threshold low",
			cb:          &CircuitBreakerConfig{FailureThreshold: 0},
			expectError: true,
			errorMsg:    "failure_threshold must be between 0 and 1",
		},
		{
			name:        "invalid min requests",
			cb:          &CircuitBreakerConfig{FailureThreshold: 0.5, MinRequests: 0},
			expectError: true,
			errorMsg:    "min_requests must be positive",
		},
		{
			name:        "invalid max requests",
			cb:          &CircuitBreakerConfig{FailureThreshold: 0.5, MinRequests: 10, MaxRequests: 0},
			expectError: true,
			errorMsg:    "max_requests must be positive",
		},
		{
			name:        "invalid interval",
			cb:          &CircuitBreakerConfig{FailureThreshold: 0.5, MinRequests: 10, MaxRequests: 100, Interval: "invalid"},
			expectError: true,
			errorMsg:    "invalid interval format",
		},
		{
			name:        "invalid timeout",
			cb:          &CircuitBreakerConfig{FailureThreshold: 0.5, MinRequests: 10, MaxRequests: 100, Interval: "60s", Timeout: "invalid"},
			expectError: true,
			errorMsg:    "invalid timeout format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateCircuitBreaker("test_preset", tt.cb)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateOperationConfig(t *testing.T) {
	temp := 2.5
	tempNeg := -0.1
	maxTokensZero := 0
	tests := []struct {
		name        string
		op          *OperationConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid operation config",
			op:          &OperationConfig{},
			expectError: false,
		},
		{
			name:        "invalid temperature high",
			op:          &OperationConfig{Temperature: &temp},
			expectError: true,
			errorMsg:    "temperature must be between 0 and 2",
		},
		{
			name:        "invalid temperature low",
			op:          &OperationConfig{Temperature: &tempNeg},
			expectError: true,
			errorMsg:    "temperature must be between 0 and 2",
		},
		{
			name:        "invalid max tokens",
			op:          &OperationConfig{MaxTokens: &maxTokensZero},
			expectError: true,
			errorMsg:    "max_tokens must be positive",
		},
		{
			name:        "invalid timeout",
			op:          &OperationConfig{Timeout: "invalid"},
			expectError: true,
			errorMsg:    "invalid timeout format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateOperationConfig("test_op", tt.op)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateServerConfig(t *testing.T) {
	tests := []struct {
		name        string
		server      *ServerConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid server config",
			server:      &ServerConfig{Port: "8080", Environment: "development"},
			expectError: false,
		},
		{
			name:        "missing port",
			server:      &ServerConfig{Environment: "production"},
			expectError: true,
			errorMsg:    "server port is required",
		},
		{
			name:        "invalid environment",
			server:      &ServerConfig{Port: "8080", Environment: "invalid"},
			expectError: true,
			errorMsg:    "server environment must be one of: development, staging, production",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateServerConfig(tt.server)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateTLSConfig(t *testing.T) {
	tests := []struct {
		name        string
		tls         *TLSConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "off mode",
			tls:         &TLSConfig{Mode: "off"},
			expectError: false,
		},
		{
			name:        "valid https tls",
			tls:         &TLSConfig{Mode: "https", CertFile: "cert", KeyFile: "key"},
			expectError: false,
		},
		{
			name:        "valid mutual tls",
			tls:         &TLSConfig{Mode: "mutual", CertFile: "cert", KeyFile: "key", CAFile: "ca"},
			expectError: false,
		},
		{
			name:        "invalid mode",
			tls:         &TLSConfig{Mode: "invalid"},
			expectError: true,
			errorMsg:    "TLS mode must be one of: off, https, mutual",
		},
		{
			name:        "https mode missing cert",
			tls:         &TLSConfig{Mode: "https", KeyFile: "key"},
			expectError: true,
			errorMsg:    "TLS cert_file is required when mode is https",
		},
		{
			name:        "https mode missing key",
			tls:         &TLSConfig{Mode: "https", CertFile: "cert"},
			expectError: true,
			errorMsg:    "TLS key_file is required when mode is https",
		},
		{
			name:        "mutual mode missing ca",
			tls:         &TLSConfig{Mode: "mutual", CertFile: "cert", KeyFile: "key"},
			expectError: true,
			errorMsg:    "TLS ca_file is recommended when mode is mutual for client certificate validation",
		},
		{
			name:        "min_version below 1.2 is rejected",
			tls:         &TLSConfig{Mode: "https", CertFile: "cert", KeyFile: "key", MinVersion: "1.1"},
			expectError: true,
			errorMsg:    "TLS min_version must be one of: 1.2, 1.3",
		},
		{
			name:        "min_version 1.2 accepted",
			tls:         &TLSConfig{Mode: "https", CertFile: "cert", KeyFile: "key", MinVersion: "1.2"},
			expectError: false,
		},
		{
			name:        "min_version 1.3 accepted",
			tls:         &TLSConfig{Mode: "https", CertFile: "cert", KeyFile: "key", MinVersion: "1.3"},
			expectError: false,
		},
		{
			name:        "reject TLS 1.3 cipher names in cipher_suites",
			tls:         &TLSConfig{Mode: "https", CertFile: "cert", KeyFile: "key", CipherSuites: []string{"TLS_AES_128_GCM_SHA256", "TLS_AES_256_GCM_SHA384"}},
			expectError: true,
			errorMsg:    "TLS cipher_suites includes TLS 1.3 suites",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateTLSConfig(tt.tls)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateRateLimitConfig(t *testing.T) {
	tests := []struct {
		name        string
		rl          *RateLimitConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "disabled rate limit",
			rl:          &RateLimitConfig{Enabled: false},
			expectError: false,
		},
		{
			name: "valid rate limit",
			rl: &RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "token_bucket",
				Defaults: RateLimitDefaults{RequestsPerMinute: 100},
			},
			expectError: false,
		},
		{
			name: "invalid backend",
			rl: &RateLimitConfig{
				Enabled:  true,
				Backend:  "invalid",
				Defaults: RateLimitDefaults{RequestsPerMinute: 100},
			},
			expectError: true,
			errorMsg:    "rate limit backend must be one of: redis, memory",
		},
		{
			name: "invalid strategy",
			rl: &RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "invalid",
				Defaults: RateLimitDefaults{RequestsPerMinute: 100},
			},
			expectError: true,
			errorMsg:    "rate limit strategy must be one of: token_bucket, fixed_window",
		},
		{
			name: "invalid defaults",
			rl: &RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "token_bucket",
				Defaults: RateLimitDefaults{RequestsPerMinute: 0},
			},
			expectError: true,
			errorMsg:    "rate limit defaults must have at least one positive rate limit",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateRateLimitConfig(tt.rl)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateLoggingConfig(t *testing.T) {
	tests := []struct {
		name        string
		logging     *LoggingConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid logging config",
			logging:     &LoggingConfig{Level: "info", Format: "json"},
			expectError: false,
		},
		{
			name:        "invalid level",
			logging:     &LoggingConfig{Level: "invalid"},
			expectError: true,
			errorMsg:    "logging level must be one of: debug, info, warn, error",
		},
		{
			name:        "invalid format",
			logging:     &LoggingConfig{Level: "info", Format: "invalid"},
			expectError: true,
			errorMsg:    "logging format must be one of: json, text",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateLoggingConfig(tt.logging)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateGenAIConfig(t *testing.T) {
	tests := []struct {
		name        string
		preset      *PresetConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid gemini config",
			preset: &PresetConfig{
				Provider: "genai-gemini",
				ProviderConfig: map[string]any{
					"backend": "gemini",
					"api_key": "test-key",
				},
			},
			expectError: false,
		},
		{
			name: "valid vertexai config",
			preset: &PresetConfig{
				Provider: "genai-vertexai",
				ProviderConfig: map[string]any{
					"backend":              "vertexai",
					"google_cloud_project": "proj",
					"google_cloud_region":  "us-central1",
				},
			},
			expectError: false,
		},
		{
			name: "missing backend",
			preset: &PresetConfig{
				Provider:       "genai-gemini",
				ProviderConfig: map[string]any{},
			},
			expectError: true,
			errorMsg:    "GenAI backend is required",
		},
		{
			name: "provider mismatch",
			preset: &PresetConfig{
				Provider: "genai-gemini",
				ProviderConfig: map[string]any{
					"backend": "vertexai",
				},
			},
			expectError: true,
			errorMsg:    "provider 'genai-gemini' does not match backend 'vertexai'",
		},
		{
			name: "gemini missing api key",
			preset: &PresetConfig{
				Provider: "genai-gemini",
				ProviderConfig: map[string]any{
					"backend": "gemini",
				},
			},
			expectError: true,
			errorMsg:    "API key is required for Gemini backend",
		},
		{
			name: "vertexai missing project",
			preset: &PresetConfig{
				Provider: "genai-vertexai",
				ProviderConfig: map[string]any{
					"backend":             "vertexai",
					"google_cloud_region": "us-central1",
				},
			},
			expectError: true,
			errorMsg:    "google_cloud_project is required for Vertex AI backend",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateGenAIConfig(tt.name, tt.preset, &Config{})
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestValidateOpenAIConfig(t *testing.T) {
	tests := []struct {
		name        string
		preset      *PresetConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid openai config with api key",
			preset: &PresetConfig{
				Provider: "openai",
				ProviderConfig: map[string]any{
					"api_key": "test-key",
				},
			},
			expectError: false,
		},
		{
			name: "valid openai config with provider_config api key",
			preset: &PresetConfig{
				Provider: "openai",
				ProviderConfig: map[string]any{
					"api_key": "test-key",
				},
			},
			expectError: false,
		},
		{
			name: "missing api key",
			preset: &PresetConfig{
				Provider: "openai",
			},
			expectError: true,
			errorMsg:    "API key is required for OpenAI backend",
		},
		{
			name: "invalid base_url",
			preset: &PresetConfig{
				Provider: "openai",
				ProviderConfig: map[string]any{
					"api_key":  "test-key",
					"base_url": "invalid-url",
				},
			},
			expectError: true,
			errorMsg:    "invalid base_url format",
		},
		{
			name: "valid base_url",
			preset: &PresetConfig{
				Provider: "openai",
				ProviderConfig: map[string]any{
					"api_key":  "test-key",
					"base_url": "https://localhost:8080",
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a minimal config for testing
			// For the "missing api key" test, don't provide a fallback key
			var testConfig *Config
			if tt.name == "missing api key" {
				testConfig = &Config{
					AI: AIConfig{
						OpenAIAPIKey: "", // No fallback key for this test
					},
				}
			} else {
				testConfig = &Config{
					AI: AIConfig{
						OpenAIAPIKey: "fallback-key",
					},
				}
			}
			err := validateOpenAIConfig(tt.name, tt.preset, testConfig)
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestIsValidURL(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		expected bool
	}{
		{"valid http", "http://example.com", true},
		{"valid https", "https://example.com", true},
		{"no protocol", "example.com", false},
		{"invalid protocol", "ftp://example.com", false},
		{"empty string", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, util.IsValidURL(tt.url))
		})
	}
}

func TestRegisterDeregisterConfigChangeCallback(t *testing.T) {
	cfg := &Config{
		callbacks: make(map[string]ConfigChangeCallback),
		viper:     viper.New(),
	}

	// Register first callback
	err := cfg.RegisterConfigChangeCallback("cb1", func(e fsnotify.Event, oldCfg, newCfg *Config) {})
	require.NoError(t, err)
	assert.Len(t, cfg.callbacks, 1)
	assert.True(t, cfg.watching)

	// Register second callback
	err = cfg.RegisterConfigChangeCallback("cb2", func(e fsnotify.Event, oldCfg, newCfg *Config) {})
	require.NoError(t, err)
	assert.Len(t, cfg.callbacks, 2)

	// Try to register existing
	err = cfg.RegisterConfigChangeCallback("cb1", func(e fsnotify.Event, oldCfg, newCfg *Config) {})
	require.Error(t, err)
	assert.Contains(t, err.Error(), "already registered")

	// Get registered callbacks
	ids := cfg.GetRegisteredCallbacks()
	assert.ElementsMatch(t, []string{"cb1", "cb2"}, ids)

	// Deregister one
	err = cfg.DeregisterConfigChangeCallback("cb1")
	require.NoError(t, err)
	assert.Len(t, cfg.callbacks, 1)

	// Deregister non-existent
	err = cfg.DeregisterConfigChangeCallback("cb-non-existent")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "not found")

	// Deregister last one
	err = cfg.DeregisterConfigChangeCallback("cb2")
	require.NoError(t, err)
	assert.Len(t, cfg.callbacks, 0)
	assert.False(t, cfg.watching)
}

func TestConfigChangeNotification(t *testing.T) {
	configFile := filepath.Join("testdata", "config_change_notification.yaml")
	updatedConfigFile := filepath.Join("testdata", "config_change_notification_updated.yaml")

	// Create a temporary copy of the initial config file to be modified
	tempDir := t.TempDir()
	tempConfigFile := filepath.Join(tempDir, "config.yaml")
	initialContent, err := os.ReadFile(configFile)
	require.NoError(t, err)
	err = os.WriteFile(tempConfigFile, initialContent, 0644)
	require.NoError(t, err)

	cfg, err := NewFromFile(tempConfigFile)
	require.NoError(t, err)

	var wg sync.WaitGroup
	wg.Add(1)

	var mu sync.Mutex
	var receivedEvent fsnotify.Event
	var receivedOldConfig *Config
	var receivedNewConfig *Config
	callback := func(event fsnotify.Event, oldConfig, newConfig *Config) {
		mu.Lock()
		receivedEvent = event
		receivedOldConfig = oldConfig
		receivedNewConfig = newConfig
		mu.Unlock()
		wg.Done()
	}

	err = cfg.RegisterConfigChangeCallback("test-cb", callback)
	require.NoError(t, err)

	// Wait a moment for viper to start watching
	time.Sleep(100 * time.Millisecond)

	// Modify the file
	updatedContent, err := os.ReadFile(updatedConfigFile)
	require.NoError(t, err)
	err = os.WriteFile(tempConfigFile, updatedContent, 0644)
	require.NoError(t, err)

	// Wait for the callback to be executed
	wg.Wait()

	mu.Lock()
	assert.NotNil(t, receivedOldConfig)
	assert.NotNil(t, receivedNewConfig)
	assert.Equal(t, "8380", receivedOldConfig.Server.Port) // Old config should have original port
	assert.Equal(t, "9090", receivedNewConfig.Server.Port) // New config should have updated port
	mu.Unlock()

	cfg.mu.RLock()
	assert.Equal(t, "9090", cfg.Server.Port) // Check if original config was updated
	cfg.mu.RUnlock()

	mu.Lock()
	assert.Equal(t, fsnotify.Write, receivedEvent.Op)
	mu.Unlock()
}

func TestConfigChangeNotification_UnmarshalError(t *testing.T) {
	configFile := filepath.Join("testdata", "config_change_notification.yaml")
	unmarshalErrorConfigFile := filepath.Join("testdata", "config_unmarshal_error.yaml")

	// Create a temporary copy of the initial config file to be modified
	tempDir := t.TempDir()
	tempConfigFile := filepath.Join(tempDir, "config.yaml")
	initialContent, err := os.ReadFile(configFile)
	require.NoError(t, err)
	err = os.WriteFile(tempConfigFile, initialContent, 0644)
	require.NoError(t, err)

	cfg, err := NewFromFile(tempConfigFile)
	require.NoError(t, err)
	originalPort := cfg.Server.Port

	callback := func(_event fsnotify.Event, _oldCfg, _newCfg *Config) {
		t.Fatalf("callback should not be called on unmarshal error")
	}

	err = cfg.RegisterConfigChangeCallback("test-cb", callback)
	require.NoError(t, err)

	// Wait a moment for viper to start watching
	time.Sleep(100 * time.Millisecond)

	// Modify the file with invalid content that will cause unmarshal to fail
	invalidContent, err := os.ReadFile(unmarshalErrorConfigFile)
	require.NoError(t, err)
	err = os.WriteFile(tempConfigFile, invalidContent, 0644)
	require.NoError(t, err)

	cfg.mu.RLock()
	assert.Equal(t, originalPort, cfg.Server.Port) // Check if original config was NOT updated
	cfg.mu.RUnlock()
}

func TestConfigChangeNotification_CallbackPanic(t *testing.T) {
	configFile := filepath.Join("testdata", "config_change_notification.yaml")
	updatedConfigFile := filepath.Join("testdata", "config_change_notification_updated.yaml")

	// Create a temporary copy of the initial config file to be modified
	tempDir := t.TempDir()
	tempConfigFile := filepath.Join(tempDir, "config.yaml")
	initialContent, err := os.ReadFile(configFile)
	require.NoError(t, err)
	err = os.WriteFile(tempConfigFile, initialContent, 0644)
	require.NoError(t, err)

	cfg, err := NewFromFile(tempConfigFile)
	require.NoError(t, err)

	panickedCh := make(chan struct{}, 1)
	normalCh := make(chan struct{}, 1)

	panickingCallback := func(event fsnotify.Event, oldConfig, newConfig *Config) {
		// Non-blocking send
		select {
		case panickedCh <- struct{}{}:
		default:
		}
		panic("test panic")
	}

	var mu sync.Mutex
	var normalCallbackCalled bool
	normalCallback := func(event fsnotify.Event, oldConfig, newConfig *Config) {
		mu.Lock()
		normalCallbackCalled = true
		mu.Unlock()
		// Non-blocking send
		select {
		case normalCh <- struct{}{}:
		default:
		}
	}

	err = cfg.RegisterConfigChangeCallback("panicking-cb", panickingCallback)
	require.NoError(t, err)
	err = cfg.RegisterConfigChangeCallback("normal-cb", normalCallback)
	require.NoError(t, err)

	// Wait a moment for viper to start watching
	time.Sleep(100 * time.Millisecond)

	// Modify the file
	updatedContent, err := os.ReadFile(updatedConfigFile)
	require.NoError(t, err)
	err = os.WriteFile(tempConfigFile, updatedContent, 0644)
	require.NoError(t, err)

	// Wait for both callbacks to have been called at least once
	timeout := time.After(2 * time.Second)
	receivedPanicked := false
	receivedNormal := false
	for !receivedPanicked || !receivedNormal {
		select {
		case <-panickedCh:
			receivedPanicked = true
		case <-normalCh:
			receivedNormal = true
		case <-timeout:
			t.Fatal("timed out waiting for callbacks")
		}
	}

	mu.Lock()
	assert.True(t, normalCallbackCalled, "normal callback should have been called despite another one panicking")
	mu.Unlock()
}

func TestAddDefaultPreset(t *testing.T) {
	tests := []struct {
		name        string
		setupViper  func(*viper.Viper)
		setupConfig func() *Config
		expectError bool
		errorMsg    string
		validate    func(t *testing.T, v *viper.Viper, config *Config)
	}{
		{
			name: "auto-set default when single preset exists",
			setupViper: func(v *viper.Viper) {
				// No API keys set
			},
			setupConfig: func() *Config {
				return &Config{
					AI: AIConfig{
						Presets: map[string]PresetConfig{
							"existing": {
								Provider: "genai-gemini",
								Model:    "gemini-2.0-flash",
							},
						},
						DefaultPreset: "", // No default set
					},
				}
			},
			expectError: false,
			validate: func(t *testing.T, v *viper.Viper, config *Config) {
				assert.Equal(t, "existing", config.AI.DefaultPreset)
			},
		},
		{
			name: "no change when multiple presets exist",
			setupViper: func(v *viper.Viper) {
				// No API keys set
			},
			setupConfig: func() *Config {
				return &Config{
					AI: AIConfig{
						Presets: map[string]PresetConfig{
							"preset1": {Provider: "genai-gemini"},
							"preset2": {Provider: "openai"},
						},
						DefaultPreset: "preset1",
					},
				}
			},
			expectError: false,
			validate: func(t *testing.T, v *viper.Viper, config *Config) {
				assert.Equal(t, "preset1", config.AI.DefaultPreset)
				assert.Len(t, config.AI.Presets, 2)
			},
		},
		{
			name: "create gemini default preset",
			setupViper: func(v *viper.Viper) {
				v.Set("ai.gemini_api_key", "test-gemini-key")
			},
			setupConfig: func() *Config {
				return &Config{
					AI: AIConfig{
						Presets: map[string]PresetConfig{},
					},
				}
			},
			expectError: false,
			validate: func(t *testing.T, v *viper.Viper, config *Config) {
				assert.Equal(t, "default", config.AI.DefaultPreset)
				assert.Len(t, config.AI.Presets, 1)

				preset := config.AI.Presets["default"]
				assert.Equal(t, "genai-gemini", preset.Provider)
				assert.Equal(t, "gemini-2.0-flash", preset.Model)
				assert.Equal(t, 0.7, preset.Temperature)
				assert.Equal(t, 8192, preset.MaxTokens)
				assert.Equal(t, "30s", preset.Timeout)

				assert.Equal(t, "gemini", preset.ProviderConfig["backend"])
				assert.Equal(t, "test-gemini-key", preset.ProviderConfig["api_key"])
			},
		},
		{
			name: "create openai default preset",
			setupViper: func(v *viper.Viper) {
				v.Set("ai.openai_api_key", "test-openai-key")
			},
			setupConfig: func() *Config {
				return &Config{
					AI: AIConfig{
						Presets: map[string]PresetConfig{},
					},
				}
			},
			expectError: false,
			validate: func(t *testing.T, v *viper.Viper, config *Config) {
				assert.Equal(t, "default", config.AI.DefaultPreset)
				assert.Len(t, config.AI.Presets, 1)

				preset := config.AI.Presets["default"]
				assert.Equal(t, "openai", preset.Provider)
				assert.Equal(t, "gpt-4o", preset.Model)
				assert.Equal(t, 0.7, preset.Temperature)
				assert.Equal(t, 8192, preset.MaxTokens)
				assert.Equal(t, "30s", preset.Timeout)

				assert.Equal(t, "test-openai-key", preset.ProviderConfig["api_key"])
			},
		},
		{
			name: "error when both api keys provided",
			setupViper: func(v *viper.Viper) {
				v.Set("ai.gemini_api_key", "test-gemini-key")
				v.Set("ai.openai_api_key", "test-openai-key")
			},
			setupConfig: func() *Config {
				return &Config{
					AI: AIConfig{
						Presets: map[string]PresetConfig{},
					},
				}
			},
			expectError: true,
			errorMsg:    "cannot specify both ai.gemini_api_key and ai.openai_api_key",
		},
		{
			name: "error when no api keys provided",
			setupViper: func(v *viper.Viper) {
				// No API keys set
			},
			setupConfig: func() *Config {
				return &Config{
					AI: AIConfig{
						Presets: map[string]PresetConfig{},
					},
				}
			},
			expectError: true,
			errorMsg:    "no API key provided",
		},
		{
			name: "set default preset for operations",
			setupViper: func(v *viper.Viper) {
				v.Set("ai.gemini_api_key", "test-gemini-key")
			},
			setupConfig: func() *Config {
				return &Config{
					AI: AIConfig{
						Presets: map[string]PresetConfig{},
						Operations: map[string]OperationConfig{
							"op1": {Preset: ""},      // Empty preset
							"op2": {Preset: "other"}, // Already has preset
						},
					},
				}
			},
			expectError: false,
			validate: func(t *testing.T, v *viper.Viper, config *Config) {
				assert.Equal(t, "default", config.AI.Operations["op1"].Preset)
				assert.Equal(t, "other", config.AI.Operations["op2"].Preset)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := viper.New()
			tt.setupViper(v)
			config := tt.setupConfig()

			err := addDefaultPreset(v, config)

			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
				if tt.validate != nil {
					tt.validate(t, v, config)
				}
			}
		})
	}
}

func TestSetDefaultPresetForOperations(t *testing.T) {
	tests := []struct {
		name       string
		operations map[string]OperationConfig
		expected   map[string]string // operation name -> expected preset
	}{
		{
			name: "set default for empty presets",
			operations: map[string]OperationConfig{
				"op1": {Preset: ""},
				"op2": {Preset: ""},
			},
			expected: map[string]string{
				"op1": "default",
				"op2": "default",
			},
		},
		{
			name: "preserve existing presets",
			operations: map[string]OperationConfig{
				"op1": {Preset: ""},
				"op2": {Preset: "custom"},
			},
			expected: map[string]string{
				"op1": "default",
				"op2": "custom",
			},
		},
		{
			name:       "no operations",
			operations: map[string]OperationConfig{},
			expected:   map[string]string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := viper.New()
			config := &Config{
				AI: AIConfig{
					Operations: tt.operations,
				},
			}

			err := setDefaultPresetForOperations(v, config)
			require.NoError(t, err)

			for opName, expectedPreset := range tt.expected {
				assert.Equal(t, expectedPreset, config.AI.Operations[opName].Preset,
					"Operation %s should have preset %s", opName, expectedPreset)
			}
		})
	}
}

func TestSetDefaultPresetForOperations_UnmarshalError(t *testing.T) {
	// Create a viper instance with invalid data to trigger unmarshal error
	v := viper.New()
	v.Set("ai.presets.default.temperature", "invalid-float")

	config := &Config{
		AI: AIConfig{
			Operations: map[string]OperationConfig{
				"op1": {Preset: ""},
			},
		},
	}

	err := setDefaultPresetForOperations(v, config)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "addDefaultPreset failed to unmarshal config")
}

func TestConfigWithFallback(t *testing.T) {
	tests := []struct {
		name           string
		config         *Config
		operationName  string
		expectError    bool
		expectedPreset string
	}{
		{
			name: "operation exists in config",
			config: &Config{
				AI: AIConfig{
					DefaultPreset: "default",
					Presets: map[string]PresetConfig{
						"default": {
							Provider:    "genai-gemini",
							Model:       "gemini-2.0-flash",
							Temperature: 0.7,
							MaxTokens:   8192,
							Timeout:     "30s",
							ProviderConfig: map[string]any{
								"backend": "gemini",
								"api_key": "test-key",
							},
						},
						"custom": {
							Provider:    "openai",
							Model:       "gpt-4o",
							Temperature: 0.5,
							MaxTokens:   4096,
							Timeout:     "20s",
							ProviderConfig: map[string]any{
								"api_key": "test-openai-key",
							},
						},
					},
					Operations: map[string]OperationConfig{
						"tailor": {Preset: "custom"},
					},
				},
			},
			operationName:  "tailor",
			expectError:    false,
			expectedPreset: "custom",
		},
		{
			name: "operation not defined - fallback to default preset",
			config: &Config{
				AI: AIConfig{
					DefaultPreset: "default",
					Presets: map[string]PresetConfig{
						"default": {
							Provider:    "genai-gemini",
							Model:       "gemini-2.0-flash",
							Temperature: 0.7,
							MaxTokens:   8192,
							Timeout:     "30s",
							ProviderConfig: map[string]any{
								"backend": "gemini",
								"api_key": "test-key",
							},
						},
					},
					Operations: map[string]OperationConfig{},
				},
			},
			operationName:  "analyze",
			expectError:    false,
			expectedPreset: "default",
		},
		{
			name: "operation not defined and no default preset",
			config: &Config{
				AI: AIConfig{
					DefaultPreset: "",
					Presets:       map[string]PresetConfig{},
					Operations:    map[string]OperationConfig{},
				},
			},
			operationName: "evaluate",
			expectError:   true,
		},
		{
			name: "operation references non-existent preset",
			config: &Config{
				AI: AIConfig{
					DefaultPreset: "default",
					Presets: map[string]PresetConfig{
						"default": {
							Provider:    "genai-gemini",
							Model:       "gemini-2.0-flash",
							Temperature: 0.7,
							MaxTokens:   8192,
							Timeout:     "30s",
							ProviderConfig: map[string]any{
								"backend": "gemini",
								"api_key": "test-key",
							},
						},
					},
					Operations: map[string]OperationConfig{
						"tailor": {Preset: "nonexistent"},
					},
				},
			},
			operationName: "tailor",
			expectError:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resolved, err := tt.config.ResolveOperationConfig(tt.operationName)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, resolved)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resolved)

				// Verify the resolved config uses the expected preset
				expectedPreset := tt.config.AI.Presets[tt.expectedPreset]
				assert.Equal(t, expectedPreset.Provider, resolved.Provider)
				assert.Equal(t, expectedPreset.Model, resolved.Model)
				assert.Equal(t, expectedPreset.Temperature, resolved.Temperature)
				assert.Equal(t, expectedPreset.MaxTokens, resolved.MaxTokens)
			}
		})
	}
}

func TestSetDefaultPresetForOperationsWithStandardOps(t *testing.T) {
	v := viper.New()
	config := &Config{
		AI: AIConfig{
			DefaultPreset: "default",
			Presets: map[string]PresetConfig{
				"default": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.7,
					MaxTokens:   8192,
					Timeout:     "30s",
					ProviderConfig: map[string]any{
						"backend": "gemini",
						"api_key": "test-key",
					},
				},
			},
			Operations: map[string]OperationConfig{},
		},
	}

	err := setDefaultPresetForOperations(v, config)
	assert.NoError(t, err)

	// Verify that standard operations were added
	standardOps := []string{"tailor", "evaluate", "analyze", "git-commit"}
	for _, op := range standardOps {
		assert.Contains(t, config.AI.Operations, op)
		assert.Equal(t, "default", config.AI.Operations[op].Preset)
	}
}

func TestApplyPresetDefaults(t *testing.T) {
	tests := []struct {
		name           string
		initialConfig  *Config
		expectedValues map[string]PresetConfig
	}{
		{
			name: "apply defaults to preset missing all parameters",
			initialConfig: &Config{
				AI: AIConfig{
					Presets: map[string]PresetConfig{
						"incomplete": {
							Provider: "openai",
							Model:    "gpt-4o",
							// Missing temperature, max_tokens, timeout
						},
					},
				},
			},
			expectedValues: map[string]PresetConfig{
				"incomplete": {
					Provider:    "openai",
					Model:       "gpt-4o",
					Temperature: 0.7,
					MaxTokens:   8192,
					Timeout:     "30s",
				},
			},
		},
		{
			name: "preserve existing values and only fill missing ones",
			initialConfig: &Config{
				AI: AIConfig{
					Presets: map[string]PresetConfig{
						"partial": {
							Provider:    "genai-gemini",
							Model:       "gemini-2.0-flash",
							Temperature: 0.5, // This should be preserved
							// Missing max_tokens and timeout
						},
					},
				},
			},
			expectedValues: map[string]PresetConfig{
				"partial": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.5,   // Preserved
					MaxTokens:   8192,  // Default applied
					Timeout:     "30s", // Default applied
				},
			},
		},
		{
			name: "no changes when all parameters are present",
			initialConfig: &Config{
				AI: AIConfig{
					Presets: map[string]PresetConfig{
						"complete": {
							Provider:    "openai",
							Model:       "gpt-4o",
							Temperature: 0.9,
							MaxTokens:   4096,
							Timeout:     "60s",
						},
					},
				},
			},
			expectedValues: map[string]PresetConfig{
				"complete": {
					Provider:    "openai",
					Model:       "gpt-4o",
					Temperature: 0.9,
					MaxTokens:   4096,
					Timeout:     "60s",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := viper.New()

			// Set up the initial config in viper
			for presetName, preset := range tt.initialConfig.AI.Presets {
				v.Set(fmt.Sprintf("ai.presets.%s.provider", presetName), preset.Provider)
				v.Set(fmt.Sprintf("ai.presets.%s.model", presetName), preset.Model)
				if preset.Temperature != 0 {
					v.Set(fmt.Sprintf("ai.presets.%s.temperature", presetName), preset.Temperature)
				}
				if preset.MaxTokens != 0 {
					v.Set(fmt.Sprintf("ai.presets.%s.max_tokens", presetName), preset.MaxTokens)
				}
				if preset.Timeout != "" {
					v.Set(fmt.Sprintf("ai.presets.%s.timeout", presetName), preset.Timeout)
				}
			}

			// Apply preset defaults
			err := applyPresetDefaults(v, tt.initialConfig)
			assert.NoError(t, err)

			// Verify the results
			for presetName, expectedPreset := range tt.expectedValues {
				actualPreset := tt.initialConfig.AI.Presets[presetName]
				assert.Equal(t, expectedPreset.Provider, actualPreset.Provider)
				assert.Equal(t, expectedPreset.Model, actualPreset.Model)
				assert.Equal(t, expectedPreset.Temperature, actualPreset.Temperature)
				assert.Equal(t, expectedPreset.MaxTokens, actualPreset.MaxTokens)
				assert.Equal(t, expectedPreset.Timeout, actualPreset.Timeout)
			}
		})
	}
}
