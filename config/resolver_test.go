package config

import (
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// intPtr is a helper function to get a pointer to an int.
func intPtr(i int) *int {
	return &i
}

func TestResolveRateLimitConfig(t *testing.T) {
	baseConfig := &Config{
		RateLimit: RateLimitConfig{
			Enabled: true,
			Defaults: RateLimitDefaults{
				RequestsPerMinute: 100,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
				Window:            "10s",
			},
			Operations: map[string]RateLimitOperation{
				"test-op-override": {
					Enabled:           true,
					RequestsPerMinute: intPtr(50),
					RequestsPerHour:   intPtr(500),
					RequestsPerDay:    intPtr(5000),
					BurstSize:         5,
					Window:            "5s",
				},
				"test-op-zero-override": {
					Enabled:           true,
					RequestsPerMinute: intPtr(0),
					RequestsPerHour:   intPtr(0),
					RequestsPerDay:    intPtr(0),
				},
				"test-op-partial-override": {
					Enabled:         true,
					RequestsPerHour: intPtr(200),
				},
				"test-op-disabled": {
					Enabled:           false,
					RequestsPerMinute: intPtr(1),
				},
			},
		},
	}

	t.Run("operation with full override", func(t *testing.T) {
		resolved, err := baseConfig.ResolveRateLimitConfig("test-op-override")
		assert.NoError(t, err)
		assert.NotNil(t, resolved)

		assert.True(t, resolved.Enabled)
		assert.Equal(t, 50, resolved.RequestsPerMinute)
		assert.Equal(t, 500, resolved.RequestsPerHour)
		assert.Equal(t, 5000, resolved.RequestsPerDay)
		assert.Equal(t, 5, resolved.BurstSize)
		assert.Equal(t, 5*time.Second, resolved.Window)
	})

	t.Run("operation with zero override", func(t *testing.T) {
		resolved, err := baseConfig.ResolveRateLimitConfig("test-op-zero-override")
		assert.NoError(t, err)
		assert.NotNil(t, resolved)

		assert.True(t, resolved.Enabled)
		assert.Equal(t, 0, resolved.RequestsPerMinute)
		assert.Equal(t, 0, resolved.RequestsPerHour)
		assert.Equal(t, 0, resolved.RequestsPerDay)
		assert.Equal(t, 10, resolved.BurstSize) // Should use default
	})

	t.Run("operation with partial override", func(t *testing.T) {
		resolved, err := baseConfig.ResolveRateLimitConfig("test-op-partial-override")
		assert.NoError(t, err)
		assert.NotNil(t, resolved)

		assert.True(t, resolved.Enabled)
		assert.Equal(t, 100, resolved.RequestsPerMinute) // Default
		assert.Equal(t, 200, resolved.RequestsPerHour)   // Override
		assert.Equal(t, 10000, resolved.RequestsPerDay)  // Default
	})

	t.Run("non-existent operation", func(t *testing.T) {
		resolved, err := baseConfig.ResolveRateLimitConfig("non-existent-op")
		assert.NoError(t, err)
		assert.NotNil(t, resolved)

		// Should return default values
		assert.True(t, resolved.Enabled)
		assert.Equal(t, 100, resolved.RequestsPerMinute)
		assert.Equal(t, 1000, resolved.RequestsPerHour)
		assert.Equal(t, 10000, resolved.RequestsPerDay)
		assert.Equal(t, 10, resolved.BurstSize)
		assert.Equal(t, 10*time.Second, resolved.Window)
	})

	t.Run("disabled operation", func(t *testing.T) {
		resolved, err := baseConfig.ResolveRateLimitConfig("test-op-disabled")
		assert.NoError(t, err)
		assert.NotNil(t, resolved)

		// Should return default values because the override is disabled
		assert.True(t, resolved.Enabled)
		assert.Equal(t, 100, resolved.RequestsPerMinute)
	})

	t.Run("invalid window format in default", func(t *testing.T) {
		invalidConfig := &Config{
			RateLimit: RateLimitConfig{
				Defaults: RateLimitDefaults{Window: "invalid"},
			},
		}
		_, err := invalidConfig.ResolveRateLimitConfig("any-op")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid default rate limit window format")
	})

	t.Run("invalid window format in override", func(t *testing.T) {
		invalidConfig := &Config{
			RateLimit: RateLimitConfig{
				Operations: map[string]RateLimitOperation{
					"bad-op": {Enabled: true, Window: "invalid"},
				},
			},
		}
		_, err := invalidConfig.ResolveRateLimitConfig("bad-op")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid rate limit window format for operation")
	})
}

func TestResolveRateLimitConfig_WithDefaultsFromFile(t *testing.T) {
	configPath := filepath.Join("testdata", "config_with_rate_limit_defaults.yaml")
	cfg, err := New(Options{
		ConfigPath:    configPath,
		ApplyDefaults: true, // Make sure defaults are applied
	})
	require.NoError(t, err)
	require.NotNil(t, cfg)

	resolved, err := cfg.ResolveRateLimitConfig("test-op-with-defaults")
	require.NoError(t, err)
	require.NotNil(t, resolved)

	assert.True(t, resolved.Enabled)
	assert.Equal(t, 200, resolved.RequestsPerMinute) // From YAML
	assert.Equal(t, 1000, resolved.RequestsPerHour)  // Default
	assert.Equal(t, 10000, resolved.RequestsPerDay)  // Default
	assert.Equal(t, 10, resolved.BurstSize)          // Default
}
