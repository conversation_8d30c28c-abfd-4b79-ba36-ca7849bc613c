package config

import (
	"fmt"
	"slices"
	"strings"
	"time"
)

// ValidateConfig validates the configuration
func ValidateConfig(config *Config) error {
	// Validate AI configuration
	if err := validateAIConfig(&config.AI, config); err != nil {
		return fmt.Errorf("AI config validation failed: %w", err)
	}

	// Validate server configuration
	if err := validateServerConfig(&config.Server); err != nil {
		return fmt.Errorf("server config validation failed: %w", err)
	}

	// Validate TLS configuration
	if err := validateTLSConfig(&config.TLS); err != nil {
		return fmt.Errorf("TLS config validation failed: %w", err)
	}

	// Validate rate limit configuration
	if err := validateRateLimitConfig(&config.RateLimit); err != nil {
		return fmt.Errorf("rate limit config validation failed: %w", err)
	}

	// Validate logging configuration
	if err := validateLoggingConfig(&config.Logging); err != nil {
		return fmt.Errorf("logging config validation failed: %w", err)
	}

	// Validate observability configuration
	if err := validateObservabilityConfig(&config.Observability); err != nil {
		return fmt.Errorf("observability config validation failed: %w", err)
	}

	return nil
}
func validateAIConfig(ai *AIConfig, fullConfig *Config) error {
	if len(ai.Presets) == 0 {
		return fmt.Errorf("at least one AI preset must be defined")
	}

	if len(ai.Presets) > MaxPresets {
		return fmt.Errorf("too many AI presets defined: %d (maximum allowed: %d)", len(ai.Presets), MaxPresets)
	}

	// Validate default preset exists
	if ai.DefaultPreset == "" {
		return fmt.Errorf("default_preset must be specified")
	}

	if _, exists := ai.Presets[ai.DefaultPreset]; !exists {
		return fmt.Errorf("default_preset '%s' not found in presets", ai.DefaultPreset)
	}

	// Validate each preset
	for name, preset := range ai.Presets {
		if err := validatePreset(name, &preset, fullConfig); err != nil {
			return err
		}
	}

	// Validate operations reference valid presets
	for opName, op := range ai.Operations {
		if _, exists := ai.Presets[op.Preset]; !exists {
			return fmt.Errorf("operation '%s' references unknown preset '%s'", opName, op.Preset)
		}

		if err := validateOperationConfig(opName, &op); err != nil {
			return err
		}
	}

	return nil
}

func validatePreset(name string, preset *PresetConfig, fullConfig *Config) error {
	if preset.Provider == "" {
		return fmt.Errorf("preset '%s': provider is required", name)
	}

	if preset.Model == "" {
		return fmt.Errorf("preset '%s': model is required", name)
	}

	if preset.Temperature < 0 || preset.Temperature > 2 {
		return fmt.Errorf("preset '%s': temperature must be between 0 and 2", name)
	}

	if preset.MaxTokens <= 0 {
		return fmt.Errorf("preset '%s': max_tokens must be positive", name)
	}

	if preset.Timeout != "" {
		if _, err := time.ParseDuration(preset.Timeout); err != nil {
			return fmt.Errorf("preset '%s': invalid timeout format: %w", name, err)
		}
	}

	// Validate circuit breaker if enabled
	if preset.CircuitBreaker.Enabled {
		if err := validateCircuitBreaker(name, &preset.CircuitBreaker); err != nil {
			return err
		}
	}

	// Validate provider-specific configuration
	if err := validateProviderConfig(name, preset, fullConfig); err != nil {
		return err
	}

	return nil
}

func validateCircuitBreaker(presetName string, cb *CircuitBreakerConfig) error {
	if cb.FailureThreshold <= 0 || cb.FailureThreshold > 1 {
		return fmt.Errorf("preset '%s': circuit breaker failure_threshold must be between 0 and 1", presetName)
	}

	if cb.MinRequests <= 0 {
		return fmt.Errorf("preset '%s': circuit breaker min_requests must be positive", presetName)
	}

	if cb.MaxRequests <= 0 {
		return fmt.Errorf("preset '%s': circuit breaker max_requests must be positive", presetName)
	}

	if cb.Interval != "" {
		if _, err := time.ParseDuration(cb.Interval); err != nil {
			return fmt.Errorf("preset '%s': circuit breaker invalid interval format: %w", presetName, err)
		}
	}

	if cb.Timeout != "" {
		if _, err := time.ParseDuration(cb.Timeout); err != nil {
			return fmt.Errorf("preset '%s': circuit breaker invalid timeout format: %w", presetName, err)
		}
	}

	return nil
}

func validateOperationConfig(opName string, op *OperationConfig) error {
	if op.Temperature != nil && (*op.Temperature < 0 || *op.Temperature > 2) {
		return fmt.Errorf("operation '%s': temperature must be between 0 and 2", opName)
	}

	if op.MaxTokens != nil && *op.MaxTokens <= 0 {
		return fmt.Errorf("operation '%s': max_tokens must be positive", opName)
	}

	if op.Timeout != "" {
		if _, err := time.ParseDuration(op.Timeout); err != nil {
			return fmt.Errorf("operation '%s': invalid timeout format: %w", opName, err)
		}
	}

	return nil
}

func validateServerConfig(server *ServerConfig) error {
	if server.Port == "" {
		return fmt.Errorf("server port is required")
	}

	validEnvs := []string{"development", "staging", "production"}
	if server.Environment != "" {
		found := slices.Contains(validEnvs, server.Environment)
		if !found {
			return fmt.Errorf("server environment must be one of: %s", strings.Join(validEnvs, ", "))
		}
	}

	return nil
}

func validateTLSConfig(tls *TLSConfig) error {
	if err := validateTLSModeAndFiles(tls); err != nil {
		return err
	}
	if err := validateTLSMinVersion(tls.MinVersion); err != nil {
		return err
	}
	if err := validateTLSCipherSuites(tls.CipherSuites); err != nil {
		return err
	}
	return nil
}

// validateTLSModeAndFiles validates TLS mode and related file requirements.
func validateTLSModeAndFiles(tls *TLSConfig) error {
	validModes := []string{"off", "https", "mutual"}
	if tls.Mode != "" {
		found := slices.Contains(validModes, tls.Mode)
		if !found {
			return fmt.Errorf("TLS mode must be one of: %s", strings.Join(validModes, ", "))
		}
	}
	// For https and mutual modes, cert and key files are required
	if tls.Mode == "https" || tls.Mode == "mutual" {
		if tls.CertFile == "" {
			return fmt.Errorf("TLS cert_file is required when mode is %s", tls.Mode)
		}
		if tls.KeyFile == "" {
			return fmt.Errorf("TLS key_file is required when mode is %s", tls.Mode)
		}
	}
	// For mutual mode, CA file is typically required for client certificate validation
	if tls.Mode == "mutual" && tls.CAFile == "" {
		return fmt.Errorf("TLS ca_file is recommended when mode is mutual for client certificate validation")
	}
	return nil
}

// validateTLSCipherSuites verifies that config doesn't include TLS 1.3 suite names.
func validateTLSCipherSuites(names []string) error {
	if len(names) == 0 {
		return nil
	}
	tls13Names := map[string]struct{}{
		"TLS_AES_128_GCM_SHA256":       {},
		"TLS_AES_256_GCM_SHA384":       {},
		"TLS_CHACHA20_POLY1305_SHA256": {},
	}
	var invalid []string
	for _, name := range names {
		if _, isTLS13 := tls13Names[name]; isTLS13 {
			invalid = append(invalid, name)
		}
	}
	if len(invalid) > 0 {
		return fmt.Errorf("TLS cipher_suites includes TLS 1.3 suites which are not configurable and should be removed: %s", strings.Join(invalid, ", "))
	}
	return nil
}

// validateTLSMinVersion checks the configured minimum TLS version.
// Empty means defaulting at runtime; only "1.2" and "1.3" are accepted.
func validateTLSMinVersion(min string) error {
	if min == "" {
		return nil
	}
	switch min {
	case "1.2", "1.3":
		return nil
	default:
		return fmt.Errorf("TLS min_version must be one of: 1.2, 1.3")
	}
}

func validateRateLimitConfig(rl *RateLimitConfig) error {
	if !rl.Enabled {
		return nil
	}

	validBackends := []string{"redis", "memory"}
	if rl.Backend != "" {
		found := slices.Contains(validBackends, rl.Backend)
		if !found {
			return fmt.Errorf("rate limit backend must be one of: %s", strings.Join(validBackends, ", "))
		}
	}

	validStrategies := []string{"token_bucket", "fixed_window"}
	if rl.Strategy != "" {
		found := slices.Contains(validStrategies, rl.Strategy)
		if !found {
			return fmt.Errorf("rate limit strategy must be one of: %s", strings.Join(validStrategies, ", "))
		}
	}

	// Validate defaults
	if err := validateRateLimitDefaults(rl); err != nil {
		return err
	}

	// Validate operation-specific rate limits
	if err := validateRateLimitOperations(rl.Operations); err != nil {
		return err
	}

	return nil
}

// validateRateLimitDefaults validates the default rate limit configuration
func validateRateLimitDefaults(rl *RateLimitConfig) error {
	// At least one rate limit must be specified
	if rl.Defaults.RequestsPerMinute <= 0 && rl.Defaults.RequestsPerHour <= 0 && rl.Defaults.RequestsPerDay <= 0 {
		return fmt.Errorf("rate limit defaults must have at least one positive rate limit (requests_per_minute, requests_per_hour, or requests_per_day)")
	}

	// Individual validations for each rate limit type
	if rl.Defaults.RequestsPerMinute < 0 {
		return fmt.Errorf("rate limit defaults requests_per_minute must be non-negative")
	}
	if rl.Defaults.RequestsPerHour < 0 {
		return fmt.Errorf("rate limit defaults requests_per_hour must be non-negative")
	}
	if rl.Defaults.RequestsPerDay < 0 {
		return fmt.Errorf("rate limit defaults requests_per_day must be non-negative")
	}

	if rl.Defaults.Window != "" {
		if _, err := time.ParseDuration(rl.Defaults.Window); err != nil {
			return fmt.Errorf("rate limit defaults invalid window format: %w", err)
		}
	}
	return nil
}

// validateRateLimitOperations validates the operation-specific rate limit configuration
func validateRateLimitOperations(operations map[string]RateLimitOperation) error {
	for opName, opLimit := range operations {
		if opLimit.Enabled {
			if err := validateRateLimitOperationEnabled(opName, opLimit); err != nil {
				return err
			}
		}
		if opLimit.Window != "" {
			if _, err := time.ParseDuration(opLimit.Window); err != nil {
				return fmt.Errorf("rate limit operation '%s' invalid window format: %w", opName, err)
			}
		}
	}
	return nil
}

// validateRateLimitOperationEnabled validates an enabled operation's rate limits
func validateRateLimitOperationEnabled(opName string, opLimit RateLimitOperation) error {
	// At least one rate limit must be specified if enabled
	hasPositiveLimit := opLimit.RequestsPerMinute != nil && *opLimit.RequestsPerMinute > 0 ||
		opLimit.RequestsPerHour != nil && *opLimit.RequestsPerHour > 0 ||
		opLimit.RequestsPerDay != nil && *opLimit.RequestsPerDay > 0

	if !hasPositiveLimit {
		return fmt.Errorf("rate limit operation '%s' must have at least one positive rate limit (requests_per_minute, requests_per_hour, or requests_per_day) if enabled", opName)
	}

	// Individual validations for each rate limit type
	if err := validateRateLimitOperationValues(opName, opLimit); err != nil {
		return err
	}

	return nil
}

// validateRateLimitOperationValues validates the individual rate limit values
func validateRateLimitOperationValues(opName string, opLimit RateLimitOperation) error {
	if opLimit.RequestsPerMinute != nil && *opLimit.RequestsPerMinute < 0 {
		return fmt.Errorf("rate limit operation '%s' requests_per_minute must be non-negative", opName)
	}
	if opLimit.RequestsPerHour != nil && *opLimit.RequestsPerHour < 0 {
		return fmt.Errorf("rate limit operation '%s' requests_per_hour must be non-negative", opName)
	}
	if opLimit.RequestsPerDay != nil && *opLimit.RequestsPerDay < 0 {
		return fmt.Errorf("rate limit operation '%s' requests_per_day must be non-negative", opName)
	}
	return nil
}

// validateLoggingConfig validates the logging configuration
func validateLoggingConfig(logging *LoggingConfig) error {
	validLevels := []string{"debug", "info", "warn", "error"}
	if logging.Level != "" {
		found := slices.Contains(validLevels, logging.Level)
		if !found {
			return fmt.Errorf("logging level must be one of: %s", strings.Join(validLevels, ", "))
		}
	}

	validFormats := []string{"json", "text"}
	if logging.Format != "" {
		found := slices.Contains(validFormats, logging.Format)
		if !found {
			return fmt.Errorf("logging format must be one of: %s", strings.Join(validFormats, ", "))
		}
	}

	return nil
}

// validateObservabilityConfig validates the observability configuration
func validateObservabilityConfig(obs *ObservabilityConfig) error {
	if err := validateTracing(obs); err != nil {
		return err
	}

	if err := validateMetrics(obs); err != nil {
		return err
	}

	if err := validateLogging(obs); err != nil {
		return err
	}

	// Validate service information - only require service_name if observability features are enabled
	if (obs.TracingEnabled || obs.MetricsEnabled || obs.LoggingEnabled) && obs.ServiceName == "" {
		return fmt.Errorf("service_name is required when tracing, metrics, or logging are enabled")
	}

	return nil
}

func validateTracing(obs *ObservabilityConfig) error {
	if !obs.TracingEnabled {
		return nil
	}

	validTraceExporters := []string{"otlp", "file"}
	if obs.TraceExporter == "" {
		return fmt.Errorf("trace_exporter is required when tracing is enabled")
	}
	if !slices.Contains(validTraceExporters, obs.TraceExporter) {
		return fmt.Errorf("trace exporter must be one of: %s", strings.Join(validTraceExporters, ", "))
	}

	switch obs.TraceExporter {
	case "otlp":
		if err := validateOTLPTraceConfig(obs); err != nil {
			return fmt.Errorf("OTLP trace config validation failed: %w", err)
		}
	case "file":
		if obs.TraceFilePath == "" {
			return fmt.Errorf("trace_file_path is required when using file trace exporter")
		}
	}

	return nil
}

func validateMetrics(obs *ObservabilityConfig) error {
	if !obs.MetricsEnabled {
		return nil
	}

	validMetricsExporters := []string{"otlp", "file"}
	if obs.MetricsExporter == "" {
		return fmt.Errorf("metrics_exporter is required when metrics are enabled")
	}
	if !slices.Contains(validMetricsExporters, obs.MetricsExporter) {
		return fmt.Errorf("metrics exporter must be one of: %s", strings.Join(validMetricsExporters, ", "))
	}

	switch obs.MetricsExporter {
	case "otlp":
		if err := validateOTLPMetricsConfig(obs); err != nil {
			return fmt.Errorf("OTLP metrics config validation failed: %w", err)
		}
	case "file":
		if obs.MetricsFilePath == "" {
			return fmt.Errorf("metrics_file_path is required when using file metrics exporter")
		}
	}

	return nil
}

func validateLogging(obs *ObservabilityConfig) error {
	if !obs.LoggingEnabled {
		return nil
	}

	validLogExporters := []string{"otlp", "file", "console"}
	if obs.LogExporter == "" {
		return fmt.Errorf("log_exporter is required when logging is enabled")
	}
	if !slices.Contains(validLogExporters, obs.LogExporter) {
		return fmt.Errorf("log exporter must be one of: %s", strings.Join(validLogExporters, ", "))
	}

	switch obs.LogExporter {
	case "otlp":
		if err := validateOTLPLogConfig(obs); err != nil {
			return fmt.Errorf("OTLP log config validation failed: %w", err)
		}
	case "file":
		if obs.LogFilePath == "" {
			return fmt.Errorf("log_file_path is required when using file log exporter")
		}
	}

	return nil
}

func validateOTLPTraceConfig(obs *ObservabilityConfig) error {
	// Validate protocol
	validProtocols := []string{"grpc", "http"}
	if obs.OTLPTraceProtocol != "" {
		found := slices.Contains(validProtocols, obs.OTLPTraceProtocol)
		if !found {
			return fmt.Errorf("OTLP trace protocol must be one of: %s", strings.Join(validProtocols, ", "))
		}
	}

	// Validate endpoint is provided
	if obs.OTLPTraceEndpoint == "" {
		return fmt.Errorf("otlp_trace_endpoint is required when using OTLP trace exporter")
	}

	// Note: URL path is optional for HTTP protocol - defaults to "/v1/traces" if not specified

	return nil
}

func validateOTLPMetricsConfig(obs *ObservabilityConfig) error {
	// Validate protocol
	validProtocols := []string{"grpc", "http"}
	if obs.OTLPMetricsProtocol != "" {
		found := slices.Contains(validProtocols, obs.OTLPMetricsProtocol)
		if !found {
			return fmt.Errorf("OTLP metrics protocol must be one of: %s", strings.Join(validProtocols, ", "))
		}
	}

	// Validate endpoint is provided
	if obs.OTLPMetricsEndpoint == "" {
		return fmt.Errorf("otlp_metrics_endpoint is required when using OTLP metrics exporter")
	}

	// Note: URL path is optional for HTTP protocol - defaults to "/v1/metrics" if not specified

	return nil
}

func validateOTLPLogConfig(obs *ObservabilityConfig) error {
	// Validate protocol
	validProtocols := []string{"grpc", "http"}
	if obs.OTLPLogProtocol != "" {
		found := slices.Contains(validProtocols, obs.OTLPLogProtocol)
		if !found {
			return fmt.Errorf("OTLP log protocol must be one of: %s", strings.Join(validProtocols, ", "))
		}
	}

	// Validate endpoint is provided
	if obs.OTLPLogEndpoint == "" {
		return fmt.Errorf("otlp_log_endpoint is required when using OTLP log exporter")
	}

	// Note: URL path is optional for HTTP protocol - defaults to "/v1/logs" if not specified

	return nil
}

func validateProviderConfig(presetName string, preset *PresetConfig, fullConfig *Config) error {
	switch preset.Provider {
	case "genai-gemini", "genai-vertexai":
		return validateGenAIConfig(presetName, preset, fullConfig)
	case "openai":
		return validateOpenAIConfig(presetName, preset, fullConfig)
	default:
		return fmt.Errorf("preset '%s': unsupported provider: %s", presetName, preset.Provider)
	}
}
