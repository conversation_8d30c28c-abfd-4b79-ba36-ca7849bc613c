package backends

import (
	"context"
	"testing"
	"time"
)

func createRedisBackend(t *testing.T) Backend {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	return backend
}

// These tests require a running Redis instance

func getRedisConfig() BackendConfig {
	return BackendConfig{
		Type:          "redis",
		RedisAddress:  "localhost:6379",
		RedisPassword: "",
		RedisDB:       1, // Use DB 1 for testing
		RedisPoolSize: 5,
	}
}

func TestNewRedisBackend(t *testing.T) {
	config := getRedisConfig()

	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	if backend == nil {
		t.Error("Expected backend to be created")
	}

	// Test ping
	ctx := context.Background()
	err = backend.Ping(ctx)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected ping to succeed, got: %v", err)
	}
}

func TestNewRedisBackend_InvalidAddress(t *testing.T) {
	config := BackendConfig{
		Type:         "redis",
		RedisAddress: "invalid:99999",
		RedisDB:      1,
	}

	_, err := NewRedisBackend(config)
	if err == nil {
		t.Error("Expected error for invalid Redis address")
	}
}

func TestNewRedisBackend_EmptyAddress(t *testing.T) {
	config := BackendConfig{
		Type:         "redis",
		RedisAddress: "",
	}

	_, err := NewRedisBackend(config)
	if err == nil {
		t.Error("Expected error for empty Redis address")
	}

	expectedErr := "redis address cannot be empty"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestRedisBackend_GetSet(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-get-set"

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// Test Get on non-existent key
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for non-existent key, got: %v", err)
	}
	if data != nil {
		t.Error("Expected nil data for non-existent key")
	}

	// Test Set
	now := time.Now()
	testData := &BackendData{
		Count:       5,
		WindowStart: now,
		LastRequest: now,
		Tokens:      10.5,
		LastRefill:  now,
		Metadata:    map[string]any{"test": "value", "number": float64(42)},
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Test Get on existing key
	retrievedData, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if retrievedData == nil {
		t.Fatal("Expected data to be retrieved")
	}

	if retrievedData.Count != 5 {
		t.Errorf("Expected count 5, got: %d", retrievedData.Count)
	}
	if retrievedData.Tokens != 10.5 {
		t.Errorf("Expected tokens 10.5, got: %f", retrievedData.Tokens)
	}
	if retrievedData.Metadata["test"] != "value" {
		t.Errorf("Expected metadata test=value, got: %v", retrievedData.Metadata["test"])
	}
	if retrievedData.Metadata["number"] != float64(42) {
		t.Errorf("Expected metadata number=42, got: %v", retrievedData.Metadata["number"])
	}

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_SetNilData(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	err = backend.Set(ctx, "test-nil", nil, time.Minute)
	if err == nil {
		t.Error("Expected error for nil data")
	}

	expectedErr := "data cannot be nil"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestRedisBackend_Delete(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-delete"

	// Set some data
	testData := &BackendData{
		Count:       5,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Verify data exists
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if data == nil {
		t.Error("Expected data to exist")
	}

	// Delete the data
	err = backend.Delete(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Delete, got: %v", err)
	}

	// Verify data is gone
	data, err = backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get after delete, got: %v", err)
	}
	if data != nil {
		t.Error("Expected data to be deleted")
	}
}

func TestRedisBackend_GetStats(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	stats, err := backend.GetStats(ctx)
	if err != nil {
		t.Errorf("Expected no error for GetStats, got: %v", err)
	}

	if stats == nil {
		t.Fatal("Expected stats to be returned")
	}

	if stats.Address != config.RedisAddress {
		t.Errorf("Expected address %s, got: %s", config.RedisAddress, stats.Address)
	}

	if stats.DB != config.RedisDB {
		t.Errorf("Expected DB %d, got: %d", config.RedisDB, stats.DB)
	}

	if stats.PoolSize != config.RedisPoolSize {
		t.Errorf("Expected pool size %d, got: %d", config.RedisPoolSize, stats.PoolSize)
	}
}

func TestRedisBackend_CheckAndIncrement(t *testing.T) {
	backend := createRedisBackend(t)
	defer backend.Close()

	ctx := context.Background()
	key := "test-check-increment"
	window := time.Minute
	limit := int64(3)

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// First request - should succeed
	count, incremented, err := backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 1, true, "First request")

	// Second request - should succeed
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 2, true, "Second request")

	// Third request - should succeed (at limit)
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 3, true, "Third request")

	// Fourth request - should fail (exceeds limit)
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 3, false, "Fourth request")

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_CheckAndIncrement_ZeroLimit(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-check-increment-zero"
	window := time.Minute
	limit := int64(0)

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// Request with zero limit - should fail
	count, incremented, err := backend.Increment(ctx, key, window, limit)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if incremented {
		t.Error("Expected request to not be incremented with zero limit")
	}
	if count != 0 {
		t.Errorf("Expected count 0, got: %d", count)
	}

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_CheckAndIncrement_WindowReset(t *testing.T) {
	backend := createRedisBackend(t)
	defer backend.Close()

	ctx := context.Background()
	key := "test-check-increment-window"
	window := 2 * time.Second
	limit := int64(2)

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// First request
	count, incremented, err := backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 1, true, "First request")

	// Second request - should reach limit
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 2, true, "Second request")

	// Third request - should fail (at limit)
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 2, false, "Third request")

	// Wait for window to expire
	time.Sleep(3 * time.Second)

	// Request after window reset - should succeed
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 1, true, "After window reset")

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_CheckAndConsumeToken(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-check-consume-token"
	bucketSize := int64(5)
	refillRate := time.Second
	refillAmount := int64(1)
	window := time.Minute

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// First request - should succeed with full bucket minus one
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error for first request, got: %v", err)
	}
	if !consumed {
		t.Error("Expected first request to consume token")
	}
	if tokens != 4.0 {
		t.Errorf("Expected 4 tokens remaining, got: %f", tokens)
	}
	if requests != 1 {
		t.Errorf("Expected 1 request, got: %d", requests)
	}

	// Second request - should succeed
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error for second request, got: %v", err)
	}
	if !consumed {
		t.Error("Expected second request to consume token")
	}
	if tokens != 3.0 {
		t.Errorf("Expected 3 tokens remaining, got: %f", tokens)
	}
	if requests != 2 {
		t.Errorf("Expected 2 requests, got: %d", requests)
	}

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_CheckAndConsumeToken_ZeroBucket(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-check-consume-zero"
	bucketSize := int64(0)
	refillRate := time.Second
	refillAmount := int64(1)
	window := time.Minute

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// Request with zero bucket size - should fail
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if consumed {
		t.Error("Expected request to not consume token with zero bucket")
	}
	if tokens != 0.0 {
		t.Errorf("Expected 0 tokens, got: %f", tokens)
	}
	if requests != 0 {
		t.Errorf("Expected 0 requests, got: %d", requests)
	}

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_CheckAndConsumeToken_TokenRefill(t *testing.T) {
	backend := createRedisBackend(t)
	defer backend.Close()

	ctx := context.Background()
	key := "test-check-consume-refill"
	bucketSize := int64(2)
	refillRate := 2 * time.Second
	refillAmount := int64(1)
	window := time.Minute

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// First request - consume one token
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	assertConsumeTokenResult(t, tokens, consumed, requests, err, 1.0, true, 1, "First request")

	// Second request - consume another token
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	assertConsumeTokenResult(t, tokens, consumed, requests, err, 0.0, true, 2, "Second request")

	// Third request - should fail (no tokens)
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	assertConsumeTokenResult(t, tokens, consumed, requests, err, 0.0, false, 2, "Third request")

	// Wait for token refill
	time.Sleep(3 * time.Second)

	// Request after refill - should succeed
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("After refill: Expected no error, got: %v", err)
	}
	if !consumed {
		t.Error("After refill: Expected request to succeed")
	}
	if tokens < 0.0 || tokens > 1.0 {
		t.Errorf("After refill: Expected tokens between 0 and 1, got: %f", tokens)
	}
	if requests != 3 {
		t.Errorf("After refill: Expected 3 requests, got: %d", requests)
	}

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_CheckAndConsumeToken_ExhaustBucket(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-check-consume-exhaust"
	bucketSize := int64(3)
	refillRate := time.Hour // Very slow refill
	refillAmount := int64(1)
	window := time.Minute

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// Consume all tokens
	for i := int64(1); i <= bucketSize; i++ {
		tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
		if err != nil {
			t.Errorf("Expected no error for request %d, got: %v", i, err)
		}
		if !consumed {
			t.Errorf("Expected request %d to consume token", i)
		}
		expectedTokens := float64(bucketSize - i)
		if tokens != expectedTokens {
			t.Errorf("Expected %f tokens after request %d, got: %f", expectedTokens, i, tokens)
		}
		if requests != i {
			t.Errorf("Expected %d requests after request %d, got: %d", i, i, requests)
		}
	}

	// Next request should fail (bucket exhausted)
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error for exhausted bucket request, got: %v", err)
	}
	if consumed {
		t.Error("Expected request to fail when bucket is exhausted")
	}
	if tokens != 0.0 {
		t.Errorf("Expected 0 tokens when exhausted, got: %f", tokens)
	}
	if requests != bucketSize {
		t.Errorf("Expected %d requests when exhausted, got: %d", bucketSize, requests)
	}

	// Clean up
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key after test: %v", err)
	}
}

func TestRedisBackend_TTL(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-ttl"

	// Clean up before test
	if err := backend.Delete(ctx, key); err != nil {
		t.Errorf("Failed to delete key before test: %v", err)
	}

	// Set data with short TTL
	testData := &BackendData{
		Count:       1,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	err = backend.Set(ctx, key, testData, 2*time.Second)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Verify data exists
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if data == nil {
		t.Error("Expected data to exist")
	}

	// Wait for TTL to expire
	time.Sleep(3 * time.Second)

	// Verify data is gone
	data, err = backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get after TTL, got: %v", err)
	}
	if data != nil {
		t.Error("Expected data to be expired")
	}
}
