package backends

import "testing"

func assertIncrementResult(t *testing.T, count int64, incremented bool, err error, expectedCount int64, expectedIncremented bool, step string) {
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%s: Expected no error, got: %v", step, err)
	}
	if incremented != expectedIncremented {
		t.<PERSON><PERSON><PERSON>("%s: Expected incremented=%v, got: %v", step, expectedIncremented, incremented)
	}
	if count != expectedCount {
		t.<PERSON><PERSON><PERSON>("%s: Expected count %d, got: %d", step, expectedCount, count)
	}
}

func assertConsumeTokenResult(t *testing.T, tokens float64, consumed bool, requests int64, err error, expectedTokens float64, expectedConsumed bool, expectedRequests int64, step string) {
	if err != nil {
		t.<PERSON><PERSON>rf("%s: Expected no error, got: %v", step, err)
	}
	if consumed != expectedConsumed {
		t.<PERSON><PERSON><PERSON>("%s: Expected consumed=%v, got: %v", step, expectedConsumed, consumed)
	}
	if requests != expectedRequests {
		t.<PERSON><PERSON><PERSON>("%s: Expected requests %d, got: %d", step, expectedRequests, requests)
	}
	if expectedTokens >= 0 && tokens != expectedTokens {
		t.Errorf("%s: Expected tokens %f, got: %f", step, expectedTokens, tokens)
	}
}
