package backends

import (
	"context"
	"testing"
	"time"
)

func createMemoryBackend(t *testing.T) Backend {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	return backend
}

func TestNewMemoryBackend(t *testing.T) {
	config := BackendConfig{
		Type:            "memory",
		CleanupInterval: 1 * time.Minute,
		MaxKeys:         1000,
	}

	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.<PERSON>rrorf("Expected no error, got: %v", err)
	}

	if backend == nil {
		t.Error("Expected backend to be created")
	}

	stats := backend.GetStats()
	if stats.MaxKeys != 1000 {
		t.<PERSON><PERSON><PERSON>("Expected max keys 1000, got: %d", stats.MaxKeys)
	}

	if stats.CleanupInterval != 1*time.Minute {
		t.<PERSON><PERSON><PERSON>("Expected cleanup interval 1 minute, got: %v", stats.CleanupInterval)
	}

	// Clean up
	backend.Close()
}

func TestNewMemoryBackend_Defaults(t *testing.T) {
	config := BackendConfig{Type: "memory"}

	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	stats := backend.GetStats()
	if stats.MaxKeys != 10000 {
		t.Errorf("Expected default max keys 10000, got: %d", stats.MaxKeys)
	}

	if stats.CleanupInterval != 5*time.Minute {
		t.Errorf("Expected default cleanup interval 5 minutes, got: %v", stats.CleanupInterval)
	}

	// Clean up
	backend.Close()
}

func TestMemoryBackend_GetSet(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"

	// Test Get on non-existent key
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for non-existent key, got: %v", err)
	}
	if data != nil {
		t.Error("Expected nil data for non-existent key")
	}

	// Test Set
	now := time.Now()
	testData := &BackendData{
		Count:       5,
		WindowStart: now,
		LastRequest: now,
		Tokens:      10.5,
		LastRefill:  now,
		Metadata:    map[string]any{"test": "value"},
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Test Get on existing key
	retrievedData, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if retrievedData == nil {
		t.Fatal("Expected data to be retrieved")
	}

	if retrievedData.Count != 5 {
		t.Errorf("Expected count 5, got: %d", retrievedData.Count)
	}
	if retrievedData.Tokens != 10.5 {
		t.Errorf("Expected tokens 10.5, got: %f", retrievedData.Tokens)
	}
	if retrievedData.Metadata["test"] != "value" {
		t.Errorf("Expected metadata test=value, got: %v", retrievedData.Metadata["test"])
	}
}

func TestMemoryBackend_SetNilData(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	err = backend.Set(ctx, "test-key", nil, time.Minute)
	if err == nil {
		t.Error("Expected error for nil data")
	}

	expectedErr := "data cannot be nil"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestMemoryBackend_Delete(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"

	// Set some data
	testData := &BackendData{
		Count:       5,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Verify data exists
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if data == nil {
		t.Error("Expected data to exist")
	}

	// Delete the data
	err = backend.Delete(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Delete, got: %v", err)
	}

	// Verify data is gone
	data, err = backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get after delete, got: %v", err)
	}
	if data != nil {
		t.Error("Expected data to be deleted")
	}
}

func TestMemoryBackend_MaxKeys(t *testing.T) {
	config := BackendConfig{
		Type:    "memory",
		MaxKeys: 2, // Very small limit for testing
	}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	testData := &BackendData{
		Count:       1,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	// Add first key - should succeed
	err = backend.Set(ctx, "key1", testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for first key, got: %v", err)
	}

	// Add second key - should succeed
	err = backend.Set(ctx, "key2", testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for second key, got: %v", err)
	}

	// Add third key - should fail due to capacity
	err = backend.Set(ctx, "key3", testData, time.Minute)
	if err == nil {
		t.Error("Expected error for third key due to capacity limit")
	}

	// Update existing key - should succeed
	err = backend.Set(ctx, "key1", testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for updating existing key, got: %v", err)
	}
}

func TestMemoryBackend_CheckAndIncrement(t *testing.T) {
	backend := createMemoryBackend(t)
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	window := time.Minute
	limit := int64(3)

	// First request - should succeed
	count, incremented, err := backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 1, true, "First request")

	// Second request - should succeed
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 2, true, "Second request")

	// Third request - should succeed (at limit)
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 3, true, "Third request")

	// Fourth request - should fail (exceeds limit)
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 3, false, "Fourth request")
}

func TestMemoryBackend_CheckAndIncrement_ZeroLimit(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	window := time.Minute
	limit := int64(0)

	// Request with zero limit - should fail
	count, incremented, err := backend.Increment(ctx, key, window, limit)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if incremented {
		t.Error("Expected request to not be incremented with zero limit")
	}
	if count != 0 {
		t.Errorf("Expected count 0, got: %d", count)
	}
}

func TestMemoryBackend_CheckAndIncrement_WindowReset(t *testing.T) {
	backend := createMemoryBackend(t)
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	window := 100 * time.Millisecond
	limit := int64(2)

	// First request
	count, incremented, err := backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 1, true, "First request")

	// Second request - should reach limit
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 2, true, "Second request")

	// Third request - should fail (at limit)
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 2, false, "Third request")

	// Wait for window to expire
	time.Sleep(150 * time.Millisecond)

	// Request after window reset - should succeed
	count, incremented, err = backend.Increment(ctx, key, window, limit)
	assertIncrementResult(t, count, incremented, err, 1, true, "After window reset")
}

func TestMemoryBackend_CheckAndIncrement_MaxKeys(t *testing.T) {
	config := BackendConfig{
		Type:    "memory",
		MaxKeys: 1,
	}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	window := time.Minute
	limit := int64(5)

	// First key - should succeed
	count, incremented, err := backend.Increment(ctx, "key1", window, limit)
	if err != nil {
		t.Errorf("Expected no error for first key, got: %v", err)
	}
	if !incremented || count != 1 {
		t.Errorf("Expected first key to succeed, got count=%d, incremented=%v", count, incremented)
	}

	// Second key - should fail due to capacity
	count, incremented, err = backend.Increment(ctx, "key2", window, limit)
	if err == nil {
		t.Error("Expected error for second key due to capacity limit")
	}
	if incremented {
		t.Error("Expected second key to not be incremented")
	}
	if count != 0 {
		t.Errorf("Expected count 0 for failed request, got: %d", count)
	}
}

func TestMemoryBackend_CheckAndConsumeToken(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	bucketSize := int64(5)
	refillRate := time.Second
	refillAmount := int64(1)
	window := time.Minute

	// First request - should succeed with full bucket minus one
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error for first request, got: %v", err)
	}
	if !consumed {
		t.Error("Expected first request to consume token")
	}
	if tokens != 4.0 {
		t.Errorf("Expected 4 tokens remaining, got: %f", tokens)
	}
	if requests != 1 {
		t.Errorf("Expected 1 request, got: %d", requests)
	}

	// Second request - should succeed
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error for second request, got: %v", err)
	}
	if !consumed {
		t.Error("Expected second request to consume token")
	}
	if tokens != 3.0 {
		t.Errorf("Expected 3 tokens remaining, got: %f", tokens)
	}
	if requests != 2 {
		t.Errorf("Expected 2 requests, got: %d", requests)
	}
}

func TestMemoryBackend_CheckAndConsumeToken_ZeroBucket(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	bucketSize := int64(0)
	refillRate := time.Second
	refillAmount := int64(1)
	window := time.Minute

	// Request with zero bucket size - should fail
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if consumed {
		t.Error("Expected request to not consume token with zero bucket")
	}
	if tokens != 0.0 {
		t.Errorf("Expected 0 tokens, got: %f", tokens)
	}
	if requests != 0 {
		t.Errorf("Expected 0 requests, got: %d", requests)
	}
}

func TestMemoryBackend_CheckAndConsumeToken_TokenRefill(t *testing.T) {
	backend := createMemoryBackend(t)
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	bucketSize := int64(2)
	refillRate := 100 * time.Millisecond
	refillAmount := int64(1)
	window := time.Minute

	// First request - consume one token
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	assertConsumeTokenResult(t, tokens, consumed, requests, err, 1.0, true, 1, "First request")

	// Second request - consume another token
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	assertConsumeTokenResult(t, tokens, consumed, requests, err, 0.0, true, 2, "Second request")

	// Third request - should fail (no tokens)
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	assertConsumeTokenResult(t, tokens, consumed, requests, err, 0.0, false, 2, "Third request")

	// Wait for token refill
	time.Sleep(150 * time.Millisecond)

	// Request after refill - should succeed
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, key, bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("After refill: Expected no error, got: %v", err)
	}
	if !consumed {
		t.Error("After refill: Expected request to succeed")
	}
	if tokens < 0.0 || tokens > 1.0 {
		t.Errorf("After refill: Expected tokens between 0 and 1, got: %f", tokens)
	}
	if requests != 3 {
		t.Errorf("After refill: Expected 3 requests, got: %d", requests)
	}
}

func TestMemoryBackend_CheckAndConsumeToken_MaxKeys(t *testing.T) {
	config := BackendConfig{
		Type:    "memory",
		MaxKeys: 1,
	}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	bucketSize := int64(5)
	refillRate := time.Second
	refillAmount := int64(1)
	window := time.Minute

	// First key - should succeed
	tokens, consumed, requests, err := backend.ConsumeToken(ctx, "key1", bucketSize, refillRate, refillAmount, window)
	if err != nil {
		t.Errorf("Expected no error for first key, got: %v", err)
	}
	if !consumed || tokens != 4.0 || requests != 1 {
		t.Errorf("Expected first key to succeed, got tokens=%f, consumed=%v, requests=%d", tokens, consumed, requests)
	}

	// Second key - should fail due to capacity
	tokens, consumed, requests, err = backend.ConsumeToken(ctx, "key2", bucketSize, refillRate, refillAmount, window)
	if err == nil {
		t.Error("Expected error for second key due to capacity limit")
	}
	if consumed {
		t.Error("Expected second key to not consume token")
	}
	if tokens != 0.0 || requests != 0 {
		t.Errorf("Expected tokens=0, requests=0 for failed request, got tokens=%f, requests=%d", tokens, requests)
	}
}

func TestMemoryBackend_Close(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}

	// Add some data
	ctx := context.Background()
	testData := &BackendData{
		Count:       1,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}
	if err := backend.Set(ctx, "test-key", testData, time.Minute); err != nil {
		t.Fatalf("Failed to set data: %v", err)
	}

	// Close should not error
	if err := backend.Close(); err != nil {
		t.Errorf("Expected no error for Close, got: %v", err)
	}

	// Operations after close should not panic (though behavior is undefined)
	// This is mainly to ensure cleanup goroutine stops properly
}
