# resumatter configuration file example

ai:
  # Provider Presets/Profiles (up to 24 user-defined configurations)
  # Each preset is a complete, reusable provider configuration
  presets:
    # Production-grade Gemini preset for general use
    prod-gemini:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.7
      max_tokens: 8192
      timeout: "30s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"
      provider_config:
        # For provider: "genai-gemini", the following fields are used:
        # - backend: Must be "gemini"
        # - api_key: Your Google AI Gemini API key.
        backend: "gemini"
        api_key: "AIzaSyDUMMY_GEMINI_API_KEY_EXAMPLE_12345"

    # Example of loading the API key from a custom environment variable
    gemini-from-env:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      timeout: "30s"
      provider_config:
        backend: "gemini"
        # The application can expand any environment variable, not just standard ones.
        # This allows for flexible key management, e.g., using different keys for different tiers.
        # The value for api_key will be taken from the GEMINI_API_KEY_FREETIER environment variable.
        api_key: "${GEMINI_API_KEY_FREETIER}"

    # Enterprise Vertex AI preset for mission-critical operations
    enterprise-vertex:
      provider: "genai-vertexai"
      model: "gemini-2.5-pro"
      temperature: 0.3
      max_tokens: 8192
      timeout: "45s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.5
        min_requests: 3
        max_requests: 5
        interval: "120s"
        timeout: "45s"
      provider_config:
        # For provider: "genai-vertexai", the following fields are used:
        # - backend: Must be "vertexai"
        # - google_cloud_project: Your GCP project ID (required).
        # - google_cloud_region: Your GCP region (e.g., "us-east1") (required).
        # - credentials_file: Path to your service account key JSON file.
        # - credentials_json: Alternatively, the content of the service account key JSON.
        # Note: Do not use "api_key" for Vertex AI; it uses service account authentication.
        backend: "vertexai"
        credentials_file: "/path/to/service-account-key.json"
        google_cloud_project: "my-vertex-project-id"
        google_cloud_region: "us-east1"

    # Creative preset optimized for resume tailoring
    creative-gemini:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.9
      max_tokens: 10240
      timeout: "40s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.7
        min_requests: 3
        max_requests: 5
        interval: "45s"
        timeout: "40s"
      provider_config:
        # google_cloud_project and google_cloud_region are not used for the "gemini" backend.
        backend: "gemini"
        api_key: "AIzaSyDUMMY_GEMINI_API_KEY_EXAMPLE_12345"

    # Analytical preset for job analysis with Gemini
    analysis-gemini:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.4
      max_tokens: 8192
      timeout: "40s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 4
        max_requests: 3
        interval: "90s"
        timeout: "40s"
      provider_config:
        backend: "gemini"
        api_key: "AIzaSyDUMMY_GEMINI_API_KEY_EXAMPLE_12345"

    # Development preset with OpenAI for experimentation
    dev-openai:
      provider: "openai"
      model: "gpt-4o"
      temperature: 0.8
      max_tokens: 4096
      timeout: "30s"
      circuit_breaker:
        enabled: false  # Disabled for development
      provider_config:
        # For provider: "openai", the following fields are used:
        # - api_key: Your OpenAI API key.
        # - organization_id: (Optional) Your OpenAI organization ID.
        # - project_id: (Optional) Your OpenAI project ID.
        # - base_url: (Optional) For custom OpenAI-compatible API endpoints.
        api_key: "sk-dummy-openai-api-key-example-12345"
        organization_id: "org-dummy-organization-id"
        project_id: "proj-dummy-project-id"
        base_url: "https://api.openai.com/v1"

    # Example of loading OpenAI credentials from custom environment variables
    openai-from-env:
      provider: "openai"
      model: "gpt-4o"
      timeout: "30s"
      provider_config:
        # You can use any environment variable names for your secrets.
        api_key: "${MY_APP_OPENAI_KEY}"
        organization_id: "${MY_APP_OPENAI_ORG_ID}"

    # Budget-optimized preset for cost-conscious deployments
    budget-lite:
      provider: "genai-gemini"
      model: "gemini-2.0-flash-lite"
      temperature: 0.6
      max_tokens: 4096
      timeout: "20s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.8
        min_requests: 10
        max_requests: 2
        interval: "30s"
        timeout: "20s"
      provider_config:
        backend: "gemini"
        api_key: "AIzaSyDUMMY_GEMINI_API_KEY_EXAMPLE_12345"

    # High-performance preset for demanding workloads
    performance-vertex:
      provider: "genai-vertexai"
      model: "gemini-2.5-pro"
      temperature: 0.5
      max_tokens: 16384
      timeout: "60s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.4
        min_requests: 2
        max_requests: 10
        interval: "180s"
        timeout: "60s"
      provider_config:
        backend: "vertexai"
        credentials_file: "/etc/gcp/high-perf-service-account.json"
        google_cloud_project: "my-high-performance-project"
        google_cloud_region: "us-west1"

    # Testing preset with minimal resources
    testing-lite:
      provider: "genai-gemini"
      model: "gemini-2.0-flash-lite"
      temperature: 0.1
      max_tokens: 2048
      timeout: "15s"
      circuit_breaker:
        enabled: false
      provider_config:
        backend: "gemini"
        api_key: "AIzaSyDUMMY_GEMINI_API_KEY_EXAMPLE_12345"

  # Default preset used when operations don't specify a preset
  default_preset: "prod-gemini"

  # Per-operation configurations using presets
  operations:
    # Resume tailoring - uses creative preset with temperature override
    tailor:
      preset: "creative-gemini"
      # Override preset temperature for even more creativity
      temperature: 0.95

    # Resume evaluation - uses enterprise preset for reliability
    evaluate:
      preset: "enterprise-vertex"
      # Override timeout for complex evaluations
      timeout: "60s"

    # Job analysis - uses analytical preset optimized for analysis
    analyze:
      preset: "analysis-gemini"
      # Override max_tokens for detailed analysis
      max_tokens: 10240

    # Git commit message generation - uses budget-lite preset
    git-commit:
      preset: "budget-lite"

# Server Configuration
server:
  port: "8780"
  environment: "development"
  cors:
    enabled: true       # disable on production, this is a backend service
    allowed_origins:
      - "*"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:
      - "Origin"
      - "Content-Type"
      - "Content-Length"
      - "Accept-Encoding"
      - "X-CSRF-Token"
      - "Authorization"

# TLS Configuration
tls:
  mode: "https"      # off, https, mutual
  min_version: "1.2"
  cipher_suites: []
  cert_file: "certs/server.pem"
  key_file: "certs/server-key.pem"
  ca_file: "certs/ca.pem"

# Authentication Configuration
auth:
  enabled: false
  api_keys:
    - "sk-rsm-prod-key-1"
    - "sk-rsm-prod-key-2"

# Rate Limiting Configuration
rate_limit:
  enabled: true             # always enable to prevent ai cost escalation, even in development
  backend: "memory"         # memory, redis
  strategy: "token_bucket"  # fixed_window, token_bucket
  key_by: "ip"              # ip, api_key, header
  header_name: "X-API-Key"
  
  defaults:
    requests_per_minute: 60
    burst_size: 10
  
  operations:
    tailor:
      enabled: true
      requests_per_minute: 10
      burst_size: 5
    
    evaluate:
      enabled: true
      requests_per_minute: 15
      burst_size: 5
    
    analyze:
      enabled: true
      requests_per_minute: 20
      burst_size: 8
  
  redis:
    address: "localhost:6379"
    password: "dummy-redis-password"
    db: 0
    pool_size: 10

# Logging Configuration
logging:
  level: "info"
  format: "json"

# Observability Configuration
observability:
  service_name: "resumatter"
  service_version: "v1.0.0"
  environment: "production"
  
  tracing_enabled: true
  trace_exporter: "otlp"            # otlp, file
  trace_file_path: "traces.json"
  
  metrics_enabled: true
  metrics_interval: 90s
  metrics_exporter: "otlp"          # otlp, file
  metrics_file_path: "metrics.json"

  logging_enabled: true
  log_exporter: "file"              # otlp, file, console
  log_file_path: "logs.json"
  
  otlp_trace_endpoint: "localhost:4318"
  otlp_trace_protocol: "http"
  otlp_trace_url_path: "/v1/traces"
  otlp_trace_headers:
    x-api-key: "dummy-otlp-api-key-12345"
  otlp_trace_insecure: false
  
  otlp_metrics_endpoint: "localhost:4318"
  otlp_metrics_protocol: "http"
  otlp_metrics_url_path: "/v1/metrics"
  otlp_metrics_headers:
    x-api-key: "dummy-otlp-api-key-12345"
  otlp_metrics_insecure: false

  otlp_log_endpoint: "localhost:4318"
  otlp_log_protocol: "http"
  otlp_log_url_path: "/v1/logs"
  otlp_log_headers:
    x-api-key: "dummy-otlp-api-key-12345"
  otlp_log_insecure: false
