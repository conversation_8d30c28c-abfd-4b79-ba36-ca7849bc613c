package logger

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.opentelemetry.io/otel"
	otelstdout "go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	otelresource "go.opentelemetry.io/otel/sdk/resource"
	oteltrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
)

// logEntry defines the structure of a log entry for JSON parsing in tests.
// It's based on zap's default production encoder keys.
type logEntry struct {
	Level   string `json:"level"`
	Time    string `json:"ts"`
	Message string `json:"msg"`
	Logger  string `json:"logger,omitempty"`
	TraceID string `json:"trace_id,omitempty"`
	SpanID  string `json:"span_id,omitempty"`
	Error   string `json:"error,omitempty"`
}

func TestLogger_Levels(t *testing.T) {
	tests := []struct {
		name      string
		logLevel  Level
		callLevel Level
		shouldLog bool
	}{
		{"debug logs at debug level", LevelDebug, LevelDebug, true},
		{"info logs at debug level", LevelDebug, LevelInfo, true},
		{"warn logs at debug level", LevelDebug, LevelWarn, true},
		{"error logs at debug level", LevelDebug, LevelError, true},
		{"debug doesn't log at info level", LevelInfo, LevelDebug, false},
		{"info logs at info level", LevelInfo, LevelInfo, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			config := &Config{
				Level:  tt.logLevel,
				Format: FormatText,
				Output: &buf,
			}
			logger := New(config)
			ctx := context.Background()

			switch tt.callLevel {
			case LevelDebug:
				logger.Debug(ctx, "test message")
			case LevelInfo:
				logger.Info(ctx, "test message")
			case LevelWarn:
				logger.Warn(ctx, "test message")
			case LevelError:
				logger.Error(ctx, "test message")
			}

			output := buf.String()
			if tt.shouldLog {
				assert.NotEmpty(t, output, "Expected log output but got none")
			} else {
				assert.Empty(t, output, "Expected no log output but got: %s", output)
			}
		})
	}
}

func TestLogger_JSONFormat(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config)
	ctx := context.Background()

	logger.Info(ctx, "test message", String("key", "value"), Int("number", 42))

	var entry map[string]any
	err := json.Unmarshal(buf.Bytes(), &entry)
	require.NoError(t, err, "Failed to parse JSON output")

	assert.Equal(t, "info", entry["level"], "Expected level 'info'")
	assert.Equal(t, "test message", entry["msg"], "Expected correct message")
	assert.Equal(t, "value", entry["key"], "Expected field key=value")
	assert.Equal(t, float64(42), entry["number"], "Expected field number=42")
}

func TestLogger_TextFormat(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatText,
		Output: &buf,
	}
	logger := New(config)
	ctx := context.Background()

	logger.Info(ctx, "test message", String("key", "value"))

	output := buf.String()
	assert.Contains(t, output, "INFO", "Expected INFO in output")
	assert.Contains(t, output, "test message", "Expected 'test message' in output")
	// Zap's development encoder formats fields as JSON at the end of the line.
	assert.Contains(t, output, `{"key": "value"}`, "Expected JSON fields in output")
}

func TestLogger_WithFields(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config).With(String("component", "test"))
	ctx := context.Background()

	logger.Info(ctx, "test message", String("extra", "field"))

	var entry map[string]any
	err := json.Unmarshal(buf.Bytes(), &entry)
	require.NoError(t, err, "Failed to parse JSON output")

	assert.Equal(t, "test", entry["component"], "Expected persistent field component=test")
	assert.Equal(t, "field", entry["extra"], "Expected call field extra=field")
}

func TestLogger_Named(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config).Named("service").Named("component")
	ctx := context.Background()

	logger.Info(ctx, "test message")

	var entry logEntry
	err := json.Unmarshal(buf.Bytes(), &entry)
	require.NoError(t, err, "Failed to parse JSON output")

	assert.Equal(t, "service.component", entry.Logger, "Expected logger name 'service.component'")
}

func TestLogger_ErrorWithErr(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelError,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config)
	ctx := context.Background()

	testErr := errors.New("test error")
	logger.ErrorWithErr(ctx, "operation failed", testErr)

	var entry logEntry
	err := json.Unmarshal(buf.Bytes(), &entry)
	require.NoError(t, err, "Failed to parse JSON output")

	assert.Equal(t, "error", entry.Level)
	assert.Equal(t, "operation failed", entry.Message)
	assert.Equal(t, "test error", entry.Error)
}

func TestLogger_TraceIntegration(t *testing.T) {
	// --- OTel SDK setup for test ---
	exp, err := otelstdout.New(otelstdout.WithPrettyPrint())
	require.NoError(t, err)
	res, err := otelresource.Merge(
		otelresource.Default(),
		otelresource.NewWithAttributes(semconv.SchemaURL, semconv.ServiceName("logger-test")),
	)
	require.NoError(t, err)
	tp := oteltrace.NewTracerProvider(
		oteltrace.WithBatcher(exp),
		oteltrace.WithResource(res),
	)
	defer func() { _ = tp.Shutdown(context.Background()) }()
	otel.SetTracerProvider(tp)
	// --- end OTel SDK setup ---

	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config)

	// Create a tracer and span
	tracer := otel.Tracer("test")
	ctx, span := tracer.Start(context.Background(), "test-operation")
	defer span.End()

	logger.Info(ctx, "test message")

	var entry logEntry
	err = json.Unmarshal(buf.Bytes(), &entry)
	require.NoError(t, err, "Failed to parse JSON output")

	// Check that trace and span IDs are present
	spanCtx := span.SpanContext()
	assert.NotEmpty(t, entry.TraceID, "Expected trace ID to be present")
	assert.NotEmpty(t, entry.SpanID, "Expected span ID to be present")
	assert.Equal(t, spanCtx.TraceID().String(), entry.TraceID, "Trace ID should match span context")
	assert.Equal(t, spanCtx.SpanID().String(), entry.SpanID, "Span ID should match span context")
}

func TestFromContext(t *testing.T) {
	logger := NewDefault()
	ctx := WithLogger(context.Background(), logger)

	retrieved := FromContext(ctx)
	assert.NotNil(t, retrieved, "Expected to retrieve logger from context")

	// Test with context without logger
	emptyCtx := context.Background()
	defaultLogger := FromContext(emptyCtx)
	assert.NotNil(t, defaultLogger, "Expected default logger when none in context")
}

func TestFromContextOrNil(t *testing.T) {
	logger := NewDefault()
	ctx := WithLogger(context.Background(), logger)

	retrieved := FromContextOrNil(ctx)
	assert.NotNil(t, retrieved, "Expected to retrieve logger from context")

	// Test with context without logger
	emptyCtx := context.Background()
	nilLogger := FromContextOrNil(emptyCtx)
	assert.Nil(t, nilLogger, "Expected nil when no logger in context")
}
