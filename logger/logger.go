package logger

import (
	"context"
	"io"
	"os"

	"go.opentelemetry.io/contrib/bridges/otelzap"
	"go.opentelemetry.io/otel/log/global"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Config holds logger configuration
type Config struct {
	Level      Level
	Format     Format
	Output     io.Writer
	EnableOtel bool   // Enable OpenTelemetry logging bridge
	LoggerName string // Name for the OpenTelemetry logger
}

// Format represents the output format
type Format string

const (
	FormatJSON Format = "json"
	FormatText Format = "text"
)

// unifiedLogger is a logger that uses uber-go/zap for logging.
// It satisfies the logger.Logger interface and can optionally forward logs to OpenTelemetry.
type unifiedLogger struct {
	level  zap.AtomicLevel
	logger *zap.Logger
}

// New creates a new logger with the given configuration.
func New(config *Config) Logger {
	if config == nil {
		config = DefaultConfig()
	}

	atomicLevel := zap.NewAtomicLevelAt(toZapLevel(config.Level))

	// Encoder configuration
	encoderConfig := zap.NewProductionEncoderConfig()
	if config.Format == FormatText {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		// Customize console output to be more like the original logger
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
		encoderConfig.ConsoleSeparator = " "
	}
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	var cores []zapcore.Core

	// Standard output core (console/file)
	var encoder zapcore.Encoder
	if config.Format == FormatJSON {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	writer := zapcore.AddSync(config.Output)
	standardCore := zapcore.NewCore(encoder, writer, atomicLevel)
	cores = append(cores, standardCore)

	// OpenTelemetry core (if enabled)
	if config.EnableOtel {
		loggerName := config.LoggerName
		if loggerName == "" {
			loggerName = "github.com/ajiwo/resumatter"
		}

		// Create OpenTelemetry core using otelzap bridge
		otelCore := otelzap.NewCore(loggerName, otelzap.WithLoggerProvider(global.GetLoggerProvider()))
		cores = append(cores, otelCore)
	}

	// Combine cores using tee
	var core zapcore.Core
	if len(cores) == 1 {
		core = cores[0]
	} else {
		core = zapcore.NewTee(cores...)
	}

	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return &unifiedLogger{
		level:  atomicLevel,
		logger: logger,
	}
}

// DefaultConfig returns a default logger configuration.
func DefaultConfig() *Config {
	return &Config{
		Level:      ParseLevel("info"),
		Format:     ParseFormat("text"),
		Output:     os.Stderr,
		EnableOtel: false,
		LoggerName: "github.com/ajiwo/resumatter",
	}
}

// NewDefault creates a logger with default configuration.
func NewDefault() Logger {
	return New(DefaultConfig())
}

// ParseLevel converts a string to a Level, defaulting to LevelInfo if invalid.
func ParseLevel(level string) Level {
	switch level {
	case "debug":
		return LevelDebug
	case "info":
		return LevelInfo
	case "warn":
		return LevelWarn
	case "error":
		return LevelError
	default:
		return LevelInfo
	}
}

// ParseFormat converts a string to a Format, defaulting to FormatText if invalid.
func ParseFormat(format string) Format {
	switch format {
	case "json":
		return FormatJSON
	case "text":
		return FormatText
	default:
		return FormatText
	}
}

func (l *unifiedLogger) Debug(ctx context.Context, msg string, fields ...Field) {
	l.logger.Debug(msg, l.convertFields(ctx, fields...)...)
}

func (l *unifiedLogger) Info(ctx context.Context, msg string, fields ...Field) {
	l.logger.Info(msg, l.convertFields(ctx, fields...)...)
}

func (l *unifiedLogger) Warn(ctx context.Context, msg string, fields ...Field) {
	l.logger.Warn(msg, l.convertFields(ctx, fields...)...)
}

func (l *unifiedLogger) Error(ctx context.Context, msg string, fields ...Field) {
	l.logger.Error(msg, l.convertFields(ctx, fields...)...)
}

func (l *unifiedLogger) ErrorWithErr(ctx context.Context, msg string, err error, fields ...Field) {
	allFields := append(fields, Err(err))
	l.logger.Error(msg, l.convertFields(ctx, allFields...)...)
}

func (l *unifiedLogger) SetLevel(level Level) {
	l.level.SetLevel(toZapLevel(level))
}

func (l *unifiedLogger) GetLevel() Level {
	return fromZapLevel(l.level.Level())
}

func (l *unifiedLogger) With(fields ...Field) Logger {
	zapFields := make([]zap.Field, len(fields))
	for i, f := range fields {
		zapFields[i] = zap.Any(f.Key, f.Value)
	}
	return &unifiedLogger{
		level:  l.level,
		logger: l.logger.With(zapFields...),
	}
}

func (l *unifiedLogger) Named(name string) Logger {
	return &unifiedLogger{
		level:  l.level,
		logger: l.logger.Named(name),
	}
}

// convertFields converts our custom Field type to zap.Field and adds trace info.
func (l *unifiedLogger) convertFields(ctx context.Context, fields ...Field) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields)+2) // +2 for trace/span ID

	// Add OpenTelemetry trace and span IDs if available
	if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
		spanCtx := span.SpanContext()
		zapFields = append(zapFields, zap.String("trace_id", spanCtx.TraceID().String()))
		zapFields = append(zapFields, zap.String("span_id", spanCtx.SpanID().String()))
	}

	for _, f := range fields {
		// Handle the error type specifically for better formatting in zap
		if f.Key == "error" {
			if err, ok := f.Value.(error); ok {
				zapFields = append(zapFields, zap.Error(err))
				continue
			}
		}
		zapFields = append(zapFields, zap.Any(f.Key, f.Value))
	}
	return zapFields
}

// toZapLevel converts our Level to zapcore.Level.
func toZapLevel(level Level) zapcore.Level {
	switch level {
	case LevelDebug:
		return zapcore.DebugLevel
	case LevelInfo:
		return zapcore.InfoLevel
	case LevelWarn:
		return zapcore.WarnLevel
	case LevelError:
		return zapcore.ErrorLevel
	default:
		return zapcore.InfoLevel
	}
}

// fromZapLevel converts zapcore.Level to our Level.
func fromZapLevel(level zapcore.Level) Level {
	switch level {
	case zapcore.DebugLevel:
		return LevelDebug
	case zapcore.InfoLevel:
		return LevelInfo
	case zapcore.WarnLevel:
		return LevelWarn
	case zapcore.ErrorLevel:
		return LevelError
	default:
		// This case might be hit for Panic, DPanic, Fatal
		if level > zapcore.ErrorLevel {
			return LevelError
		}
		return LevelInfo
	}
}
