package util

import "net/url"

// IsValidURL checks if a given string is a valid HTTP or HTTPS URL.
// It parses the string as a URI and ensures it has a scheme (http/https) and a host.
func IsValidURL(s string) bool {
	u, err := url.ParseRequestURI(s)
	if err != nil {
		return false
	}
	// Ensure it has a scheme (http/https) and a host.
	return (u.Scheme == "http" || u.Scheme == "https") && u.Host != ""
}
