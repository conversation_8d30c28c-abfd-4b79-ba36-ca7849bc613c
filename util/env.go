package util

import "os"

// ExpandEnvVarsRecursive traverses a value and expands environment variables in strings.
// It recursively processes maps and slices, expanding any string values that contain
// environment variable references in the format ${VAR_NAME} or $VAR_NAME.
//
// Note: This function uses os.ExpandEnv which supports basic environment variable
// expansion. Default values (like ${VAR:-default}) are a shell feature and are
// NOT supported by os.ExpandEnv. Unset variables expand to empty strings.
//
// Supported types:
//   - string: expands environment variables using os.ExpandEnv
//   - map[string]any: recursively processes all values
//   - []any: recursively processes all elements
//   - other types: returned unchanged
//
// Example:
//
//	// Set environment variables
//	os.Setenv("API_KEY", "secret123")
//	os.Setenv("TIMEOUT", "30s")
//
//	input := map[string]any{
//	  "api_key": "${API_KEY}",           // expands to "secret123"
//	  "config": map[string]any{
//	    "timeout": "${TIMEOUT}",         // expands to "30s"
//	    "host": "${UNSET_VAR}",          // expands to "" (empty string)
//	  },
//	  "hosts": []any{"${API_KEY}", "static"},
//	}
//	expanded := ExpandEnvVarsRecursive(input)
func ExpandEnvVarsRecursive(value any) any {
	switch v := value.(type) {
	case string:
		return os.ExpandEnv(v)
	case map[string]any:
		for key, val := range v {
			v[key] = ExpandEnvVarsRecursive(val)
		}
		return v
	case []any:
		for i, val := range v {
			v[i] = ExpandEnvVarsRecursive(val)
		}
		return v
	default:
		return v
	}
}
