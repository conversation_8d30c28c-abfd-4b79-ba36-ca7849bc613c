package util

import "testing"

func TestMaskAPI<PERSON>ey(t *testing.T) {
	tests := []struct {
		name     string
		api<PERSON><PERSON>   string
		expected string
	}{
		{
			name:     "empty api key",
			api<PERSON>ey:   "",
			expected: "(not set)",
		},
		{
			name:     "short api key",
			apiKey:   "12345678",
			expected: "***",
		},
		{
			name:     "short api key 2",
			apiKey:   "1234",
			expected: "***",
		},
		{
			name:     "valid api key",
			apiKey:   "1234567890123456",
			expected: "1234***3456",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MaskAPIKey(tt.apiKey); got != tt.expected {
				t.<PERSON>("MaskAPIKey() = %v, want %v", got, tt.expected)
			}
		})
	}
}
