package util_test

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/ajiwo/resumatter/util"
)

func TestExpandEnvVarsRecursive(t *testing.T) {
	// Set up environment variables for testing
	os.Setenv("TEST_VAR_1", "value1")
	os.Setenv("TEST_VAR_2", "value2")
	os.Unsetenv("TEST_VAR_UNSET")

	testCases := []struct {
		name     string
		input    any
		expected any
	}{
		{
			name:     "string with single env var",
			input:    "key: ${TEST_VAR_1}",
			expected: "key: value1",
		},
		{
			name:     "string with multiple env vars",
			input:    "${TEST_VAR_1}-${TEST_VAR_2}",
			expected: "value1-value2",
		},
		{
			name:     "string with unset env var",
			input:    "key: ${TEST_VAR_UNSET}",
			expected: "key: ",
		},
		{
			name: "map with string values",
			input: map[string]any{
				"key1": "${TEST_VAR_1}",
				"key2": "static",
				"key3": "${TEST_VAR_UNSET}",
			},
			expected: map[string]any{
				"key1": "value1",
				"key2": "static",
				"key3": "",
			},
		},
		{
			name: "slice with string values",
			input: []any{
				"${TEST_VAR_1}",
				"static",
				"${TEST_VAR_UNSET}",
			},
			expected: []any{
				"value1",
				"static",
				"",
			},
		},
		{
			name: "nested map",
			input: map[string]any{
				"level1": map[string]any{
					"key1": "${TEST_VAR_1}",
					"key2": "static",
				},
			},
			expected: map[string]any{
				"level1": map[string]any{
					"key1": "value1",
					"key2": "static",
				},
			},
		},
		{
			name: "nested slice",
			input: []any{
				[]any{"${TEST_VAR_1}", "static"},
			},
			expected: []any{
				[]any{"value1", "static"},
			},
		},
		{
			name: "map with nested slice",
			input: map[string]any{
				"data": []any{
					"${TEST_VAR_1}",
					map[string]any{"key": "${TEST_VAR_2}"},
				},
			},
			expected: map[string]any{
				"data": []any{
					"value1",
					map[string]any{"key": "value2"},
				},
			},
		},
		{
			name:     "unsupported type int",
			input:    123,
			expected: 123,
		},
		{
			name:     "unsupported type bool",
			input:    true,
			expected: true,
		},
		{
			name:     "nil value",
			input:    nil,
			expected: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := util.ExpandEnvVarsRecursive(tc.input)
			assert.Equal(t, tc.expected, actual)
		})
	}
}
