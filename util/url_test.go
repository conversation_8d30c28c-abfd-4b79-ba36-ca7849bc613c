package util

import "testing"

func TestIsValidURL(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "valid http url",
			input:    "http://example.com",
			expected: true,
		},
		{
			name:     "valid https url",
			input:    "https://example.com",
			expected: true,
		},
		{
			name:     "invalid url scheme",
			input:    "ftp://example.com",
			expected: false,
		},
		{
			name:     "url without scheme",
			input:    "example.com",
			expected: false,
		},
		{
			name:     "empty string",
			input:    "",
			expected: false,
		},
		{
			name:     "just http",
			input:    "http://",
			expected: false,
		},
		{
			name:     "just https",
			input:    "https://",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if got := IsValidURL(tc.input); got != tc.expected {
				t.<PERSON>rrorf("IsValidURL(%q) = %v; want %v", tc.input, got, tc.expected)
			}
		})
	}
}
