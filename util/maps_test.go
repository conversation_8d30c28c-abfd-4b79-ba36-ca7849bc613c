package util

import (
	"reflect"
	"testing"
)

func TestCopyMetadata(t *testing.T) {
	t.Run("nil map", func(t *testing.T) {
		copiedMap := CopyMetadata(nil)
		if copiedMap == nil {
			t.<PERSON><PERSON><PERSON>("CopyMetadata(nil) got nil, want non-nil empty map")
		}
		if len(copiedMap) != 0 {
			t.<PERSON><PERSON><PERSON>("CopyMetadata(nil) got map with length %d, want 0", len(copiedMap))
		}
	})

	t.Run("non-nil map", func(t *testing.T) {
		originalMap := map[string]any{
			"string": "value",
			"int":    123,
			"bool":   true,
		}
		copiedMap := CopyMetadata(originalMap)

		if !reflect.DeepEqual(originalMap, copiedMap) {
			t.<PERSON><PERSON>("Copied map is not equal to the original. got %v, want %v", copiedMap, originalMap)
		}

		// Modify original map to ensure it's a deep copy
		originalMap["new_key"] = "new_value"

		if _, exists := copiedMap["new_key"]; exists {
			t.<PERSON><PERSON>("Modification of original map affected the copied map")
		}

		// Modify the copied map to ensure the original is not affected
		copiedMap["another_key"] = "another_value"
		if _, exists := originalMap["another_key"]; exists {
			t.Errorf("Modification of copied map affected the original map")
		}
	})
}
