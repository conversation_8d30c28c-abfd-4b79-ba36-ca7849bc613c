# Resumatter

> [!WARNING]
> This project is in the early stages of development and should be considered beta software. APIs and configurations may change.

Resumatter is an AI-powered resume tailoring tool designed to optimize your resume for any job description. It leverages Generative AI to analyze your resume and a target job posting, then rewrites your resume to maximize its impact and improve its Applicant Tracking System (ATS) score.

## Core Features

- **AI-Powered Resume Tailoring**: Integrates with multiple AI providers (Google Gemini, OpenAI) to help rewrite and optimize resume content.
- **ATS Scoring & Analysis**: Offers estimated ATS scoring and analysis, highlighting potential strengths and areas for improvement based on job descriptions.
- **Resume Integrity Evaluation**: Compares tailored resumes against originals to help ensure important information is preserved.
- **REST API**: Provides functionality through a versioned REST API for integration with other tools.
- **Flexible Configuration**: Configurable via YAML files with validation, environment variable support, and variable expansion.
- **Observability Support**: Includes OpenTelemetry integration for metrics and tracing to help with monitoring and debugging.

## API Endpoints

Resumatter exposes the following core endpoints:

- `POST /api/v1/ai/tailor`: Takes a base resume and a job description, and returns a tailored resume along with an ATS analysis.
- `POST /api/v1/ai/evaluate`: Compares a base resume and a tailored resume to check for significant data loss or hallucinated additions.
- `POST /api/v1/ai/analyze`: Takes a job description and returns a detailed analysis of its quality, clarity, inclusivity, and market competitiveness.
- `GET /health`: A simple health check endpoint.

## Getting Started

### 1. Build the Application
```bash
make build
```
This command compiles the main `resumatter` server binary into the `./build` directory.

### 2. Configure the Application

You have two options for configuration:

#### Option A: Using Environment Variables (Recommended for quick start)
Set the required environment variable and run directly:
```bash
export AI_GEMINI_API_KEY="your-gemini-api-key"
./build/resumatter serve
```

#### Option B: Using Configuration File
Copy `config.getting-started.yaml` to `config.yaml` and populate it with your settings:
```bash
cp config.getting-started.yaml config.yaml
# Edit config.yaml with your API keys and settings
./build/resumatter serve
```

### 3. Run the Server
```bash
./build/resumatter serve
```
The server will start, typically on port 8780, as defined in your configuration.

### 4. Use the API
You can interact with the API using `curl` or any HTTP client. The `scripts` directory contains example usage scripts.

**Example: Tailor a Resume**
```bash
./scripts/test-tailor.sh
```

## Configuration Management

While the main application is a server, it includes a powerful CLI for managing its configuration.

### Validate Configuration
```bash
./build/resumatter validate --verbose
```
Validates the configuration file and reports any errors. With `--verbose`, shows summary statistics.

### Show Complete Configuration
```bash
./build/resumatter show
```
Displays the entire parsed configuration in JSON format.

### List & Show AI Presets
```bash
# List all available presets
./build/resumatter list-presets

# Show details for a specific preset
./build/resumatter show-preset prod-gemini
```

## Key Implementation Details

### Environment Variable Support
The application provides comprehensive environment variable support through two mechanisms:

**1. Viper's Automatic Binding (Override mechanism):**
- Environment variables can override any configuration key.
- Example: `AI_GEMINI_API_KEY=your-key` sets the Gemini API key.
- Example: `SERVER_PORT=9999` overrides `server.port`.

**2. In-Config Variable Expansion (Template mechanism):**
- Configuration files support `${VAR_NAME}` expansion for dynamic value injection.
- Ideal for sensitive data like API keys.
- Example: `api_key: "${GEMINI_API_KEY}"`

### Configuration File Optional
The application can run without a configuration file by using environment variables:
- Set `AI_GEMINI_API_KEY` environment variable to provide the required API key
- All other settings will use sensible defaults
- Configuration files are still supported for advanced customization

### Project Structure
- **`cmd/`**: Main application entry points (`resumatter`).
- **`internal/`**: Core application logic, including the CLI commands (`internal/cli`) and the HTTP server (`internal/server`).
- **`ai/`**: Logic for interacting with different AI providers.
- **`config/`**: Configuration loading, validation, and default values.
- **`telemetry/`**: OpenTelemetry metrics and tracing implementation.
- **`ratelimit/`**: Rate limiting strategies and backends.

## Dependencies

- **Go**: 1.24.5+
- **Cobra**: Command-line interface framework.
- **Viper**: Configuration management.
- **Gin**: HTTP web framework.
- **OpenTelemetry**: Metrics and tracing.
- **Redis**: Backend for rate limiting.

## Contributing

Contributions are welcome! Please note that this project is maintained in my spare time, so my availability may be limited to a couple of times a week. I appreciate your patience and understanding.

If you'd like to contribute, please:

1.  Fork the repository.
2.  Create a new branch for your feature or bug fix.
3.  Make your changes and commit them with clear, descriptive messages.
4.  Open a pull request, and I will review it as soon as I can.

## License

This project is licensed under the Apache License 2.0. See the [LICENSE](LICENSE) file for details.

