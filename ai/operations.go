package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"text/template"

	"github.com/ajiwo/resumatter/ai/prompts"
	"github.com/ajiwo/resumatter/config"

	"github.com/invopop/jsonschema"
)

// AIOperationDefinition defines a single AI operation, encapsulating its prompts and I/O types.
type AIOperationDefinition struct {
	Name             string
	SystemPrompt     string
	UserPromptFormat string
	NewInput         func() any         // Function to create a new instance of the input struct
	NewOutput        func() any         // Function to create a new instance of the output struct
	ResponseSchema   *jsonschema.Schema // Cached JSON schema for the output
}

var (
	operations     = make(map[string]AIOperationDefinition)
	operationsOnce sync.Once
)

func init() {
	operationsOnce.Do(func() {
		registerOperation(AIOperationDefinition{
			Name:             "tailor",
			SystemPrompt:     prompts.SystemTailor,
			UserPromptFormat: prompts.UserTailor,
			NewInput:         func() any { return &TailorResumeInput{} },
			NewOutput:        func() any { return &TailorResumeOutput{} },
		})
		registerOperation(AIOperationDefinition{
			Name:             "evaluate",
			SystemPrompt:     prompts.SystemEvaluate,
			UserPromptFormat: prompts.UserEvaluate,
			NewInput:         func() any { return &EvaluateResumeInput{} },
			NewOutput:        func() any { return &EvaluateResumeOutput{} },
		})
		registerOperation(AIOperationDefinition{
			Name:             "analyze",
			SystemPrompt:     prompts.SystemAnalyze,
			UserPromptFormat: prompts.UserAnalyze,
			NewInput:         func() any { return &AnalyzeJobInput{} },
			NewOutput:        func() any { return &AnalyzeJobOutput{} },
		})
		registerOperation(AIOperationDefinition{
			Name:             "git-commit",
			SystemPrompt:     prompts.SystemGitCommit,
			UserPromptFormat: prompts.UserGitCommit,
			NewInput:         func() any { return &GenerateGitCommitInput{} },
			NewOutput:        func() any { return &GenerateGitCommitOutput{} },
		})
	})
}

func registerOperation(op AIOperationDefinition) {
	if _, exists := operations[op.Name]; exists {
		panic(fmt.Sprintf("AI operation '%s' already registered", op.Name))
	}
	// Generate and cache JSON schema for the structured response at registration time
	reflector := &jsonschema.Reflector{
		ExpandedStruct: true,
	}
	op.ResponseSchema = reflector.Reflect(op.NewOutput())

	operations[op.Name] = op
}

// GetOperationDefinition retrieves an AI operation definition by name.
func GetOperationDefinition(name string) (AIOperationDefinition, bool) {
	op, ok := operations[name]
	return op, ok
}

// ExecuteAIOperation executes a registered AI operation.
func ExecuteAIOperation(ctx context.Context, client Client, cfg *config.ResolvedOperationConfig, operationName string, input any) (any, error) {
	opDef, ok := GetOperationDefinition(operationName)
	if !ok {
		return nil, fmt.Errorf("AI operation '%s' not found", operationName)
	}

	output := opDef.NewOutput()

	// Prepare the user prompt using text/template for type-safe injection.
	tmpl, err := template.New(operationName).Parse(opDef.UserPromptFormat)
	if err != nil {
		return nil, fmt.Errorf("failed to parse user prompt template for operation '%s': %w", operationName, err)
	}

	var userPromptBuf bytes.Buffer
	if err := tmpl.Execute(&userPromptBuf, input); err != nil {
		return nil, fmt.Errorf("failed to execute user prompt template for operation '%s': %w", operationName, err)
	}
	userPrompt := userPromptBuf.String()

	// Create the AI request.
	aiReq := &GenerateRequest{
		Messages: []Message{
			{
				Role:    "user",
				Content: userPrompt,
			},
		},
		SystemInstruction: opDef.SystemPrompt,
		ResponseSchema:    opDef.ResponseSchema,
		ResponseMIMEType:  "application/json",
		Timeout:           &cfg.Timeout,
	}

	// Call the AI service.
	resp, err := client.GenerateContent(ctx, aiReq)
	if err != nil {
		return nil, fmt.Errorf("AI generation failed: %w", err)
	}

	// Parse the JSON response into the output struct.
	if err := json.Unmarshal([]byte(resp.Content), &output); err != nil {
		return nil, fmt.Errorf("failed to parse AI response JSON: %w", err)
	}

	return output, nil
}

func ListRegisteredOperations() []string {
	names := make([]string, 0, len(operations))
	for name := range operations {
		names = append(names, name)
	}
	return names
}
