package ai

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/ajiwo/resumatter/ai/prompts"
	"github.com/ajiwo/resumatter/config"
	"github.com/stretchr/testify/require"
)

type mockClient struct {
	resp  *GenerateResponse
	err   error
	check func(ctx context.Context, req *GenerateRequest)
}

func (m *mockClient) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	if m.check != nil {
		m.check(ctx, req)
	}
	return m.resp, m.err
}

func minimalResolvedCfg(timeout time.Duration) *config.ResolvedOperationConfig {
	return &config.ResolvedOperationConfig{
		Provider:       "openai",
		Model:          "test-model",
		Temperature:    0.1,
		MaxTokens:      128,
		Timeout:        timeout,
		ProviderConfig: map[string]any{},
	}
}

func TestExecuteAIOperation_SuccessTailor(t *testing.T) {
	ctx := context.Background()
	cfg := minimalResolvedCfg(750 * time.Millisecond)

	input := &TailorResumeInput{
		BaseResume:     "base",
		JobDescription: "job",
	}

	respJSON := `{
		"tailored_resume": "tailored",
		"ats_analysis": {"score": 90, "strengths": "S", "weaknesses": "W"},
		"job_posting_analysis": {"clarity": "C", "inclusivity": "I", "quality": "Q"}
	}`

	checked := false
	mc := &mockClient{
		resp: &GenerateResponse{Content: respJSON},
		check: func(ctx context.Context, req *GenerateRequest) {
			// Verify request is shaped as expected
			if len(req.Messages) != 1 {
				t.Fatalf("expected 1 message, got %d", len(req.Messages))
			}
			if req.SystemInstruction != prompts.SystemTailor {
				t.Fatalf("unexpected system instruction for tailor operation")
			}
			if req.ResponseSchema == nil {
				t.Fatalf("expected non-nil response schema")
			}
			if req.ResponseMIMEType != "application/json" {
				t.Fatalf("expected application/json mime type, got %q", req.ResponseMIMEType)
			}
			// Request should have timeout set by ExecuteAIOperation
			if req.Timeout == nil {
				t.Fatalf("expected request with timeout")
			}
			if *req.Timeout != 750*time.Millisecond {
				t.Fatalf("expected timeout of 750ms, got %v", *req.Timeout)
			}
			checked = true
		},
	}

	res, err := ExecuteAIOperation(ctx, mc, cfg, "tailor", input)
	require.Nilf(t, err, "unexpected error: %v", err)
	require.True(t, checked, "mock check was not executed")

	// Validate result content regardless of concrete type (map or struct)
	switch v := res.(type) {
	case *TailorResumeOutput:
		if v.TailoredResume != "tailored" || v.ATSAnalysis.Score != 90 || v.JobPostingAnalysis.Clarity != "C" {
			t.Fatalf("unexpected typed output: %+v", v)
		}
	case map[string]any:
		// Be tolerant to current implementation that may return a map
		if v["tailored_resume"] != "tailored" {
			t.Fatalf("unexpected map output: %+v", v)
		}
	default:
		t.Fatalf("unexpected output type: %T", v)
	}
}

func TestExecuteAIOperation_ClientError(t *testing.T) {
	ctx := context.Background()
	cfg := minimalResolvedCfg(500 * time.Millisecond)
	input := &AnalyzeJobInput{JobDescription: "text"}

	mc := &mockClient{
		err: errors.New("upstream error"),
	}

	_, err := ExecuteAIOperation(ctx, mc, cfg, "analyze", input)
	if err == nil {
		t.Fatalf("expected error, got nil")
	}
}

func TestExecuteAIOperation_InvalidJSON(t *testing.T) {
	ctx := context.Background()
	cfg := minimalResolvedCfg(500 * time.Millisecond)
	input := &EvaluateResumeInput{BaseResume: "a", TailoredResume: "b"}

	mc := &mockClient{resp: &GenerateResponse{Content: "not-json"}}

	_, err := ExecuteAIOperation(ctx, mc, cfg, "evaluate", input)
	if err == nil {
		t.Fatalf("expected JSON unmarshal error, got nil")
	}
}

func TestListRegisteredOperations(t *testing.T) {
	operations := ListRegisteredOperations()
	require.Contains(t, operations, "tailor")
	require.Contains(t, operations, "evaluate")
	require.Contains(t, operations, "analyze")
	require.Contains(t, operations, "git-commit")
}
