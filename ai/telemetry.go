package ai

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/ajiwo/resumatter/logger"
	"github.com/ajiwo/resumatter/telemetry"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// InstrumentedClient wraps an AI client with observability
type InstrumentedClient struct {
	delegate  Client
	operation string
	provider  string
	tracer    trace.Tracer
	metrics   *telemetry.Metrics
	logger    logger.Logger
}

// NewInstrumentedClient creates a new instrumented AI client
func NewInstrumentedClient(client Client, operation, provider string) Client {
	tel := telemetry.Global()
	if tel == nil {
		return client // Return original client if telemetry not initialized
	}

	return &InstrumentedClient{
		delegate:  client,
		operation: operation,
		provider:  provider,
		tracer:    tel.Tracer(),
		metrics:   tel.Metrics(),
		logger:    tel.Logger().Named("ai"),
	}
}

// GenerateContent generates content with full observability
func (c *InstrumentedClient) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	start := time.Now()
	ctx, span := c.startSpan(ctx, req)
	defer span.End()

	c.logRequestStart(ctx, req)

	resp, err := c.instrumentProviderCall(ctx, req)

	duration := time.Since(start)
	status := "success"
	if err != nil {
		status = "error"
		c.handleFailedRequest(ctx, span, err, duration)
	} else {
		c.handleSuccessfulRequest(ctx, span, resp, duration)
	}

	c.recordRequestMetrics(ctx, status, duration)
	c.finalizeSpan(span, status, duration)

	return resp, err
}

func (c *InstrumentedClient) startSpan(ctx context.Context, req *GenerateRequest) (context.Context, trace.Span) {
	ctx, span := c.tracer.Start(ctx, fmt.Sprintf("ai.operation.%s", c.operation),
		trace.WithAttributes(
			attribute.String("ai.operation", c.operation),
			attribute.String("ai.provider", c.provider),
			attribute.String("ai.model", ""), // Will be set by provider-specific client
			attribute.Int("ai.messages.count", len(req.Messages)),
		),
	)

	attrs := []attribute.KeyValue{}
	if req.Temperature != nil {
		attrs = append(attrs, attribute.Float64("ai.temperature", *req.Temperature))
	}
	if req.MaxTokens != nil {
		attrs = append(attrs, attribute.Int("ai.max_tokens", *req.MaxTokens))
	}
	if req.SystemInstruction != "" {
		attrs = append(attrs, attribute.Int("ai.system_instruction.length", len(req.SystemInstruction)))
	}

	totalPromptLength := 0
	for _, msg := range req.Messages {
		totalPromptLength += len(msg.Content)
	}
	attrs = append(attrs, attribute.Int("ai.prompt.length", totalPromptLength))
	span.SetAttributes(attrs...)

	return ctx, span
}

func (c *InstrumentedClient) logRequestStart(ctx context.Context, req *GenerateRequest) {
	totalPromptLength := 0
	for _, msg := range req.Messages {
		totalPromptLength += len(msg.Content)
	}

	c.logger.Info(ctx, "Starting AI request",
		logger.String("operation", c.operation),
		logger.String("provider", c.provider),
		logger.Int("messages_count", len(req.Messages)),
		logger.Int("prompt_length", totalPromptLength),
	)
}

func (c *InstrumentedClient) handleSuccessfulRequest(ctx context.Context, span trace.Span, resp *GenerateResponse, duration time.Duration) {
	span.SetStatus(codes.Ok, "")

	if resp.Usage != nil {
		span.SetAttributes(
			attribute.Int("ai.tokens.prompt", resp.Usage.PromptTokens),
			attribute.Int("ai.tokens.completion", resp.Usage.CompletionTokens),
			attribute.Int("ai.tokens.total", resp.Usage.TotalTokens),
		)
		if c.metrics != nil {
			c.metrics.RecordAITokens(ctx, c.operation, c.provider, "prompt", int64(resp.Usage.PromptTokens))
			c.metrics.RecordAITokens(ctx, c.operation, c.provider, "completion", int64(resp.Usage.CompletionTokens))
			c.metrics.RecordAITokens(ctx, c.operation, c.provider, "total", int64(resp.Usage.TotalTokens))
		}
	}

	responseAttrs := []attribute.KeyValue{}
	if resp.Content != "" {
		responseAttrs = append(responseAttrs, attribute.Int("ai.response.length", len(resp.Content)))
	}
	if resp.FinishReason != "" {
		responseAttrs = append(responseAttrs, attribute.String("ai.finish_reason", resp.FinishReason))
	}
	if len(responseAttrs) > 0 {
		span.SetAttributes(responseAttrs...)
	}

	c.logger.Info(ctx, "AI request completed successfully",
		logger.String("operation", c.operation),
		logger.String("provider", c.provider),
		logger.Duration("duration", duration),
		logger.Int("response_length", len(resp.Content)),
		logger.String("finish_reason", resp.FinishReason),
	)
}

func (c *InstrumentedClient) handleFailedRequest(ctx context.Context, span trace.Span, err error, duration time.Duration) {
	span.RecordError(err)
	span.SetStatus(codes.Error, err.Error())

	if c.metrics != nil {
		errorType := "unknown"
		var circuitErr *CircuitBreakerError
		if errors.As(err, &circuitErr) {
			errorType = "circuit_breaker"
			span.SetAttributes(attribute.String("ai.circuit_breaker.state", circuitErr.State))
		} else {
			var timeoutErr *TimeoutError
			if errors.As(err, &timeoutErr) {
				errorType = "timeout"
				span.SetAttributes(attribute.String("ai.timeout.duration", timeoutErr.Duration.String()))
			}
		}
		c.metrics.RecordAIError(ctx, c.operation, c.provider, errorType)
	}

	c.logger.ErrorWithErr(ctx, "AI request failed",
		err,
		logger.String("operation", c.operation),
		logger.String("provider", c.provider),
		logger.Duration("duration", duration),
	)
}

func (c *InstrumentedClient) recordRequestMetrics(ctx context.Context, status string, duration time.Duration) {
	if c.metrics != nil {
		c.metrics.RecordAIRequest(ctx, c.operation, c.provider, status, duration)
	}
}

func (c *InstrumentedClient) finalizeSpan(span trace.Span, status string, duration time.Duration) {
	span.SetAttributes(
		attribute.String("ai.status", status),
		attribute.Float64("ai.duration_ms", float64(duration.Nanoseconds())/1e6),
	)
}

// instrumentProviderCall adds provider-specific instrumentation
func (c *InstrumentedClient) instrumentProviderCall(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	// Start provider-specific span
	ctx, span := c.tracer.Start(ctx, fmt.Sprintf("ai.provider.%s.call", c.provider),
		trace.WithAttributes(
			attribute.String("ai.provider", c.provider),
		),
	)
	defer span.End()

	// Call the actual provider
	resp, err := c.delegate.GenerateContent(ctx, req)

	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return resp, err
}

// CircuitBreakerError represents a circuit breaker error
type CircuitBreakerError struct {
	State string
	Err   error
}

func (e *CircuitBreakerError) Error() string {
	return fmt.Sprintf("circuit breaker %s: %v", e.State, e.Err)
}

func (e *CircuitBreakerError) Unwrap() error {
	return e.Err
}

// TimeoutError represents a timeout error
type TimeoutError struct {
	Duration time.Duration
	Err      error
}

func (e *TimeoutError) Error() string {
	return fmt.Sprintf("timeout after %v: %v", e.Duration, e.Err)
}

func (e *TimeoutError) Unwrap() error {
	return e.Err
}
