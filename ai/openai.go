package ai

import (
	"context"
	"fmt"

	"github.com/ajiwo/resumatter/config"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
)

// openAIClient implements the Client interface for OpenAI
type openAIClient struct {
	client openai.Client
	config *config.ResolvedOperationConfig
}

// newOpenAIClient creates a new OpenAI client
func newOpenAIClient(_ context.Context, cfg *config.ResolvedOperationConfig) (Client, error) {
	apiKey := cfg.GetAPIKey()
	if apiKey == "" {
		return nil, fmt.Errorf("API key is required for OpenAI backend")
	}

	opts := []option.RequestOption{}

	openaiCfg := parseOpenAIConfig(cfg)

	baseUrl := openaiCfg.BaseURL
	if baseUrl != "" {
		opts = append(opts, option.WithBaseURL(baseUrl))
	}
	if openaiCfg.OrganizationID != "" {
		opts = append(opts, option.WithOrganization(openaiCfg.OrganizationID))
	}
	if openaiCfg.ProjectID != "" {
		opts = append(opts, option.WithProject(openaiCfg.ProjectID))
	}

	opts = append(opts, option.WithAPIKey(openaiCfg.APIKey))
	client := openai.NewClient(opts...)

	return &openAIClient{
		client: client,
		config: cfg,
	}, nil
}

// GenerateContent generates content using OpenAI
func (c *openAIClient) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	// Convert messages to OpenAI format
	messages := make([]openai.ChatCompletionMessageParamUnion, 0, len(req.Messages)+1)

	// Add system instruction if provided
	if req.SystemInstruction != "" {
		messages = append(messages, openai.SystemMessage(req.SystemInstruction))
	}

	// Add conversation messages
	for _, msg := range req.Messages {
		switch msg.Role {
		case "user":
			messages = append(messages, openai.UserMessage(msg.Content))
		case "assistant":
			messages = append(messages, openai.AssistantMessage(msg.Content))
		case "system":
			messages = append(messages, openai.SystemMessage(msg.Content))
		default:
			// Default to user message
			messages = append(messages, openai.UserMessage(msg.Content))
		}
	}

	// Build completion parameters
	params := openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    c.config.Model,
	}

	// Set temperature
	temperature := c.config.Temperature
	if req.Temperature != nil {
		temperature = *req.Temperature
	}
	params.Temperature = openai.Float(temperature)

	// Set max tokens
	maxTokens := c.config.MaxTokens
	if req.MaxTokens != nil {
		maxTokens = *req.MaxTokens
	}
	if maxTokens > 0 {
		params.MaxTokens = openai.Int(int64(maxTokens))
	}

	// Add response format if schema is provided
	if req.ResponseSchema != nil {
		schemaParam := openai.ResponseFormatJSONSchemaJSONSchemaParam{
			Name:   "response_schema",
			Schema: req.ResponseSchema,
			Strict: openai.Bool(true),
		}
		params.ResponseFormat = openai.ChatCompletionNewParamsResponseFormatUnion{
			OfJSONSchema: &openai.ResponseFormatJSONSchemaParam{
				JSONSchema: schemaParam,
			},
		}
	}

	// Prepare request options
	reqOptions := []option.RequestOption{}

	// Add timeout if provided in the request
	if req.Timeout != nil {
		reqOptions = append(reqOptions, option.WithRequestTimeout(*req.Timeout))
	}

	// Make the API call
	completion, err := c.client.Chat.Completions.New(ctx, params, reqOptions...)
	if err != nil {
		return nil, fmt.Errorf("OpenAI completion failed: %w", err)
	}

	// Build response
	response := buildGenerateResponse(completion, c.config)

	return response, nil
}

// buildGenerateResponse creates a GenerateResponse from OpenAI completion result
func buildGenerateResponse(completion *openai.ChatCompletion, cfg *config.ResolvedOperationConfig) *GenerateResponse {
	// Extract response
	var content string
	var finishReason string
	if len(completion.Choices) > 0 {
		content = completion.Choices[0].Message.Content
		finishReason = completion.Choices[0].FinishReason
	}

	// Build response
	response := &GenerateResponse{
		Content:      content,
		FinishReason: finishReason,
		Metadata: map[string]any{
			"provider": cfg.Provider,
			"model":    cfg.Model,
		},
	}

	// Add usage information
	if completion.Usage.PromptTokens > 0 || completion.Usage.CompletionTokens > 0 {
		response.Usage = &Usage{
			PromptTokens:     int(completion.Usage.PromptTokens),
			CompletionTokens: int(completion.Usage.CompletionTokens),
			TotalTokens:      int(completion.Usage.TotalTokens),
		}
	}

	return response
}

// parseOpenAIConfig extracts OpenAI configuration from provider config
func parseOpenAIConfig(cfg *config.ResolvedOperationConfig) *config.OpenAIConfig {
	var openaiCfg config.OpenAIConfig

	if cfg.ProviderConfig != nil {
		if apiKey, ok := cfg.ProviderConfig["api_key"].(string); ok {
			openaiCfg.APIKey = apiKey
		}
		if orgID, ok := cfg.ProviderConfig["organization_id"].(string); ok {
			openaiCfg.OrganizationID = orgID
		}
		if baseURL, ok := cfg.ProviderConfig["base_url"].(string); ok {
			openaiCfg.BaseURL = baseURL
		}
		if projectID, ok := cfg.ProviderConfig["project_id"].(string); ok {
			openaiCfg.ProjectID = projectID
		}
	}

	return &openaiCfg
}
