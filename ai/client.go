package ai

import (
	"context"
	"fmt"
	"time"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/telemetry"
)

// Client represents a unified AI client interface
type Client interface {
	// GenerateContent generates content using the configured AI provider
	GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error)
}

// clientWithCircuitBreaker wraps a Client with circuit breaker functionality.
type clientWithCircuitBreaker struct {
	delegate Client
	breaker  *AICircuitBreaker
}

// GenerateContent executes the request with circuit breaker protection.
func (c *clientWithCircuitBreaker) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	return c.breaker.Execute(ctx, func() (*GenerateResponse, error) {
		return c.delegate.GenerateContent(ctx, req)
	})
}

// GenerateRequest represents a request to generate content
type GenerateRequest struct {
	// Messages for the conversation
	Messages []Message `json:"messages"`
	// SystemInstruction for the AI model
	SystemInstruction string `json:"system_instruction,omitempty"`
	// ResponseSchema for structured output (JSON schema)
	ResponseSchema any `json:"response_schema,omitempty"`
	// ResponseMIMEType for the response format
	ResponseMIMEType string `json:"response_mime_type,omitempty"`
	// Override temperature for this request
	Temperature *float64 `json:"temperature,omitempty"`
	// Override max tokens for this request
	MaxTokens *int `json:"max_tokens,omitempty"`
	// Timeout for the request
	Timeout *time.Duration `json:"timeout,omitempty"`
}

// Message represents a conversation message
type Message struct {
	Role    string `json:"role"`    // "user", "assistant", "system"
	Content string `json:"content"` // The message content
}

// GenerateResponse represents the response from content generation
type GenerateResponse struct {
	// Content is the generated text
	Content string `json:"content"`
	// FinishReason indicates why generation stopped
	FinishReason string `json:"finish_reason,omitempty"`
	// Usage contains token usage information
	Usage *Usage `json:"usage,omitempty"`
	// Provider-specific metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// NewClient creates a new AI client for a specific operation
func NewClient(ctx context.Context, config *config.Config, operationName string) (Client, error) {
	cfg, err := config.ResolveOperationConfig(operationName)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve operation config: %w", err)
	}

	var concreteClient Client
	switch cfg.Provider {
	case "genai-gemini", "genai-vertexai":
		concreteClient, err = newGenAIClient(ctx, cfg)
	case "openai":
		concreteClient, err = newOpenAIClient(ctx, cfg)
	default:
		return nil, fmt.Errorf("unsupported provider: %s", cfg.Provider)
	}

	if err != nil {
		return nil, err // Propagate client creation error
	}

	// Wrap the client with a circuit breaker
	cb := NewAICircuitBreaker(operationName, cfg, telemetry.Global(), telemetry.Logger())
	if cb != nil {
		concreteClient = &clientWithCircuitBreaker{
			delegate: concreteClient,
			breaker:  cb,
		}
	}

	// Wrap with observability instrumentation
	instrumentedClient := NewInstrumentedClient(concreteClient, operationName, cfg.Provider)

	return instrumentedClient, nil
}
