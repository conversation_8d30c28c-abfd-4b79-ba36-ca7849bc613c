package ai

import (
	"context"
	"testing"

	"github.com/ajiwo/resumatter/config"
	"github.com/openai/openai-go"
	"github.com/stretchr/testify/require"
)

func TestParseOpenAIConfig(t *testing.T) {
	cfg := &config.ResolvedOperationConfig{
		Provider: "openai",
		ProviderConfig: map[string]any{
			"api_key":         "sk-test",
			"organization_id": "org_123",
			"base_url":        "https://api.example.com/v1",
			"project_id":      "proj_456",
		},
	}

	oc := parseOpenAIConfig(cfg)
	require.Equal(t, "sk-test", oc.APIKey)
	require.Equal(t, "org_123", oc.OrganizationID)
	require.Equal(t, "https://api.example.com/v1", oc.BaseURL)
	require.Equal(t, "proj_456", oc.ProjectID)
}

func TestNewOpenAIClient_MissingAPIKey(t *testing.T) {
	ctx := context.Background()
	cfg := &config.ResolvedOperationConfig{
		Provider:       "openai",
		Model:          "gpt-4o-mini",
		ProviderConfig: map[string]any{
			// no api_key
		},
	}

	_, err := newOpenAIClient(ctx, cfg)
	require.Error(t, err)
	require.Contains(t, err.Error(), "API key is required for OpenAI backend")
}

func TestNewOpenAIClient_SucceedsWithAPIKey(t *testing.T) {
	ctx := context.Background()
	cfg := &config.ResolvedOperationConfig{
		Provider: "openai",
		Model:    "gpt-4o-mini",
		ProviderConfig: map[string]any{
			"api_key":         "sk-test",
			"organization_id": "org_123",
			"project_id":      "proj_456",
			"base_url":        "https://api.openai.com/v1",
		},
	}

	client, err := newOpenAIClient(ctx, cfg)
	require.NoError(t, err)
	require.NotNil(t, client)
}

func TestBuildGenerateResponse_NoChoicesNoUsage(t *testing.T) {
	completion := &openai.ChatCompletion{}
	cfg := &config.ResolvedOperationConfig{Provider: "openai", Model: "gpt-4o-mini"}

	resp := buildGenerateResponse(completion, cfg)
	require.NotNil(t, resp)
	require.Equal(t, "", resp.Content)
	require.Equal(t, "", resp.FinishReason)
	require.Nil(t, resp.Usage)
	require.Equal(t, map[string]any{"provider": "openai", "model": "gpt-4o-mini"}, resp.Metadata)
}

func TestBuildGenerateResponse_WithChoicesAndUsage(t *testing.T) {
	completion := &openai.ChatCompletion{
		Choices: []openai.ChatCompletionChoice{{
			Message:      openai.ChatCompletionMessage{Content: "hello world"},
			FinishReason: "stop",
		}},
	}
	// The Usage fields in the SDK are integers; set them to be > 0 to populate resp.Usage
	completion.Usage.PromptTokens = 10
	completion.Usage.CompletionTokens = 5
	completion.Usage.TotalTokens = 15

	cfg := &config.ResolvedOperationConfig{Provider: "openai", Model: "gpt-4o-mini"}

	resp := buildGenerateResponse(completion, cfg)
	require.NotNil(t, resp)
	require.Equal(t, "hello world", resp.Content)
	require.Equal(t, "stop", resp.FinishReason)
	require.NotNil(t, resp.Usage)
	require.Equal(t, 10, resp.Usage.PromptTokens)
	require.Equal(t, 5, resp.Usage.CompletionTokens)
	require.Equal(t, 15, resp.Usage.TotalTokens)
}
