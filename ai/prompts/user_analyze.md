
Please perform a comprehensive analysis of the provided job description to help optimize it for attracting qualified candidates and ensuring best practices.

**Analysis Areas:**

1. **Overall Job Quality Score** (0-100):
   Provide an overall assessment of the job posting quality.

2. **Clarity Assessment**:
   - Score (0-100) for how clear and understandable the job description is
   - Detailed analysis of structure, language, and comprehensibility
   - Specific improvements for better clarity

3. **Inclusivity Analysis**:
   - Score (0-100) for inclusive language and bias-free content
   - Overall assessment of inclusivity
   - Flag any potentially problematic terms or phrases
   - Provide specific suggestions for more inclusive language

4. **Candidate Attraction**:
   - Score (0-100) for how attractive this posting is to qualified candidates
   - List specific strengths that would attract candidates
   - Identify weaknesses that might deter good candidates

5. **Market Competitiveness**:
   - Assess salary transparency (good/fair/poor and why)
   - Evaluate if requirements are realistic for the role level
   - Determine how well it aligns with current industry standards

6. **Top Recommendations**:
   Provide 3-5 high-impact recommendations for improving this job posting.

**Job Description to Analyze:**
-----
{{.JobDescription}}
-----
