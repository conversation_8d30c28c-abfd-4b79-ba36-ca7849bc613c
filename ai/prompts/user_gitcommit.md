
Please analyze the provided Git diff and generate an appropriate commit message.

**Requirements:**

1. **Format**: Use conventional commit format when applicable (feat:, fix:, docs:, style:, 
   refactor:, test:, chore:)

2. **Structure**:
   - Subject line: Clear, concise summary (50 characters or less)
   - Body (if needed): More detailed explanation for complex changes, wrapped at 77-83 
     characters per line
   - Breaking changes: Note any breaking changes if present

3. **Content Guidelines**:
   - Focus on WHAT changed and WHY (not HOW)
   - Use imperative mood ("Add feature" not "Added feature")
   - Be specific but concise
   - Avoid unnecessary technical jargon

4. **Context Awareness**:
   Use the recent commit history to understand the development flow and ensure your 
   commit message fits the established pattern and continues the logical progression 
   of changes.

5. **Security Check**:
   If you detect any sensitive information (API keys, passwords, credentials, private 
   data) in the diff, respond with: "SECURITY WARNING: Sensitive information detected 
   in diff. Please review and sanitize before committing."

**Recent Commit History (for context):**
-----
{{.RecentCommitHistory}}
-----

**Git Diff:**
-----
{{.GitDiff}}
-----
