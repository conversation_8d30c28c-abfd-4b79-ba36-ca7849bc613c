package ai

import (
	"context"
	"fmt"

	"github.com/ajiwo/resumatter/config"

	"cloud.google.com/go/auth"
	"cloud.google.com/go/auth/credentials"
	"google.golang.org/genai"
)

// genAIClient implements the Client interface for Google GenAI
type genAIClient struct {
	client *genai.Client
	config *config.ResolvedOperationConfig
}

// newGenAIClient creates a new GenAI client
func newGenAIClient(ctx context.Context, cfg *config.ResolvedOperationConfig) (Client, error) {
	genaiCfg := parseGenAIConfig(cfg)

	var client *genai.Client
	var err error
	switch genaiCfg.Backend {
	case "gemini":
		client, err = createGeminiClient(ctx, genaiCfg)
	case "vertexai":
		client, err = createVertexAIClient(ctx, genaiCfg)
	default:
		return nil, fmt.Errorf("unsupported GenAI backend: %s", genaiCfg.Backend)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create GenAI client: %w", err)
	}

	return &genAIClient{
		client: client,
		config: cfg,
	}, nil
}

// GenerateContent generates content using GenAI
func (c *genAIClient) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	// Create generation config
	temperature := float32(c.config.Temperature)
	if req.Temperature != nil {
		temperature = float32(*req.Temperature)
	}

	maxTokens := int32(c.config.MaxTokens)
	if req.MaxTokens != nil {
		maxTokens = int32(*req.MaxTokens)
	}

	genConfig := &genai.GenerateContentConfig{
		Temperature:     &temperature,
		MaxOutputTokens: maxTokens,
	}

	// Add timeout if provided in the request
	if req.Timeout != nil {
		genConfig.HTTPOptions = &genai.HTTPOptions{
			Timeout: req.Timeout,
		}
	}

	// Add system instruction if provided
	if req.SystemInstruction != "" {
		genConfig.SystemInstruction = genai.NewContentFromText(req.SystemInstruction, genai.RoleUser)
	}

	// Add response schema if provided
	if req.ResponseSchema != nil {
		genConfig.ResponseJsonSchema = req.ResponseSchema
		if req.ResponseMIMEType != "" {
			genConfig.ResponseMIMEType = req.ResponseMIMEType
		} else {
			genConfig.ResponseMIMEType = "application/json"
		}
	}

	// Build contents from full message history (excluding system messages)
	contents, err := buildGenAIContents(req.Messages)
	if err != nil {
		return nil, err
	}

	// Generate content
	resp, err := c.client.Models.GenerateContent(ctx, c.config.Model, contents, genConfig)
	if err != nil {
		return nil, fmt.Errorf("GenAI generation failed: %w", err)
	}

	// Extract content from response using the built-in Text() method
	// This properly handles all text parts and filters out non-text content
	content := resp.Text()

	// Build response
	response := &GenerateResponse{
		Content: content,
		Metadata: map[string]any{
			"provider": c.config.Provider,
			"model":    c.config.Model,
		},
	}

	// Add usage information if available
	if resp.UsageMetadata != nil {
		response.Usage = &Usage{
			PromptTokens:     int(resp.UsageMetadata.PromptTokenCount),
			CompletionTokens: int(resp.UsageMetadata.CandidatesTokenCount),
			TotalTokens:      int(resp.UsageMetadata.TotalTokenCount),
		}
	}

	// Add finish reason if available
	if len(resp.Candidates) > 0 {
		response.FinishReason = string(resp.Candidates[0].FinishReason)
	}

	return response, nil
}

// buildGenAIContents converts our message history into GenAI contents.
// - Skips system messages because they are handled via SystemInstruction
// - Maps role "assistant" to GenAI role "model"
// - Returns an error if there are no messages or only system messages
func buildGenAIContents(messages []Message) ([]*genai.Content, error) {
	if len(messages) == 0 {
		return nil, fmt.Errorf("no messages provided in request")
	}

	contents := make([]*genai.Content, 0, len(messages))
	for _, m := range messages {
		if m.Role == "system" {
			continue
		}
		role := m.Role
		if role == "assistant" {
			role = "model"
		}
		contents = append(contents, genai.NewContentFromText(m.Content, genai.Role(role)))
	}
	if len(contents) == 0 {
		return nil, fmt.Errorf("no non-system messages provided in request")
	}
	return contents, nil
}

// parseGenAIConfig extracts GenAI configuration from provider config
func parseGenAIConfig(cfg *config.ResolvedOperationConfig) *config.GenAIConfig {
	var genaiCfg config.GenAIConfig

	if cfg.ProviderConfig != nil {
		if apiKey, ok := cfg.ProviderConfig["api_key"].(string); ok {
			genaiCfg.ApiKey = apiKey
		}
		if backend, ok := cfg.ProviderConfig["backend"].(string); ok {
			genaiCfg.Backend = backend
		}
		if credFile, ok := cfg.ProviderConfig["credentials_file"].(string); ok {
			genaiCfg.CredentialsFile = credFile
		}
		if credJSON, ok := cfg.ProviderConfig["credentials"].(string); ok {
			genaiCfg.CredentialsJSON = credJSON
		}
		if project, ok := cfg.ProviderConfig["google_cloud_project"].(string); ok {
			genaiCfg.GoogleCloudProject = project
		}
		if region, ok := cfg.ProviderConfig["google_cloud_region"].(string); ok {
			genaiCfg.GoogleCloudRegion = region
		}
	}

	return &genaiCfg
}

// createGeminiClient creates a Gemini API client
func createGeminiClient(ctx context.Context, cfg *config.GenAIConfig) (*genai.Client, error) {
	if cfg.ApiKey == "" {
		return nil, fmt.Errorf("API key is required for Gemini backend")
	}

	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		Backend: genai.BackendGeminiAPI,
		APIKey:  cfg.ApiKey,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	return client, nil
}

// createVertexAIClient creates a Vertex AI client
func createVertexAIClient(ctx context.Context, cfg *config.GenAIConfig) (*genai.Client, error) {
	if cfg.ApiKey != "" {
		return nil, fmt.Errorf("api_key should not be set for Vertex AI backend (uses service account authentication)")
	}

	if cfg.GoogleCloudProject == "" {
		return nil, fmt.Errorf("google_cloud_project is required for Vertex AI backend")
	}

	var creds *auth.Credentials
	var err error

	// Try to get credentials from file or JSON
	if cfg.CredentialsFile != "" {
		creds, err = credentials.DetectDefault(&credentials.DetectOptions{
			CredentialsFile: cfg.CredentialsFile,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to load credentials from file: %w", err)
		}
	} else if cfg.CredentialsJSON != "" {
		creds, err = credentials.DetectDefault(&credentials.DetectOptions{
			CredentialsJSON: []byte(cfg.CredentialsJSON),
		})
		if err != nil {
			return nil, fmt.Errorf("failed to load credentials from JSON: %w", err)
		}
	}

	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		Backend:     genai.BackendVertexAI,
		Credentials: creds,
		Project:     cfg.GoogleCloudProject,
		Location:    cfg.GoogleCloudRegion,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Vertex AI client: %w", err)
	}

	return client, nil
}
