package ai

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/stretchr/testify/require"
)

func makeBreakerForTest(t *testing.T, op string, cbCfg config.CircuitBreakerConfig) *AICircuitBreaker {
	t.Helper()
	cfg := &config.ResolvedOperationConfig{
		Provider:       "test-provider",
		Model:          "test-model",
		Temperature:    0,
		MaxTokens:      0,
		Timeout:        0,
		ProviderConfig: map[string]any{},
		CircuitBreaker: cbCfg,
	}
	br := NewAICircuitBreaker(op, cfg, nil, logger.NewDefault())
	require.NotNil(t, br, "breaker should be created when enabled")
	return br
}

func TestAICircuitBreaker_OpenStateReturnsTypedError(t *testing.T) {
	cbCfg := config.CircuitBreakerConfig{
		Enabled:          true,
		FailureThreshold: 0.5,
		MinRequests:      1,
		MaxRequests:      1,
		Interval:         "0s",
		Timeout:          "0s",
	}
	br := makeBreakerForTest(t, "op-open", cbCfg)

	ctx := context.Background()
	// First call fails to trip the breaker to open state
	_, _ = br.Execute(ctx, func() (*GenerateResponse, error) {
		return nil, fmt.Errorf("fail-once")
	})

	// Next call should be rejected due to open state
	_, err := br.Execute(ctx, func() (*GenerateResponse, error) { return &GenerateResponse{}, nil })
	require.Error(t, err)
	var cbe *CircuitBreakerError
	require.True(t, errors.As(err, &cbe), "expected CircuitBreakerError")
	require.Equal(t, "open", cbe.State)
}

func TestAICircuitBreaker_TooManyRequestsReturnsTypedError(t *testing.T) {
	cbCfg := config.CircuitBreakerConfig{
		Enabled:          true,
		FailureThreshold: 0.5,
		MinRequests:      1,
		MaxRequests:      1, // allow only 1 request in half-open
		Interval:         "0s",
		Timeout:          "20ms", // short timeout to enter half-open quickly
	}
	br := makeBreakerForTest(t, "op-half-open", cbCfg)

	ctx := context.Background()
	// Trip to open first
	_, _ = br.Execute(ctx, func() (*GenerateResponse, error) { return nil, fmt.Errorf("boom") })

	// Wait for half-open
	time.Sleep(30 * time.Millisecond)

	start := make(chan struct{})
	var wg sync.WaitGroup
	wg.Add(2)

	results := make(chan error, 2)
	fn := func() {
		defer wg.Done()
		<-start
		_, err := br.Execute(ctx, func() (*GenerateResponse, error) {
			// Keep it busy a bit to force the second call to exceed MaxRequests
			time.Sleep(30 * time.Millisecond)
			return &GenerateResponse{Content: "ok"}, nil
		})
		results <- err
	}

	go fn()
	go fn()
	close(start)
	wg.Wait()
	close(results)

	var sawTooMany bool
	var sawSuccess bool
	for err := range results {
		if err == nil {
			sawSuccess = true
			continue
		}
		var cbe *CircuitBreakerError
		if errors.As(err, &cbe) {
			if cbe.State == "half-open" {
				sawTooMany = true
			}
		}
	}

	require.True(t, sawSuccess, "one request should succeed in half-open")
	require.True(t, sawTooMany, "one request should be rejected with half-open too many requests error")
}

func TestAICircuitBreaker_GenericErrorWrappedWithClosedState(t *testing.T) {
	cbCfg := config.CircuitBreakerConfig{
		Enabled:          true,
		FailureThreshold: 1.0, // require 100% failures, but also set high min to avoid opening
		MinRequests:      100,
		MaxRequests:      5,
		Interval:         "0s",
		Timeout:          "0s",
	}
	br := makeBreakerForTest(t, "op-generic", cbCfg)

	ctx := context.Background()
	orig := errors.New("upstream failure")
	_, err := br.Execute(ctx, func() (*GenerateResponse, error) { return nil, orig })
	require.Error(t, err)
	var cbe *CircuitBreakerError
	require.True(t, errors.As(err, &cbe), "expected CircuitBreakerError wrapping upstream error")
	require.Equal(t, "closed", cbe.State)
	require.True(t, errors.Is(err, orig), "wrapped error should be reachable via errors.Is")
}
