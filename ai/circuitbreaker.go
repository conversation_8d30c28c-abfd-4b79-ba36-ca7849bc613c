package ai

import (
	"context"
	"errors"
	"fmt"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/ajiwo/resumatter/telemetry"

	"github.com/sony/gobreaker/v2"
)

// Circuit breaker error types for external packages
var (
	// ErrCircuitBreakerOpen is returned when the circuit breaker is open
	ErrCircuitBreakerOpen = errors.New("circuit breaker is open")
	// ErrCircuitBreakerTooManyRequests is returned when circuit breaker is half-open with too many requests
	ErrCircuitBreakerTooManyRequests = errors.New("circuit breaker has too many requests")
)

// AICircuitBreaker provides circuit breaker protection for AI API calls
type AICircuitBreaker struct {
	instance      *gobreaker.CircuitBreaker[*GenerateResponse]
	operationName string
	providerName  string
	metrics       *telemetry.Metrics
}

// NewAICircuitBreaker creates a circuit breaker configured for a specific operation type
func NewAICircuitBreaker(operation string, cfg *config.ResolvedOperationConfig, tel *telemetry.Telemetry, log logger.Logger) *AICircuitBreaker {
	// If circuit breaker is disabled, return nil to indicate no circuit breaker
	if !cfg.CircuitBreaker.Enabled {
		return nil
	}

	// Create the circuit breaker instance first so we can reference it in the callback
	cb := &AICircuitBreaker{
		operationName: operation,
		providerName:  cfg.Provider,
	}
	if tel != nil {
		cb.metrics = tel.Metrics()
	}

	// Parse circuit breaker interval and timeout
	interval, err := cfg.GetCircuitBreakerInterval()
	if err != nil {
		log.Error(context.Background(), "Failed to parse circuit breaker interval", logger.String("operation", operation), logger.Err(err))
		return nil
	}

	timeout, err := cfg.GetCircuitBreakerTimeout()
	if err != nil {
		log.Error(context.Background(), "Failed to parse circuit breaker timeout", logger.String("operation", operation), logger.Err(err))
		return nil
	}

	settings := gobreaker.Settings{
		Name:        fmt.Sprintf("AI-%s", operation),
		MaxRequests: uint32(cfg.CircuitBreaker.MaxRequests),
		Interval:    interval,
		Timeout:     timeout,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= uint32(cfg.CircuitBreaker.MinRequests) &&
				failureRatio >= cfg.CircuitBreaker.FailureThreshold
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			log.Info(context.Background(), "Circuit breaker state changed",
				logger.String("name", name),
				logger.String("from", from.String()),
				logger.String("to", to.String()))
			if cb.metrics != nil {
				var stateValue int64
				switch to {
				case gobreaker.StateClosed:
					stateValue = 0
				case gobreaker.StateOpen:
					stateValue = 1
				case gobreaker.StateHalfOpen:
					stateValue = 2
				}
				cb.metrics.RecordCircuitBreakerState(context.Background(), cb.operationName, cb.providerName, stateValue)
			}
		},
	}

	cb.instance = gobreaker.NewCircuitBreaker[*GenerateResponse](settings)
	return cb
}

// Execute executes the provided function with circuit breaker protection
func (cb *AICircuitBreaker) Execute(ctx context.Context, fn func() (*GenerateResponse, error)) (*GenerateResponse, error) {
	if cb == nil || cb.instance == nil {
		// If breaker is disabled/nil, just execute the function directly
		return fn()
	}

	result, err := cb.instance.Execute(fn)
	if err != nil {
		if cb.metrics != nil {
			cb.metrics.RecordCircuitBreakerRequest(ctx, cb.operationName, cb.providerName, "failure")
		}

		// Convert gobreaker errors to our custom errors so telemetry can classify them
		if errors.Is(err, gobreaker.ErrOpenState) {
			return nil, &CircuitBreakerError{State: "open", Err: ErrCircuitBreakerOpen}
		}
		if errors.Is(err, gobreaker.ErrTooManyRequests) {
			return nil, &CircuitBreakerError{State: "half-open", Err: ErrCircuitBreakerTooManyRequests}
		}

		// For other circuit breaker errors, return a typed error with current state when available
		return nil, &CircuitBreakerError{State: cb.instance.State().String(), Err: err}
	}

	if cb.metrics != nil {
		cb.metrics.RecordCircuitBreakerRequest(ctx, cb.operationName, cb.providerName, "success")
	}
	return result, nil
}

// GetStats returns circuit breaker statistics
func (cb *AICircuitBreaker) GetStats() map[string]any {
	if cb == nil || cb.instance == nil {
		return map[string]any{
			"enabled": false,
		}
	}

	counts := cb.instance.Counts()
	return map[string]any{
		"name":                  cb.instance.Name(),
		"operation":             cb.operationName,
		"state":                 cb.instance.State().String(),
		"enabled":               true,
		"requests":              counts.Requests,
		"total_successes":       counts.TotalSuccesses,
		"total_failures":        counts.TotalFailures,
		"consecutive_successes": counts.ConsecutiveSuccesses,
		"consecutive_failures":  counts.ConsecutiveFailures,
	}
}

// IsHealthy returns true if the circuit breaker is in closed state
func (cb *AICircuitBreaker) IsHealthy() bool {
	if cb == nil || cb.instance == nil {
		return true // If no circuit breaker, consider it healthy
	}
	return cb.instance.State() == gobreaker.StateClosed
}

// GetState returns the current circuit breaker state
func (cb *AICircuitBreaker) GetState() string {
	if cb == nil || cb.instance == nil {
		return "disabled"
	}
	return cb.instance.State().String()
}

// GetCounts returns the current circuit breaker counts
func (cb *AICircuitBreaker) GetCounts() gobreaker.Counts {
	if cb == nil || cb.instance == nil {
		return gobreaker.Counts{}
	}
	return cb.instance.Counts()
}

// GetOperationName returns the operation name this circuit breaker protects
func (cb *AICircuitBreaker) GetOperationName() string {
	if cb == nil {
		return ""
	}
	return cb.operationName
}
