# Adding a New AI Operation

This guide explains how to extend Resumatter with new AI-powered operations. The system is designed to make adding new operations straightforward and consistent.

## Overview

Resumatter uses a generic AI operation handling system that simplifies adding new AI operations. Instead of creating individual functions and handlers for each operation, the system uses:

- **`AIOperationDefinition`**: A centralized way to define AI operations with their prompts and I/O types
- **`ExecuteAIOperation`**: A single generic function that handles all AI operations
- **`NewAIOperationHandler`**: A generic HTTP handler that works for any registered AI operation

## How to Add a New AI Operation

Adding a new AI operation involves five simple steps. Let's walk through each one:

### 1. Define Input and Output Types

Add your input and output struct types to `ai/types.go`:

```go
// ExampleOperationInput represents the input for the example operation
type ExampleOperationInput struct {
    InputField1 string `json:"input_field_1"`
    InputField2 string `json:"input_field_2"`
}

// ExampleOperationOutput represents the output from the example operation
type ExampleOperationOutput struct {
    OutputField1 string `json:"output_field_1"`
    OutputField2 int    `json:"output_field_2"`
    Results      []string `json:"results"`
}
```

**Tips:**
- Use descriptive struct names following the pattern `{OperationName}Input` and `{OperationName}Output`
- Include JSON tags for proper API serialization
- Document complex fields with comments for clarity

### 2. Create Prompt Templates

Create two new Markdown files in the `ai/prompts/` directory for your system and user prompts:

- `system_{operation_name}.md`
- `user_{operation_name}.md`

For an operation named `example`, you would create `system_example.md` and `user_example.md`.

**`system_example.md`:**
```markdown
You are an expert [domain expert] with deep knowledge of:

- [Key expertise area 1]
- [Key expertise area 2]
- [Key expertise area 3]

Your role is to [describe the main purpose and responsibilities].
```

**`user_example.md`:**
```markdown
Please [describe what the AI should do with the input].

**Requirements:**

1. **[Requirement 1]**: [Description]
2. **[Requirement 2]**: [Description]
3. **[Requirement 3]**: [Description]

**Input Data:**
-----
{{.InputField1}}
-----

**Additional Context (if applicable):**
-----
{{.InputField2}}
-----
```

Next, embed these prompt files into the application by adding them to `ai/prompts/prompts.go`:

```go
package prompts

import _ "embed"

// ... existing prompts ...

//go:embed system_example.md
var SystemExample string

//go:embed user_example.md
var UserExample string
```

**Tips:**
- Use descriptive variable names following the pattern `System{OperationName}` and `User{OperationName}`.
- Write clear, specific system prompts that establish the AI's role and expertise.
- Structure user prompts with clear requirements and formatting.
- Use `{{.FieldName}}` placeholders for dynamic content injection (these will be filled with your input data).
- Follow the established pattern of separating content sections with `-----` dividers.

### 3. Register the Operation

Add your operation registration to the `init()` function in `ai/operations.go`:

```go
func init() {
    operationsOnce.Do(func() {
        // ... existing operations ...
        
        registerOperation(AIOperationDefinition{
            Name:             "example",
            SystemPrompt:     prompts.SystemExample,
            UserPromptFormat: prompts.UserExample,
            NewInput:         func() any { return &ExampleOperationInput{} },
            NewOutput:        func() any { return &ExampleOperationOutput{} },
        })
    })
}
```

**Important notes:**
- Choose a unique, descriptive operation name (use lowercase, kebab-case for multi-word names)
- The `NewInput` and `NewOutput` functions must return pointers to your struct types
- Operation names must be unique - the system will prevent duplicate registrations

### 4. Add HTTP Route

Add the new endpoint to `internal/server/routes.go` in the `RegisterRoutes()` function:

```go
func RegisterRoutes(router *gin.Engine, deps *Dependencies) {
    // ... existing routes ...
    
    // API v1 routes
    v1 := router.Group("/api/v1")
    setupMiddleware(v1, deps) // Pass dependencies to middleware setup
    {
        aiGroup := v1.Group("/ai")
        {
            // Create and register a handler for each AI operation
            tailorHandler := NewAIOperationHandler("tailor", deps.Config, deps.AIClients["tailor"], deps.Logger)
            aiGroup.POST("/tailor", tailorHandler.Handle)

            // ... existing operations ...
            
            exampleHandler := NewAIOperationHandler("example", deps.Config, deps.AIClients["example"], deps.Logger)
            aiGroup.POST("/example", exampleHandler.Handle)
        }
    }
}
```

**Important notes:**
- Follow RESTful URL patterns (`/api/v1/ai/{operation-name}`)
- The operation name in the handler must exactly match the name used in registration
- AI clients are automatically initialized for all registered operations in `internal/cli/run.go`
- The generic handler automatically handles request parsing and response formatting

### 5. Configure Your Operation (Optional)

If your operation needs specific settings, you can add them to your configuration file:

```yaml
ai:
  operations:
    example:
      preset: "default"  # or a specific preset name
      timeout: "30s"     # operation-specific timeout
```

Your operation will use the default preset if no specific configuration is provided.

### 6. Document Your Operation

Consider updating the main `README.md` to document your new endpoint:

```markdown
## API Endpoints

- `POST /api/v1/ai/example`: [Brief description of what this operation does]
```

## Complete Example: Text Summarization

Let's walk through a complete example of adding a text summarization operation to Resumatter:

### 1. Types (`ai/types.go`):
```go
type SummarizeInput struct {
    Text       string `json:"text"`
    MaxLength  int    `json:"max_length,omitempty"`
}

type SummarizeOutput struct {
    Summary    string `json:"summary"`
    WordCount  int    `json:"word_count"`
    KeyPoints  []string `json:"key_points"`
}
```

### 2. Prompts (`ai/prompts/`):

Create `system_summarize.md` and `user_summarize.md` in `ai/prompts/`.

**`system_summarize.md`:**
```markdown
You are an expert content analyst and summarization specialist with expertise in:

- Extracting key information from complex texts
- Creating concise, accurate summaries
- Identifying main themes and important details
- Maintaining factual accuracy while condensing content
```

**`user_summarize.md`:**
```markdown
Please create a comprehensive summary of the provided text.

**Requirements:**
1. **Accuracy**: Maintain factual accuracy and key information
2. **Conciseness**: Create a clear, concise summary
3. **Key Points**: Extract 3-5 most important points
4. **Length**: Aim for approximately {{.MaxLength}} words (if specified)

**Text to Summarize:**
-----
{{.Text}}
-----
```

Then, update `ai/prompts/prompts.go`:
```go
package prompts

import _ "embed"

// ...

//go:embed system_summarize.md
var SystemSummarize string

//go:embed user_summarize.md
var UserSummarize string
```

### 3. Registration (`ai/operations.go`):
```go
registerOperation(AIOperationDefinition{
    Name:             "summarize",
    SystemPrompt:     prompts.SystemSummarize,
    UserPromptFormat: prompts.UserSummarize,
    NewInput:         func() any { return &SummarizeInput{} },
    NewOutput:        func() any { return &SummarizeOutput{} },
})
```

### 4. Route (`internal/server/routes.go`):
```go
exampleHandler := NewAIOperationHandler("summarize", deps.Config, deps.AIClients["summarize"], deps.Logger)
ai.POST("/summarize", exampleHandler.Handle)
```

## Testing Your New Operation

Once you've implemented your operation, here's how to test it:

1. **Build and start the server**:
   ```bash
   make build
   ./build/resumatter serve
   ```

2. **Test with curl**:
   ```bash
   curl -X POST http://localhost:8080/api/v1/ai/example \
     -H "Content-Type: application/json" \
     -d '{"input_field_1": "test value", "input_field_2": "another value"}'
   ```

3. **Create a test script**: Consider adding a test script in the `scripts/` directory following the pattern of existing scripts like `test-tailor.sh`.

## Understanding the Implementation Details

The AI operation system is built on several key components:

1. **Operation Registration**: Operations are registered in `ai/operations.go` with their prompts and type definitions
2. **Generic Execution**: `ExecuteAIOperation` in `ai/operations.go` handles the common logic for all operations
3. **HTTP Handlers**: `NewAIOperationHandler` in `internal/server/handlers.go` provides a generic HTTP interface
4. **Prompt Templating**: Uses Go's `text/template` for safe injection of input data into prompts
5. **Structured Responses**: Uses JSON schema for structured AI responses that are unmarshaled into Go structs
6. **Configuration Resolution**: Operation-specific configs are resolved from the main config in `config/resolver.go`

## Best Practices

Here are some tips for creating effective AI operations:

1. **Type Safety**: Use Go structs for input/output to ensure type safety and proper JSON serialization
2. **Error Handling**: The system provides consistent error responses automatically
3. **Prompt Engineering**: Study existing prompts in `ai/prompts/` for effective patterns
4. **Response Format**: All operations return a consistent JSON structure with metadata
5. **Configuration**: Operations inherit from presets but can be customized as needed
6. **Observability**: Circuit breakers, metrics, and tracing are built-in
7. **Input Validation**: While explicit validation tags aren't used, you can validate in your operation logic if needed. The `ShouldBindJSON` method in the handler provides basic JSON validation.

## Contributing

We welcome contributions of new AI operations! This architecture makes it easy to extend Resumatter's capabilities while maintaining code quality and consistency. If you're adding a new operation, please:

- Follow the patterns established by existing operations
- Include clear documentation and examples
- Test your operation thoroughly
- Consider adding example scripts in the `scripts/` directory

The modular design ensures that your new operations will automatically benefit from all the system's built-in features like rate limiting, circuit breakers, and observability.
