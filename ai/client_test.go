package ai

import (
	"context"
	"testing"

	"github.com/ajiwo/resumatter/config"
)

// TestNewClient_ErrorHandling verifies that the NewClient function correctly
// handles and returns errors under various failure conditions, such as when a
// downstream client fails to initialize or when an unsupported provider is specified.
func TestNewClient_ErrorHandling(t *testing.T) {
	cfg := &config.Config{
		AI: config.AIConfig{
			Presets: map[string]config.PresetConfig{
				"testpreset": {
					Provider:       "openai",
					Timeout:        "10ms",
					ProviderConfig: map[string]any{},
				},
			},
			Operations: map[string]config.OperationConfig{
				"testop": {
					Preset: "testpreset",
				},
			},
		},
	}
	ctx := context.Background()
	_, err := NewClient(ctx, cfg, "testop")
	if err == nil {
		t.Fatal("expected error due to missing API key or config, got nil")
	}
	// Instead of mutating the struct field in the map, reassign the map entry
	cfg.AI.Presets["testpreset"] = config.PresetConfig{
		Provider:       "unsupported",
		Timeout:        "10ms",
		ProviderConfig: map[string]any{},
	}
	_, err = NewClient(ctx, cfg, "testop")
	if err == nil {
		t.Error("expected error for unsupported provider")
	}
}
