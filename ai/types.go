package ai

// TailorResumeInput represents the input for tailoring a resume
type TailorResumeInput struct {
	BaseResume     string `json:"base_resume"`
	JobDescription string `json:"job_description"`
}

// ATSAnalysis represents the ATS scoring and analysis
type ATSAnalysis struct {
	Score      int    `json:"score"`
	Strengths  string `json:"strengths"`
	Weaknesses string `json:"weaknesses"`
}

// JobPostingAnalysis represents the analysis of the job posting
type JobPostingAnalysis struct {
	Clarity     string `json:"clarity"`
	Inclusivity string `json:"inclusivity"`
	Quality     string `json:"quality"`
}

// TailorResumeOutput represents the output from tailoring a resume
type TailorResumeOutput struct {
	TailoredResume     string             `json:"tailored_resume"`
	ATSAnalysis        ATSAnalysis        `json:"ats_analysis"`
	JobPostingAnalysis JobPostingAnalysis `json:"job_posting_analysis"`
}

// EvaluateResumeInput represents the input for evaluating a resume
type EvaluateResumeInput struct {
	BaseResume     string `json:"base_resume"`
	TailoredResume string `json:"tailored_resume"`
}

// EvaluationFinding represents a specific issue found in the evaluation
type EvaluationFinding struct {
	// "Overclaim", "Invention", or "Incorrect Linking"
	Type        string `json:"type"`
	Description string `json:"description"`
	Evidence    string `json:"evidence"`
}

// EvaluateResumeOutput represents the output from evaluating a resume
type EvaluateResumeOutput struct {
	Summary  string              `json:"summary"`
	Findings []EvaluationFinding `json:"findings"`
}

// AnalyzeJobInput represents the input for analyzing a job description
type AnalyzeJobInput struct {
	JobDescription string `json:"job_description"`
}

// JobQualityScore represents a scored aspect of job quality
type JobQualityScore struct {
	// 0-100 score
	Score int `json:"score"`
	// Detailed analysis
	Analysis string `json:"analysis"`
	// Specific improvement suggestions
	Improvements []string `json:"improvements"`
}

// InclusivityAnalysis represents inclusivity assessment with specific feedback
type InclusivityAnalysis struct {
	// 0-100 score
	Score int `json:"score"`
	// Overall assessment
	Analysis string `json:"analysis"`
	// Potentially problematic terms
	FlaggedTerms []string `json:"flagged_terms"`
	// Specific improvement suggestions
	Suggestions []string `json:"suggestions"`
}

// CandidateAttraction represents how well the job attracts candidates
type CandidateAttraction struct {
	// 0-100 score
	Score int `json:"score"`
	// What attracts candidates
	Strengths []string `json:"strengths"`
	// What might deter candidates
	Weaknesses []string `json:"weaknesses"`
}

// MarketCompetitiveness represents market analysis of the job posting
type MarketCompetitiveness struct {
	// Assessment of salary disclosure
	SalaryTransparency string `json:"salary_transparency"`
	// Whether requirements are realistic
	RequirementsRealism string `json:"requirements_realism"`
	// How well it aligns with industry standards
	IndustryAlignment string `json:"industry_alignment"`
}

// AnalyzeJobOutput represents the comprehensive output from job analysis
type AnalyzeJobOutput struct {
	// Overall score 0-100
	JobQualityScore int `json:"job_quality_score"`
	// Clarity assessment
	Clarity JobQualityScore `json:"clarity"`
	// Inclusivity analysis
	Inclusivity InclusivityAnalysis `json:"inclusivity"`
	// Attraction analysis
	CandidateAttraction CandidateAttraction `json:"candidate_attraction"`
	// Market analysis
	MarketCompetitiveness MarketCompetitiveness `json:"market_competitiveness"`
	// Top-level recommendations
	Recommendations []string `json:"recommendations"`
}

// GenerateGitCommitInput represents the input for generating git commit messages
type GenerateGitCommitInput struct {
	RecentCommitHistory string `json:"recent_commit_history"`
	GitDiff             string `json:"git_diff"`
}

// GenerateGitCommitOutput represents the output from git commit message generation
type GenerateGitCommitOutput struct {
	CommitMessage string   `json:"commit_message"`
	Subject       string   `json:"subject"`
	Body          string   `json:"body"`
	Type          string   `json:"type"` // feat, fix, docs, etc.
	Scope         string   `json:"scope"`
	Breaking      bool     `json:"breaking"`
	Issues        []string `json:"issues"`
}
