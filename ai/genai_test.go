package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/ajiwo/resumatter/config"
	"github.com/stretchr/testify/require"
	"google.golang.org/genai"
)

func TestParseGenAIConfig(t *testing.T) {
	cfg := &config.ResolvedOperationConfig{
		Provider: "genai-gemini",
		ProviderConfig: map[string]any{
			"api_key":              "k-123",
			"backend":              "gemini",
			"credentials_file":     "/tmp/creds.json",
			"credentials":          "{\"type\":\"service_account\"}",
			"google_cloud_project": "proj-1",
			"google_cloud_region":  "us-central1",
		},
	}

	gen := parseGenAIConfig(cfg)
	require.Equal(t, "k-123", gen.ApiKey)
	require.Equal(t, "gemini", gen.Backend)
	require.Equal(t, "/tmp/creds.json", gen.CredentialsFile)
	require.Equal(t, "{\"type\":\"service_account\"}", gen.CredentialsJSON)
	require.Equal(t, "proj-1", gen.GoogleCloudProject)
	require.Equal(t, "us-central1", gen.GoogleCloudRegion)
}

func TestNewGenAIClient_UnsupportedBackend(t *testing.T) {
	ctx := context.Background()
	cfg := &config.ResolvedOperationConfig{
		Provider: "genai-gemini",
		Model:    "gemini-3.0-pro",
		ProviderConfig: map[string]any{
			"backend": "invalid",
		},
	}

	_, err := newGenAIClient(ctx, cfg)
	require.Error(t, err)
	require.Contains(t, err.Error(), "unsupported GenAI backend")
}

func TestCreateGeminiClient_MissingAPIKey(t *testing.T) {
	ctx := context.Background()
	_, err := createGeminiClient(ctx, &config.GenAIConfig{Backend: "gemini", ApiKey: ""})
	require.Error(t, err)
	require.Contains(t, err.Error(), "API key is required for Gemini backend")
}

func TestNewGenAIClient_PropagatesGeminiCreationError(t *testing.T) {
	ctx := context.Background()
	cfg := &config.ResolvedOperationConfig{
		Provider: "genai-gemini",
		Model:    "gemini-2.0-flash",
		ProviderConfig: map[string]any{
			"backend": "gemini",
			"api_key": "", // triggers createGeminiClient error
		},
	}

	_, err := newGenAIClient(ctx, cfg)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to create GenAI client")
	require.Contains(t, err.Error(), "API key is required for Gemini backend")
}

func TestCreateVertexAIClient_WithAPIKeyShouldError(t *testing.T) {
	ctx := context.Background()
	_, err := createVertexAIClient(ctx, &config.GenAIConfig{
		Backend:            "vertexai",
		ApiKey:             "should-not-be-set",
		GoogleCloudProject: "proj",
		GoogleCloudRegion:  "us-central1",
	})
	require.Error(t, err)
	require.Contains(t, err.Error(), "api_key should not be set for Vertex AI backend")
}

func TestCreateVertexAIClient_MissingProject(t *testing.T) {
	ctx := context.Background()
	_, err := createVertexAIClient(ctx, &config.GenAIConfig{
		Backend:           "vertexai",
		GoogleCloudRegion: "us-central1",
	})
	require.Error(t, err)
	require.Contains(t, err.Error(), "google_cloud_project is required for Vertex AI backend")
}

func TestNewGenAIClient_PropagatesVertexCreationError(t *testing.T) {
	ctx := context.Background()
	cfg := &config.ResolvedOperationConfig{
		Provider: "genai-vertexai",
		Model:    "gemini-1.5-pro",
		ProviderConfig: map[string]any{
			"backend":             "vertexai",
			"google_cloud_region": "us-central1",
			// missing project triggers error
		},
	}

	_, err := newGenAIClient(ctx, cfg)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to create GenAI client")
	require.Contains(t, err.Error(), "google_cloud_project is required for Vertex AI backend")
}

// roundTripperFunc is a helper to stub HTTP round trips.
type roundTripperFunc func(*http.Request) (*http.Response, error)

func (f roundTripperFunc) RoundTrip(r *http.Request) (*http.Response, error) { return f(r) }

func TestGenAI_GenerateContent_Basic(t *testing.T) {
	t.Parallel()
	ctx := context.Background()

	var captured map[string]any
	transport := roundTripperFunc(func(r *http.Request) (*http.Response, error) {
		require.Equal(t, http.MethodPost, r.Method)
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)
		_ = r.Body.Close()

		err = json.Unmarshal(bodyBytes, &captured)
		require.NoError(t, err)

		// Return a minimal valid GenAI GenerateContentResponse
		respPayload := map[string]any{
			"candidates": []any{
				map[string]any{
					"content": map[string]any{
						"parts": []any{
							map[string]any{"text": "hello world"},
						},
					},
					"finishReason": "STOP",
				},
			},
			"usageMetadata": map[string]any{
				"promptTokenCount":     7,
				"candidatesTokenCount": 11,
				"totalTokenCount":      18,
			},
		}
		rb, _ := json.Marshal(respPayload)
		return &http.Response{
			StatusCode: http.StatusOK,
			Header:     http.Header{"Content-Type": []string{"application/json"}},
			Body:       io.NopCloser(bytes.NewReader(rb)),
			Request:    r,
		}, nil
	})

	httpClient := &http.Client{Transport: transport}
	gaClient, err := genai.NewClient(ctx, &genai.ClientConfig{
		Backend:    genai.BackendGeminiAPI,
		APIKey:     "test-key",
		HTTPClient: httpClient,
	})
	require.NoError(t, err)

	cfg := &config.ResolvedOperationConfig{
		Provider:    "genai-gemini",
		Model:       "gemini-2.0-flash",
		Temperature: 0.2,
		MaxTokens:   123,
	}
	client := &genAIClient{client: gaClient, config: cfg}

	req := &GenerateRequest{
		Messages: []Message{{Role: "user", Content: "Hi"}},
	}

	resp, err := client.GenerateContent(ctx, req)
	require.NoError(t, err)

	// Verify decoded response
	require.NotNil(t, resp)
	require.Equal(t, "hello world", resp.Content)
	require.Equal(t, "STOP", resp.FinishReason)
	require.NotNil(t, resp.Usage)
	require.Equal(t, 7, resp.Usage.PromptTokens)
	require.Equal(t, 11, resp.Usage.CompletionTokens)
	require.Equal(t, 18, resp.Usage.TotalTokens)
	require.Equal(t, map[string]any{"provider": "genai-gemini", "model": "gemini-2.0-flash"}, resp.Metadata)

	// Verify request payload sent to GenAI
	// SDK may place model in URL; focus on config
	// SDK may serialize as "config" (Vertex) or "generationConfig" (Gemini)
	var cfgMap map[string]any
	if m, ok := captured["config"].(map[string]any); ok {
		cfgMap = m
	} else if m, ok := captured["generationConfig"].(map[string]any); ok {
		cfgMap = m
	}
	require.NotNil(t, cfgMap)
	require.InDelta(t, 0.2, cfgMap["temperature"].(float64), 1e-6)
	require.EqualValues(t, 123, cfgMap["maxOutputTokens"])
	_, hasSys := cfgMap["systemInstruction"]
	require.False(t, hasSys) // not provided
	_, hasMime := cfgMap["responseMimeType"]
	require.False(t, hasMime) // not set
	_, hasSchema := cfgMap["responseJsonSchema"]
	require.False(t, hasSchema)

	contents := captured["contents"].([]any)
	require.Len(t, contents, 1)
	first := contents[0].(map[string]any)
	parts := first["parts"].([]any)
	require.Len(t, parts, 1)
	p0 := parts[0].(map[string]any)
	require.Equal(t, "Hi", p0["text"])
}

func TestGenAI_GenerateContent_WithOverridesAndSchema(t *testing.T) {
	t.Parallel()
	ctx := context.Background()

	var captured map[string]any
	transport := roundTripperFunc(func(r *http.Request) (*http.Response, error) {
		bodyBytes, err := io.ReadAll(r.Body)
		require.NoError(t, err)
		_ = r.Body.Close()
		_ = json.Unmarshal(bodyBytes, &captured)

		// Respond with a successful body
		respPayload := map[string]any{
			"candidates": []any{
				map[string]any{
					"content": map[string]any{
						"parts": []any{
							map[string]any{"text": "structured"},
						},
					},
					"finishReason": "STOP",
				},
			},
			"usageMetadata": map[string]any{
				"promptTokenCount":     1,
				"candidatesTokenCount": 2,
				"totalTokenCount":      3,
			},
		}
		rb, _ := json.Marshal(respPayload)
		return &http.Response{
			StatusCode: http.StatusOK,
			Header:     http.Header{"Content-Type": []string{"application/json"}},
			Body:       io.NopCloser(bytes.NewReader(rb)),
			Request:    r,
		}, nil
	})

	httpClient := &http.Client{Transport: transport}
	gaClient, err := genai.NewClient(ctx, &genai.ClientConfig{
		Backend:    genai.BackendGeminiAPI,
		APIKey:     "test-key",
		HTTPClient: httpClient,
	})
	require.NoError(t, err)

	cfg := &config.ResolvedOperationConfig{
		Provider:    "genai-gemini",
		Model:       "gemini-2.0-flash",
		Temperature: 0.9, // will be overridden
		MaxTokens:   999, // will be overridden
	}
	client := &genAIClient{client: gaClient, config: cfg}

	// Request overrides and schema
	temp := 0.3
	maxTok := 10
	schema := map[string]any{
		"type":       "object",
		"properties": map[string]any{"x": map[string]any{"type": "string"}},
	}
	req := &GenerateRequest{
		Messages:          []Message{{Role: "user", Content: "Return JSON"}},
		SystemInstruction: "Keep answers short",
		ResponseSchema:    schema,
		// ResponseMIMEType intentionally empty to test defaulting to application/json
		Temperature: &temp,
		MaxTokens:   &maxTok,
	}

	resp, err := client.GenerateContent(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.Equal(t, "structured", resp.Content)
	require.Equal(t, "STOP", resp.FinishReason)
	require.NotNil(t, resp.Usage)
	require.Equal(t, 1, resp.Usage.PromptTokens)
	require.Equal(t, 2, resp.Usage.CompletionTokens)
	require.Equal(t, 3, resp.Usage.TotalTokens)

	// Verify request payload used overrides and schema/mimetype
	// SDK may serialize as "config" (Vertex) or "generationConfig" (Gemini)
	var cfgMap map[string]any
	if m, ok := captured["config"].(map[string]any); ok {
		cfgMap = m
	} else if m, ok := captured["generationConfig"].(map[string]any); ok {
		cfgMap = m
	}
	require.NotNil(t, cfgMap)
	require.InDelta(t, 0.3, cfgMap["temperature"].(float64), 1e-6)
	require.EqualValues(t, 10, cfgMap["maxOutputTokens"])

	// System instruction present (may be nested or top-level depending on backend)
	var sysAny any
	if cfgMap != nil {
		sysAny = cfgMap["systemInstruction"]
	}
	if sysAny == nil {
		sysAny = captured["systemInstruction"]
	}
	if sysAny == nil {
		sysAny = captured["system_instruction"]
	}
	require.NotNil(t, sysAny)
	sys, ok := sysAny.(map[string]any)
	require.True(t, ok)
	parts := sys["parts"].([]any)
	require.Len(t, parts, 1)
	p0 := parts[0].(map[string]any)
	require.Equal(t, "Keep answers short", p0["text"])

	// Schema and MIME type present/defaulted
	require.Equal(t, "application/json", cfgMap["responseMimeType"])
	require.NotNil(t, cfgMap["responseJsonSchema"])
}

func TestBuildGenAIContents_EmptyMessages(t *testing.T) {
	_, err := buildGenAIContents(nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "no messages provided")
}

func TestBuildGenAIContents_OnlySystem(t *testing.T) {
	msgs := []Message{{Role: "system", Content: "sys"}}
	_, err := buildGenAIContents(msgs)
	require.Error(t, err)
	require.Contains(t, err.Error(), "no non-system messages")
}

func TestBuildGenAIContents_RoleMappingAndText(t *testing.T) {
	msgs := []Message{
		{Role: "system", Content: "ignored"},
		{Role: "user", Content: "hello"},
		{Role: "assistant", Content: "there"},
	}
	contents, err := buildGenAIContents(msgs)
	require.NoError(t, err)
	require.Len(t, contents, 2)

	// Marshal to JSON to inspect fields (role, parts[0].text)
	data, err := json.Marshal(contents)
	require.NoError(t, err)

	var out []map[string]any
	require.NoError(t, json.Unmarshal(data, &out))
	require.Equal(t, "user", out[0]["role"]) // first non-system remains user
	parts0 := out[0]["parts"].([]any)
	p0 := parts0[0].(map[string]any)
	require.Equal(t, "hello", p0["text"])

	require.Equal(t, "model", out[1]["role"]) // assistant mapped to model
	parts1 := out[1]["parts"].([]any)
	p1 := parts1[0].(map[string]any)
	require.Equal(t, "there", p1["text"])
}
