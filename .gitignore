# Build artifacts
/build/
/dist/

# Coverage reports
/coverage/
*.out
*.html

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.prof
*.pprof

# Dependency directories
vendor/

# Environment and config files
.env
.env.local
.env.*.local
config.yaml
config.local.yaml
config.local.yml

# Keys and certificates
.key/
*.key
*.pem
*.crt
*.p12
*.pfx

# Temporary files
tmp_*
tmp/
temp/
.tmp/

# Log files
*.log
logs/

# Air live reload
.air.toml.local
tmp/

# Test artifacts
testdata/output/
*.test.json

# Development tools
.golangci.yml.local

# Backup files
*.bak
*.backup

# Local development overrides
docker-compose.override.yml
Makefile.local
