package file

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sync"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/sdk/instrumentation"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
	sdkmetricdata "go.opentelemetry.io/otel/sdk/metric/metricdata"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	"go.opentelemetry.io/otel/trace"
)

// FileExporter exports OpenTelemetry metrics and traces to a local file.
// It satisfies both the sdkmetric.Exporter and sdktrace.SpanExporter interfaces.
type FileExporter struct {
	filePath string
	file     *os.File
	mu       sync.Mutex
}

// serializableSpan is a struct designed for easy JSON serialization of a span.
type serializableSpan struct {
	Name                   string                `json:"name"`
	SpanContext            trace.SpanContext     `json:"span_context"`
	Parent                 trace.SpanContext     `json:"parent"`
	SpanKind               trace.SpanKind        `json:"span_kind"`
	StartTime              time.Time             `json:"start_time"`
	EndTime                time.Time             `json:"end_time"`
	Attributes             []attribute.KeyValue  `json:"attributes"`
	Events                 []serializableEvent   `json:"events"`
	Links                  []sdktrace.Link       `json:"links"`
	Status                 serializableStatus    `json:"status"`
	DroppedAttributes      int                   `json:"dropped_attributes"`
	DroppedEvents          int                   `json:"dropped_events"`
	DroppedLinks           int                   `json:"dropped_links"`
	ChildSpanCount         int                   `json:"child_span_count"`
	Resource               *resource.Resource    `json:"resource"`
	InstrumentationLibrary instrumentation.Scope `json:"instrumentation_library"`
}

// serializableStatus is a serializable representation of a span's status.
type serializableStatus struct {
	Code        codes.Code `json:"code"`
	Description string     `json:"description"`
}

// serializableEvent is a serializable representation of a span's event.
type serializableEvent struct {
	Name       string               `json:"name"`
	Attributes []attribute.KeyValue `json:"attributes"`
	Time       time.Time            `json:"time"`
}

// NewFileExporter creates a new FileExporter.
func NewFileExporter(filePath string) (*FileExporter, error) {
	file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open metrics file %s: %w", filePath, err)
	}

	return &FileExporter{
			filePath: filePath,
			file:     file,
		},
		nil
}

// --- sdkmetric.Exporter implementation ---

func (e *FileExporter) Aggregation(kind sdkmetric.InstrumentKind) sdkmetric.Aggregation {
	return sdkmetric.DefaultAggregationSelector(kind)
}

// Temporality returns the Temporality to use for an instrument kind.
func (e *FileExporter) Temporality(kind sdkmetric.InstrumentKind) sdkmetricdata.Temporality {
	return sdkmetric.DefaultTemporalitySelector(kind)
}

// Export exports a batch of metrics to the file.
func (e *FileExporter) Export(ctx context.Context, rm *sdkmetricdata.ResourceMetrics) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.file == nil {
		return errors.New("exporter is shut down")
	}

	data, err := json.Marshal(rm)
	if err != nil {
		return fmt.Errorf("failed to marshal ResourceMetrics to JSON: %w", err)
	}

	return e.writeToFile(data)
}

// --- sdktrace.SpanExporter implementation ---

// ExportSpans exports a batch of spans to the file.
func (e *FileExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.file == nil {
		return errors.New("exporter is shut down")
	}

	for _, s := range spans {
		serializable := toSerializableSpan(s)
		data, err := json.Marshal(serializable)
		if err != nil {
			return fmt.Errorf("failed to marshal span to JSON: %w", err)
		}
		if err := e.writeToFile(data); err != nil {
			return err
		}
	}

	return nil
}

// --- Common methods ---

// ForceFlush flushes the exporter.
func (e *FileExporter) ForceFlush(ctx context.Context) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.file != nil {
		return e.file.Sync()
	}
	return nil
}

// Shutdown shuts down the exporter.
func (e *FileExporter) Shutdown(ctx context.Context) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.file != nil {
		err := e.file.Close()
		e.file = nil // Ensure file handle is cleared even if close fails
		if err != nil {
			return fmt.Errorf("failed to close file %s: %w", e.filePath, err)
		}
	}
	return nil
}

// writeToFile is a helper to write data and a newline to the file.
func (e *FileExporter) writeToFile(data []byte) error {
	if _, err := e.file.Write(data); err != nil {
		return fmt.Errorf("failed to write to file %s: %w", e.filePath, err)
	}
	if _, err := e.file.WriteString("\n"); err != nil {
		return fmt.Errorf("failed to write newline to file %s: %w", e.filePath, err)
	}
	return nil
}

// toSerializableSpan converts a ReadOnlySpan to a serializable format.
func toSerializableSpan(s sdktrace.ReadOnlySpan) serializableSpan {
	events := make([]serializableEvent, len(s.Events()))
	for i, e := range s.Events() {
		events[i] = serializableEvent{
			Name:       e.Name,
			Attributes: e.Attributes,
			Time:       e.Time,
		}
	}

	return serializableSpan{
		Name:                   s.Name(),
		SpanContext:            s.SpanContext(),
		Parent:                 s.Parent(),
		SpanKind:               s.SpanKind(),
		StartTime:              s.StartTime(),
		EndTime:                s.EndTime(),
		Attributes:             s.Attributes(),
		Events:                 events,
		Links:                  s.Links(),
		Status:                 serializableStatus{Code: s.Status().Code, Description: s.Status().Description},
		DroppedAttributes:      s.DroppedAttributes(),
		DroppedEvents:          s.DroppedEvents(),
		DroppedLinks:           s.DroppedLinks(),
		ChildSpanCount:         s.ChildSpanCount(),
		Resource:               s.Resource(),
		InstrumentationLibrary: s.InstrumentationScope(),
	}
}
