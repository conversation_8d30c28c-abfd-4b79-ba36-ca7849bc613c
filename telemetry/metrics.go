package telemetry

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

// Metrics holds all application metrics
type Metrics struct {
	// HTTP metrics
	HTTPRequestsTotal   metric.Int64Counter
	HTTPRequestDuration metric.Float64Histogram
	HTTPRequestSize     metric.Int64Histogram
	HTTPResponseSize    metric.Int64Histogram

	// AI operation metrics
	AIRequestsTotal   metric.Int64Counter
	AIRequestDuration metric.Float64Histogram
	AITokensUsed      metric.Int64Counter
	AIProviderErrors  metric.Int64Counter

	// Circuit breaker metrics
	CircuitBreakerState    metric.Int64Gauge
	CircuitBreakerRequests metric.Int64Counter

	// Application metrics
	ConfigReloads     metric.Int64Counter
	ActiveConnections metric.Int64Gauge
}

// NewMetrics creates a new metrics collection
func NewMetrics(meter metric.Meter) (*Metrics, error) {
	m := &Metrics{}

	var err error

	// HTTP metrics
	m.HTTPRequestsTotal, err = meter.Int64Counter(
		"http_requests_total",
		metric.WithDescription("Total number of HTTP requests"),
		metric.WithUnit("{request}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create http_requests_total counter: %w", err)
	}

	m.HTTPRequestDuration, err = meter.Float64Histogram(
		"http_request_duration_seconds",
		metric.WithDescription("HTTP request duration in seconds"),
		metric.WithUnit("s"),
		metric.WithExplicitBucketBoundaries(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create http_request_duration_seconds histogram: %w", err)
	}

	m.HTTPRequestSize, err = meter.Int64Histogram(
		"http_request_size_bytes",
		metric.WithDescription("HTTP request size in bytes"),
		metric.WithUnit("By"),
		metric.WithExplicitBucketBoundaries(100, 1000, 10000, 100000, 1000000),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create http_request_size_bytes histogram: %w", err)
	}

	m.HTTPResponseSize, err = meter.Int64Histogram(
		"http_response_size_bytes",
		metric.WithDescription("HTTP response size in bytes"),
		metric.WithUnit("By"),
		metric.WithExplicitBucketBoundaries(100, 1000, 10000, 100000, 1000000),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create http_response_size_bytes histogram: %w", err)
	}

	// AI operation metrics
	m.AIRequestsTotal, err = meter.Int64Counter(
		"ai_requests_total",
		metric.WithDescription("Total number of AI requests"),
		metric.WithUnit("{request}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create ai_requests_total counter: %w", err)
	}

	m.AIRequestDuration, err = meter.Float64Histogram(
		"ai_request_duration_seconds",
		metric.WithDescription("AI request duration in seconds"),
		metric.WithUnit("s"),
		metric.WithExplicitBucketBoundaries(0.1, 0.5, 1, 2, 5, 10, 30, 60, 120),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create ai_request_duration_seconds histogram: %w", err)
	}

	m.AITokensUsed, err = meter.Int64Counter(
		"ai_tokens_used_total",
		metric.WithDescription("Total number of AI tokens used"),
		metric.WithUnit("{token}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create ai_tokens_used_total counter: %w", err)
	}

	m.AIProviderErrors, err = meter.Int64Counter(
		"ai_provider_errors_total",
		metric.WithDescription("Total number of AI provider errors"),
		metric.WithUnit("{error}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create ai_provider_errors_total counter: %w", err)
	}

	// Circuit breaker metrics
	m.CircuitBreakerState, err = meter.Int64Gauge(
		"circuit_breaker_state",
		metric.WithDescription("Circuit breaker state (0=closed, 1=open, 2=half-open)"),
		metric.WithUnit("{state}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create circuit_breaker_state gauge: %w", err)
	}

	m.CircuitBreakerRequests, err = meter.Int64Counter(
		"circuit_breaker_requests_total",
		metric.WithDescription("Total number of circuit breaker requests"),
		metric.WithUnit("{request}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create circuit_breaker_requests_total counter: %w", err)
	}

	// Application metrics
	m.ConfigReloads, err = meter.Int64Counter(
		"config_reloads_total",
		metric.WithDescription("Total number of configuration reloads"),
		metric.WithUnit("{reload}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create config_reloads_total counter: %w", err)
	}

	m.ActiveConnections, err = meter.Int64Gauge(
		"active_connections",
		metric.WithDescription("Number of active connections"),
		metric.WithUnit("{connection}"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create active_connections gauge: %w", err)
	}

	return m, nil
}

// RecordHTTPRequest records HTTP request metrics
func (m *Metrics) RecordHTTPRequest(ctx context.Context, method, route, status string, duration time.Duration, requestSize, responseSize int64) {
	attrs := []attribute.KeyValue{
		attribute.String("method", method),
		attribute.String("route", route),
		attribute.String("status", status),
	}

	m.HTTPRequestsTotal.Add(ctx, 1, metric.WithAttributes(attrs...))
	m.HTTPRequestDuration.Record(ctx, duration.Seconds(), metric.WithAttributes(attrs...))

	if requestSize > 0 {
		m.HTTPRequestSize.Record(ctx, requestSize, metric.WithAttributes(attrs...))
	}
	if responseSize > 0 {
		m.HTTPResponseSize.Record(ctx, responseSize, metric.WithAttributes(attrs...))
	}
}

// RecordAIRequest records AI operation metrics
func (m *Metrics) RecordAIRequest(ctx context.Context, operation, provider, status string, duration time.Duration) {
	attrs := []attribute.KeyValue{
		attribute.String("operation", operation),
		attribute.String("provider", provider),
		attribute.String("status", status),
	}

	m.AIRequestsTotal.Add(ctx, 1, metric.WithAttributes(attrs...))
	m.AIRequestDuration.Record(ctx, duration.Seconds(), metric.WithAttributes(attrs...))
}

// RecordAITokens records AI token usage
func (m *Metrics) RecordAITokens(ctx context.Context, operation, provider, tokenType string, count int64) {
	attrs := []attribute.KeyValue{
		attribute.String("operation", operation),
		attribute.String("provider", provider),
		attribute.String("type", tokenType), // "prompt", "completion", "total"
	}

	m.AITokensUsed.Add(ctx, count, metric.WithAttributes(attrs...))
}

// RecordAIError records AI provider errors
func (m *Metrics) RecordAIError(ctx context.Context, operation, provider, errorType string) {
	attrs := []attribute.KeyValue{
		attribute.String("operation", operation),
		attribute.String("provider", provider),
		attribute.String("error_type", errorType),
	}

	m.AIProviderErrors.Add(ctx, 1, metric.WithAttributes(attrs...))
}

// RecordCircuitBreakerState records circuit breaker state
func (m *Metrics) RecordCircuitBreakerState(ctx context.Context, operation, provider string, state int64) {
	attrs := []attribute.KeyValue{
		attribute.String("operation", operation),
		attribute.String("provider", provider),
	}

	m.CircuitBreakerState.Record(ctx, state, metric.WithAttributes(attrs...))
}

// RecordCircuitBreakerRequest records circuit breaker requests
func (m *Metrics) RecordCircuitBreakerRequest(ctx context.Context, operation, provider, result string) {
	attrs := []attribute.KeyValue{
		attribute.String("operation", operation),
		attribute.String("provider", provider),
		attribute.String("result", result), // "success", "failure", "rejected"
	}

	m.CircuitBreakerRequests.Add(ctx, 1, metric.WithAttributes(attrs...))
}

// RecordConfigReload records configuration reload events
func (m *Metrics) RecordConfigReload(ctx context.Context, success bool) {
	status := "success"
	if !success {
		status = "failure"
	}

	attrs := []attribute.KeyValue{
		attribute.String("status", status),
	}

	m.ConfigReloads.Add(ctx, 1, metric.WithAttributes(attrs...))
}

// SetActiveConnections sets the current number of active connections
func (m *Metrics) SetActiveConnections(ctx context.Context, count int64) {
	m.ActiveConnections.Record(ctx, count)
}
