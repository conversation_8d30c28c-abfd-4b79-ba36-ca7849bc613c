package telemetry

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"time"

	"go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploggrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploghttp"
	"go.opentelemetry.io/otel/log"
	"go.opentelemetry.io/otel/log/global"
	"go.opentelemetry.io/otel/log/noop"
	sdklog "go.opentelemetry.io/otel/sdk/log"
	"go.opentelemetry.io/otel/sdk/resource"
)

// LoggingConfig holds logging configuration for telemetry
type LoggingConfig struct {
	Enabled        bool              `mapstructure:"enabled" yaml:"enabled"`
	Exporter       string            `mapstructure:"exporter" yaml:"exporter"`   // "otlp", "file", "console"
	FilePath       string            `mapstructure:"file_path" yaml:"file_path"` // for file exporter
	OTLPEndpoint   string            `mapstructure:"otlp_endpoint" yaml:"otlp_endpoint"`
	OTLPProtocol   string            `mapstructure:"otlp_protocol" yaml:"otlp_protocol"` // "grpc", "http"
	OTLPURLPath    string            `mapstructure:"otlp_url_path" yaml:"otlp_url_path"`
	OTLPHeaders    map[string]string `mapstructure:"otlp_headers" yaml:"otlp_headers"`
	OTLPInsecure   bool              `mapstructure:"otlp_insecure" yaml:"otlp_insecure"`
	BatchProcessor BatchConfig       `mapstructure:"batch_processor" yaml:"batch_processor"`
}

// BatchConfig holds batch processor configuration
type BatchConfig struct {
	MaxQueueSize       int `mapstructure:"max_queue_size" yaml:"max_queue_size"`
	ExportTimeout      int `mapstructure:"export_timeout_ms" yaml:"export_timeout_ms"`
	ExportInterval     int `mapstructure:"export_interval_ms" yaml:"export_interval_ms"`
	MaxExportBatchSize int `mapstructure:"max_export_batch_size" yaml:"max_export_batch_size"`
}

// LoggingProvider manages OpenTelemetry logging infrastructure
type LoggingProvider struct {
	loggerProvider *sdklog.LoggerProvider
	config         LoggingConfig
}

// NewLoggingProvider creates a new logging provider with OTLP exporters
func NewLoggingProvider(ctx context.Context, cfg LoggingConfig, res *resource.Resource) (*LoggingProvider, error) {
	if !cfg.Enabled {
		// Set no-op logger provider
		global.SetLoggerProvider(noop.NewLoggerProvider())
		return &LoggingProvider{config: cfg}, nil
	}

	// Create log exporter
	exporter, err := createLogExporter(ctx, cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create log exporter: %w", err)
	}

	// Create batch processor options
	batchOpts := []sdklog.BatchProcessorOption{}
	if cfg.BatchProcessor.MaxQueueSize > 0 {
		batchOpts = append(batchOpts, sdklog.WithMaxQueueSize(cfg.BatchProcessor.MaxQueueSize))
	}
	if cfg.BatchProcessor.ExportTimeout > 0 {
		batchOpts = append(batchOpts, sdklog.WithExportTimeout(time.Duration(cfg.BatchProcessor.ExportTimeout)*time.Millisecond))
	}
	if cfg.BatchProcessor.ExportInterval > 0 {
		batchOpts = append(batchOpts, sdklog.WithExportInterval(time.Duration(cfg.BatchProcessor.ExportInterval)*time.Millisecond))
	}
	if cfg.BatchProcessor.MaxExportBatchSize > 0 {
		batchOpts = append(batchOpts, sdklog.WithExportMaxBatchSize(cfg.BatchProcessor.MaxExportBatchSize))
	}

	// Create logger provider
	loggerProvider := sdklog.NewLoggerProvider(
		sdklog.WithProcessor(sdklog.NewBatchProcessor(exporter, batchOpts...)),
		sdklog.WithResource(res),
	)

	// Set global logger provider
	global.SetLoggerProvider(loggerProvider)

	return &LoggingProvider{
		loggerProvider: loggerProvider,
		config:         cfg,
	}, nil
}

// createLogExporter creates a log exporter based on configuration
func createLogExporter(ctx context.Context, cfg LoggingConfig) (sdklog.Exporter, error) {
	switch cfg.Exporter {
	case "otlp":
		return createOTLPLogExporter(ctx, cfg)
	case "file":
		return createFileLogExporter(cfg)
	case "console":
		return createConsoleLogExporter()
	default:
		return nil, fmt.Errorf("unsupported log exporter: %s", cfg.Exporter)
	}
}

// createOTLPLogExporter creates an OTLP log exporter
func createOTLPLogExporter(ctx context.Context, cfg LoggingConfig) (sdklog.Exporter, error) {
	switch cfg.OTLPProtocol {
	case "grpc":
		return createGRPCLogExporter(ctx, cfg)
	case "http":
		return createHTTPLogExporter(ctx, cfg)
	default:
		return nil, fmt.Errorf("unsupported OTLP log protocol: %s", cfg.OTLPProtocol)
	}
}

// createGRPCLogExporter creates a gRPC OTLP log exporter
func createGRPCLogExporter(ctx context.Context, cfg LoggingConfig) (sdklog.Exporter, error) {
	opts := []otlploggrpc.Option{
		otlploggrpc.WithEndpoint(cfg.OTLPEndpoint),
	}

	if cfg.OTLPInsecure {
		opts = append(opts, otlploggrpc.WithInsecure())
	}

	if len(cfg.OTLPHeaders) > 0 {
		opts = append(opts, otlploggrpc.WithHeaders(cfg.OTLPHeaders))
	}

	return otlploggrpc.New(ctx, opts...)
}

// createHTTPLogExporter creates an HTTP OTLP log exporter
func createHTTPLogExporter(ctx context.Context, cfg LoggingConfig) (sdklog.Exporter, error) {
	opts := []otlploghttp.Option{
		otlploghttp.WithEndpoint(cfg.OTLPEndpoint),
	}

	if cfg.OTLPURLPath != "" {
		opts = append(opts, otlploghttp.WithURLPath(cfg.OTLPURLPath))
	}

	if cfg.OTLPInsecure {
		opts = append(opts, otlploghttp.WithInsecure())
	}

	if len(cfg.OTLPHeaders) > 0 {
		opts = append(opts, otlploghttp.WithHeaders(cfg.OTLPHeaders))
	}

	return otlploghttp.New(ctx, opts...)
}

// createFileLogExporter creates a file log exporter
func createFileLogExporter(cfg LoggingConfig) (sdklog.Exporter, error) {
	if cfg.FilePath == "" {
		return nil, fmt.Errorf("file_path is required for file log exporter")
	}

	// Create a dedicated file log exporter
	return newFileLogExporter(cfg.FilePath)
}

// createConsoleLogExporter creates a console log exporter
func createConsoleLogExporter() (sdklog.Exporter, error) {
	// Create a simple console exporter that writes to stdout
	return &consoleLogExporter{writer: os.Stdout}, nil
}

// consoleLogExporter is a simple console log exporter
type consoleLogExporter struct {
	writer io.Writer
}

func (e *consoleLogExporter) Export(ctx context.Context, records []sdklog.Record) error {
	for _, record := range records {
		// Simple JSON output for console
		fmt.Fprintf(e.writer, `{"timestamp":"%s","level":"%s","message":"%s","attributes":%s}`+"\n",
			record.Timestamp().Format(time.RFC3339),
			record.Severity().String(),
			record.Body().AsString(),
			formatAttributes(record),
		)
	}
	return nil
}

func (e *consoleLogExporter) Shutdown(ctx context.Context) error {
	return nil
}

func (e *consoleLogExporter) ForceFlush(ctx context.Context) error {
	return nil
}

// fileLogExporter is a file-based log exporter
type fileLogExporter struct {
	file *os.File
}

func newFileLogExporter(filePath string) (*fileLogExporter, error) {
	file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file %s: %w", filePath, err)
	}

	return &fileLogExporter{file: file}, nil
}

func (e *fileLogExporter) Export(ctx context.Context, records []sdklog.Record) error {
	for _, record := range records {
		// JSON output for file
		line := fmt.Sprintf(`{"timestamp":"%s","level":"%s","message":"%s","attributes":%s}`+"\n",
			record.Timestamp().Format(time.RFC3339),
			record.Severity().String(),
			record.Body().AsString(),
			formatAttributes(record),
		)

		if _, err := e.file.WriteString(line); err != nil {
			return fmt.Errorf("failed to write log record: %w", err)
		}
	}
	return nil
}

func (e *fileLogExporter) Shutdown(ctx context.Context) error {
	if e.file != nil {
		return e.file.Close()
	}
	return nil
}

func (e *fileLogExporter) ForceFlush(ctx context.Context) error {
	if e.file != nil {
		return e.file.Sync()
	}
	return nil
}

// formatAttributes formats log attributes as JSON string
func formatAttributes(record sdklog.Record) string {
	attrs := make(map[string]any)

	// Walk through all attributes
	record.WalkAttributes(func(kv log.KeyValue) bool {
		attrs[kv.Key] = formatValue(kv.Value)
		return true
	})

	// Add trace information if available
	if record.TraceID().IsValid() {
		attrs["trace_id"] = record.TraceID().String()
	}
	if record.SpanID().IsValid() {
		attrs["span_id"] = record.SpanID().String()
	}

	if len(attrs) == 0 {
		return "{}"
	}

	// Use json.Marshal for proper JSON formatting
	jsonBytes, err := json.Marshal(attrs)
	if err != nil {
		// Fallback to simple formatting if marshaling fails
		return fmt.Sprintf(`{"error":"failed to marshal attributes: %v"}`, err)
	}
	return string(jsonBytes)
}

// formatValue converts a log.Value to a simple interface{} for JSON formatting
func formatValue(v log.Value) any {
	switch v.Kind() {
	case log.KindBool:
		return v.AsBool()
	case log.KindFloat64:
		return v.AsFloat64()
	case log.KindInt64:
		return v.AsInt64()
	case log.KindString:
		return v.AsString()
	case log.KindBytes:
		return string(v.AsBytes())
	case log.KindSlice:
		slice := v.AsSlice()
		result := make([]any, len(slice))
		for i, item := range slice {
			result[i] = formatValue(item)
		}
		return result
	case log.KindMap:
		kvs := v.AsMap()
		result := make(map[string]any)
		for _, kv := range kvs {
			result[kv.Key] = formatValue(kv.Value)
		}
		return result
	default:
		return v.String()
	}
}

// Shutdown gracefully shuts down the logging provider
func (p *LoggingProvider) Shutdown(ctx context.Context) error {
	if p.loggerProvider != nil {
		return p.loggerProvider.Shutdown(ctx)
	}
	return nil
}

// ForceFlush forces a flush of the logging provider
func (p *LoggingProvider) ForceFlush(ctx context.Context) error {
	if p.loggerProvider != nil {
		return p.loggerProvider.ForceFlush(ctx)
	}
	return nil
}
