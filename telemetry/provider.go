package telemetry

import (
	"context"
	"fmt"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/telemetry/exporters/file"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetrichttp"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/metric"
	noopMetrics "go.opentelemetry.io/otel/metric/noop"
	"go.opentelemetry.io/otel/propagation"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
	"go.opentelemetry.io/otel/trace"
	noopTrace "go.opentelemetry.io/otel/trace/noop"
)

// Provider manages OpenTelemetry providers and exporters
type Provider struct {
	tracerProvider  *sdktrace.TracerProvider
	meterProvider   *sdkmetric.MeterProvider
	loggingProvider *LoggingProvider
	tracer          trace.Tracer
	meter           metric.Meter
	config          config.ObservabilityConfig
}

// NewProvider creates a new OpenTelemetry provider with OTLP exporters
func NewProvider(ctx context.Context, cfg config.ObservabilityConfig) (*Provider, error) {
	// Create resource with service information
	res, err := createResource(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}

	provider := &Provider{
		config: cfg,
	}

	// Initialize tracing if enabled
	if cfg.TracingEnabled {
		if err := provider.initTracing(ctx, res); err != nil {
			return nil, fmt.Errorf("failed to initialize tracing: %w", err)
		}
	}

	// Initialize metrics if enabled
	if cfg.MetricsEnabled {
		if err := provider.initMetrics(ctx, res); err != nil {
			return nil, fmt.Errorf("failed to initialize metrics: %w", err)
		}
	}

	// Initialize logging if enabled
	if cfg.LoggingEnabled {
		if err := provider.initLogging(ctx, res); err != nil {
			return nil, fmt.Errorf("failed to initialize logging: %w", err)
		}
	}

	// Set global propagator for trace context propagation
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	return provider, nil
}

// createResource creates an OpenTelemetry resource with service information
func createResource(cfg config.ObservabilityConfig) (*resource.Resource, error) {
	return resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(cfg.ServiceName),
			semconv.ServiceVersion(cfg.ServiceVersion),
			semconv.DeploymentEnvironmentName(cfg.Environment),
		),
	)
}

// initTracing initializes the trace provider with OTLP exporter
func (p *Provider) initTracing(ctx context.Context, res *resource.Resource) error {
	// Create OTLP trace exporter
	exporter, err := p.createTraceExporter(ctx)
	if err != nil {
		return fmt.Errorf("failed to create trace exporter: %w", err)
	}

	// Create trace provider
	p.tracerProvider = sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sdktrace.AlwaysSample()),
	)

	// Set global tracer provider
	otel.SetTracerProvider(p.tracerProvider)

	// Create tracer for this service
	p.tracer = p.tracerProvider.Tracer(
		p.config.ServiceName,
		trace.WithInstrumentationVersion(p.config.ServiceVersion),
		trace.WithSchemaURL(semconv.SchemaURL),
	)

	return nil
}

// createTraceExporter creates a trace exporter based on the configured type.
func (p *Provider) createTraceExporter(ctx context.Context) (sdktrace.SpanExporter, error) {
	switch p.config.TraceExporter {
	case "otlp":
		// For OTLP, we further select based on the protocol (gRPC or HTTP)
		switch p.config.OTLPTraceProtocol {
		case "grpc":
			return p.createGRPCTraceExporter(ctx)
		case "http":
			return p.createHTTPTraceExporter(ctx)
		default:
			return nil, fmt.Errorf("unsupported OTLP trace protocol: %s", p.config.OTLPTraceProtocol)
		}
	case "file":
		return file.NewFileExporter(p.config.TraceFilePath)
	default:
		return nil, fmt.Errorf("unsupported trace exporter: %s", p.config.TraceExporter)
	}
}

// createGRPCTraceExporter creates a gRPC OTLP trace exporter
func (p *Provider) createGRPCTraceExporter(ctx context.Context) (sdktrace.SpanExporter, error) {
	opts := []otlptracegrpc.Option{
		otlptracegrpc.WithEndpoint(p.config.OTLPTraceEndpoint),
	}

	if p.config.OTLPTraceInsecure {
		opts = append(opts, otlptracegrpc.WithInsecure())
	}

	if len(p.config.OTLPTraceHeaders) > 0 {
		opts = append(opts, otlptracegrpc.WithHeaders(p.config.OTLPTraceHeaders))
	}

	return otlptracegrpc.New(ctx, opts...)
}

// createHTTPTraceExporter creates an HTTP OTLP trace exporter
func (p *Provider) createHTTPTraceExporter(ctx context.Context) (sdktrace.SpanExporter, error) {
	opts := []otlptracehttp.Option{
		otlptracehttp.WithEndpoint(p.config.OTLPTraceEndpoint),
	}

	if p.config.OTLPTraceURLPath != "" {
		opts = append(opts, otlptracehttp.WithURLPath(p.config.OTLPTraceURLPath))
	}

	if p.config.OTLPTraceInsecure {
		opts = append(opts, otlptracehttp.WithInsecure())
	}

	if len(p.config.OTLPTraceHeaders) > 0 {
		opts = append(opts, otlptracehttp.WithHeaders(p.config.OTLPTraceHeaders))
	}

	return otlptracehttp.New(ctx, opts...)
}

// initMetrics initializes the meter provider with OTLP exporter
func (p *Provider) initMetrics(ctx context.Context, res *resource.Resource) error {
	// Create OTLP metrics exporter
	exporter, err := p.createMetricsExporter(ctx)
	if err != nil {
		return fmt.Errorf("failed to create metrics exporter: %w", err)
	}

	// Create meter provider
	p.meterProvider = sdkmetric.NewMeterProvider(
		sdkmetric.WithReader(sdkmetric.NewPeriodicReader(exporter,
			sdkmetric.WithInterval(p.config.MetricsInterval))),
		sdkmetric.WithResource(res),
	)

	// Set global meter provider
	otel.SetMeterProvider(p.meterProvider)

	// Create meter for this service
	p.meter = p.meterProvider.Meter(
		p.config.ServiceName,
		metric.WithInstrumentationVersion(p.config.ServiceVersion),
		metric.WithSchemaURL(semconv.SchemaURL),
	)

	return nil
}

// createMetricsExporter creates a metrics exporter based on the configured type.
func (p *Provider) createMetricsExporter(ctx context.Context) (sdkmetric.Exporter, error) {
	switch p.config.MetricsExporter {
	case "otlp":
		// For OTLP, we further select based on the protocol (gRPC or HTTP)
		switch p.config.OTLPMetricsProtocol {
		case "grpc":
			return p.createGRPCMetricsExporter(ctx)
		case "http":
			return p.createHTTPMetricsExporter(ctx)
		default:
			return nil, fmt.Errorf("unsupported OTLP metrics protocol: %s", p.config.OTLPMetricsProtocol)
		}
	case "file":
		return file.NewFileExporter(p.config.MetricsFilePath)
	default:
		return nil, fmt.Errorf("unsupported metrics exporter: %s", p.config.MetricsExporter)
	}
}

// createGRPCMetricsExporter creates a gRPC OTLP metrics exporter
func (p *Provider) createGRPCMetricsExporter(ctx context.Context) (sdkmetric.Exporter, error) {
	opts := []otlpmetricgrpc.Option{
		otlpmetricgrpc.WithEndpoint(p.config.OTLPMetricsEndpoint),
	}

	if p.config.OTLPMetricsInsecure {
		opts = append(opts, otlpmetricgrpc.WithInsecure())
	}

	if len(p.config.OTLPMetricsHeaders) > 0 {
		opts = append(opts, otlpmetricgrpc.WithHeaders(p.config.OTLPMetricsHeaders))
	}

	return otlpmetricgrpc.New(ctx, opts...)
}

// createHTTPMetricsExporter creates an HTTP OTLP metrics exporter
func (p *Provider) createHTTPMetricsExporter(ctx context.Context) (sdkmetric.Exporter, error) {
	opts := []otlpmetrichttp.Option{
		otlpmetrichttp.WithEndpoint(p.config.OTLPMetricsEndpoint),
	}

	if p.config.OTLPMetricsURLPath != "" {
		opts = append(opts, otlpmetrichttp.WithURLPath(p.config.OTLPMetricsURLPath))
	}

	if p.config.OTLPMetricsInsecure {
		opts = append(opts, otlpmetrichttp.WithInsecure())
	}

	if len(p.config.OTLPMetricsHeaders) > 0 {
		opts = append(opts, otlpmetrichttp.WithHeaders(p.config.OTLPMetricsHeaders))
	}

	return otlpmetrichttp.New(ctx, opts...)
}

// initLogging initializes the logging provider with OTLP exporter
func (p *Provider) initLogging(ctx context.Context, res *resource.Resource) error {
	// Create logging configuration from observability config
	loggingConfig := LoggingConfig{
		Enabled:      p.config.LoggingEnabled,
		Exporter:     p.config.LogExporter,
		FilePath:     p.config.LogFilePath,
		OTLPEndpoint: p.config.OTLPLogEndpoint,
		OTLPProtocol: p.config.OTLPLogProtocol,
		OTLPURLPath:  p.config.OTLPLogURLPath,
		OTLPHeaders:  p.config.OTLPLogHeaders,
		OTLPInsecure: p.config.OTLPLogInsecure,
		BatchProcessor: BatchConfig{
			MaxQueueSize:       1000,
			ExportTimeout:      5000, // 5 seconds
			ExportInterval:     1000, // 1 second
			MaxExportBatchSize: 100,
		},
	}

	// Create logging provider
	loggingProvider, err := NewLoggingProvider(ctx, loggingConfig, res)
	if err != nil {
		return fmt.Errorf("failed to create logging provider: %w", err)
	}

	p.loggingProvider = loggingProvider
	return nil
}

// Tracer returns the tracer instance, or a no-op tracer if tracing is disabled
func (p *Provider) Tracer() trace.Tracer {
	if p.tracer != nil {
		return p.tracer
	}
	return noopTrace.NewTracerProvider().Tracer("")
}

// Meter returns the meter instance, or a no-op meter if metrics are disabled
func (p *Provider) Meter() metric.Meter {
	if p.meter != nil {
		return p.meter
	}
	return noopMetrics.NewMeterProvider().Meter("")
}

// Shutdown gracefully shuts down the providers
func (p *Provider) Shutdown(ctx context.Context) error {
	var errs []error

	if p.tracerProvider != nil {
		if err := p.tracerProvider.Shutdown(ctx); err != nil {
			errs = append(errs, fmt.Errorf("failed to shutdown tracer provider: %w", err))
		}
		p.tracerProvider = nil // Mark as shut down
	}

	if p.meterProvider != nil {
		if err := p.meterProvider.Shutdown(ctx); err != nil {
			errs = append(errs, fmt.Errorf("failed to shutdown meter provider: %w", err))
		}
		p.meterProvider = nil // Mark as shut down
	}

	if p.loggingProvider != nil {
		if err := p.loggingProvider.Shutdown(ctx); err != nil {
			errs = append(errs, fmt.Errorf("failed to shutdown logging provider: %w", err))
		}
		p.loggingProvider = nil // Mark as shut down
	}

	if len(errs) > 0 {
		return fmt.Errorf("shutdown errors: %v", errs)
	}

	return nil
}
