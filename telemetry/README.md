# Telemetry System

The telemetry system provides comprehensive observability for the application through tracing, metrics, and structured logging using OpenTelemetry.

## Overview

The telemetry package implements a complete observability stack with support for:
- Distributed tracing
- Application metrics
- Structured logging
- Multiple exporters (OTLP, file, console)

## Integration Points

### 1. Server Initialization
The telemetry system is initialized in the CLI serve command (`internal/cli/run.go`).
Look for the `initializeTelemetry` function call and `telemetry.SetGlobal` in `internal/cli/run.go` (around line 287):

```go
tel, err := telemetry.Initialize(ctx, telemetry.Config{
	Observability: cfg.Observability,
	Logger:        appLogger,
})
if err != nil {
	return nil, fmt.Errorf("failed to initialize telemetry: %w", err)
}

telemetry.SetGlobal(tel)
```

### 2. HTTP Middleware
Observability is automatically added to all HTTP routes via middleware in `internal/server/routes.go`.
Check the `RegisterRoutes` function in `internal/server/routes.go` (around line 11):

```go
router.Use(telemetry.HTTPMiddleware())
router.Use(telemetry.RecoveryMiddleware())
```

This instruments all HTTP requests with:
- Tracing spans with detailed attributes
- Metrics for request count, duration, and size
- Structured logging with appropriate log levels

### 3. AI Client Instrumentation
All AI clients are automatically wrapped with observability in `ai/client.go`.
See the `NewClient` function in `ai/client.go` (around line 102) where `NewInstrumentedClient` is called:

```go
instrumentedClient := NewInstrumentedClient(concreteClient, operationName, cfg.Provider)
```

This provides:
- Tracing for AI operations
- Metrics for AI request count, duration, and token usage
- Structured logging for AI operations

## Key Components

### Telemetry Manager (`telemetry.go`)
- Main entry point for the observability stack.
- Look for the `Telemetry` struct and `Initialize` function.
- Manages the OpenTelemetry provider.
- Provides global access to tracer, meter, and metrics via `Tracer()`, `Meter()`, `Metrics()`, and `Logger()` methods.

### Provider (`provider.go`)
- Initializes OpenTelemetry providers and exporters.
- Look for the `Provider` struct and `NewProvider` function.
- Supports OTLP (gRPC/HTTP), file, and console exporters.
- Configures tracing, metrics, and logging.

### Metrics (`metrics.go`)
- Defines all application metrics.
- Check the `Metrics` struct and `NewMetrics` function.
- Provides helper methods to record metrics (e.g., `RecordHTTPRequest`, `RecordAIRequest`).
- Metrics include HTTP, AI operations, circuit breaker, and application metrics.

### Middleware (`middleware.go`)
- Gin middleware for HTTP observability.
- See `HTTPMiddleware()` and `RecoveryMiddleware()` functions.
- Automatic tracing, metrics, and logging for HTTP requests.
- Panic recovery with observability.

### Logging Provider (`logging.go`)
- OpenTelemetry logging infrastructure.
- Look for the `LoggingProvider` struct and `NewLoggingProvider` function.
- Supports OTLP, file, and console exporters.
- Structured logging with trace context.

## Exporters

The implementation for various exporters can be found in `telemetry/provider.go` (for traces and metrics) and `telemetry/logging.go` (for logs).

### OTLP Exporters
Support for both gRPC and HTTP protocols.
- Configurable endpoints, headers, and security settings.
- Batch processing for efficient data transmission.
- See `createTraceExporter`, `createMetricsExporter` in `telemetry/provider.go` and `createLogExporter` in `telemetry/logging.go`.

### File Exporters
Local file output for development and debugging.
- JSON formatted output for easy inspection.
- Separate files for traces and metrics.
- See `file.NewFileExporter` usage in `telemetry/provider.go` and `createFileLogExporter` in `telemetry/logging.go`.

### Console Exporter
Direct console output for development.
- See `createConsoleLogExporter` in `telemetry/logging.go`.

## Configuration

Telemetry is configured through the `ObservabilityConfig` struct, defined in `config/config.go`.
This struct contains options for:
- Service identification (name, version, environment)
- Enabling/disabling tracing, metrics, logging
- Exporter selection and configuration
- OTLP endpoint settings

## Dynamic Reloading

The telemetry system supports configuration reloading without server restart.
- It detects changes to observability configuration and gracefully shuts down the existing telemetry stack, then initializes a new one with updated configuration.
- This logic is primarily handled in `internal/cli/run.go` within the `registerConfigChangeCallback` function, which calls `handleObservabilityConfigChange`.
- The `telemetry.Telemetry` struct's `Shutdown` and `Initialize` methods (in `telemetry/telemetry.go`) are used for this process.

## Metrics Collected

All defined metrics can be found in the `Metrics` struct within `telemetry/metrics.go`.

### HTTP Metrics
- `http_requests_total` - Total number of HTTP requests
- `http_request_duration_seconds` - HTTP request duration
- `http_request_size_bytes` - HTTP request size
- `http_response_size_bytes` - HTTP response size

### AI Operation Metrics
- `ai_requests_total` - Total number of AI requests
- `ai_request_duration_seconds` - AI request duration
- `ai_tokens_used_total` - Total number of AI tokens used
- `ai_provider_errors_total` - Total number of AI provider errors

### Circuit Breaker Metrics
- `circuit_breaker_state` - Circuit breaker state
- `circuit_breaker_requests_total` - Total number of circuit breaker requests

### Application Metrics
- `config_reloads_total` - Total number of configuration reloads
- `active_connections` - Number of active connections