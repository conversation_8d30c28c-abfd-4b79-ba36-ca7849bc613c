package telemetry

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"sync"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"

	"go.opentelemetry.io/otel/metric"
	noopMetrics "go.opentelemetry.io/otel/metric/noop"
	"go.opentelemetry.io/otel/trace"
	noopTrace "go.opentelemetry.io/otel/trace/noop"
)

// Telemetry manages the observability stack for the application
type Telemetry struct {
	provider *Provider
	logger   logger.Logger
	mu       sync.RWMutex
	shutdown bool
}

// Config holds telemetry configuration
type Config struct {
	Observability config.ObservabilityConfig
	Logger        logger.Logger
}

// Initialize sets up the complete observability stack
func Initialize(ctx context.Context, cfg Config) (*Telemetry, error) {
	// Use provided logger or create a default one
	log := cfg.Logger
	if log == nil {
		log = logger.New(&logger.Config{
			Level:  logger.LevelInfo,
			Format: logger.FormatJSON,
			Output: os.Stderr,
		}).Named("telemetry")
	}

	log.Info(ctx, "Initializing observability stack",
		logger.String("service", cfg.Observability.ServiceName),
		logger.String("version", cfg.Observability.ServiceVersion),
		logger.String("environment", cfg.Observability.Environment),
		logger.Any("tracing_enabled", cfg.Observability.TracingEnabled),
		logger.Any("metrics_enabled", cfg.Observability.MetricsEnabled),
	)

	// Create OpenTelemetry provider
	provider, err := NewProvider(ctx, cfg.Observability)
	if err != nil {
		return nil, fmt.Errorf("failed to create telemetry provider: %w", err)
	}

	telemetry := &Telemetry{
		provider: provider,
		logger:   log,
	}

	log.Info(ctx, "Observability stack initialized successfully")

	return telemetry, nil
}

// Tracer returns the global tracer instance
func (t *Telemetry) Tracer() trace.Tracer {
	t.mu.RLock()
	defer t.mu.RUnlock()

	if t.provider == nil {
		return noopTrace.NewTracerProvider().Tracer("")
	}
	return t.provider.Tracer()
}

// Meter returns the global meter instance
func (t *Telemetry) Meter() metric.Meter {
	t.mu.RLock()
	defer t.mu.RUnlock()

	if t.provider == nil {
		return noopMetrics.NewMeterProvider().Meter("")
	}
	return t.provider.Meter()
}

// Metrics returns the metrics collection instance, creating it lazily if needed
func (t *Telemetry) Metrics() *Metrics {
	t.mu.RLock()
	defer t.mu.RUnlock()

	if t.provider == nil {
		return nil
	}

	meter := t.provider.Meter()
	if meter == nil {
		return nil
	}

	// Check if this is a no-op meter (when metrics are disabled)
	// No-op meters don't support creating real metrics, so return nil
	if isNoOpMeter(meter) {
		return nil
	}

	// Create metrics on-demand - this is safe because NewMetrics is deterministic
	// and creates the same instruments each time
	metrics, err := NewMetrics(meter)
	if err != nil {
		// Log error but don't panic - return nil to indicate metrics unavailable
		t.logger.ErrorWithErr(context.Background(), "Failed to create metrics collection", err)
		return nil
	}

	return metrics
}

// Logger returns the telemetry logger
func (t *Telemetry) Logger() logger.Logger {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.logger
}

// Shutdown gracefully shuts down the telemetry stack
func (t *Telemetry) Shutdown(ctx context.Context) error {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.shutdown {
		return nil
	}

	t.logger.Info(ctx, "Shutting down observability stack")

	var err error
	if t.provider != nil {
		err = t.provider.Shutdown(ctx)
	}

	t.shutdown = true

	if err != nil {
		t.logger.ErrorWithErr(ctx, "Failed to shutdown observability stack", err)
		return err
	}

	t.logger.Info(ctx, "Observability stack shutdown completed")
	return nil
}

// IsShutdown returns true if telemetry has been shut down
func (t *Telemetry) IsShutdown() bool {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.shutdown
}

// Global telemetry instance
var (
	globalTelemetry *Telemetry
	globalMu        sync.RWMutex
)

// SetGlobal sets the global telemetry instance
func SetGlobal(t *Telemetry) {
	globalMu.Lock()
	defer globalMu.Unlock()
	globalTelemetry = t
}

// Global returns the global telemetry instance
func Global() *Telemetry {
	globalMu.RLock()
	defer globalMu.RUnlock()
	return globalTelemetry
}

// Tracer returns the global tracer, or a no-op tracer if not initialized
func Tracer() trace.Tracer {
	if t := Global(); t != nil {
		return t.Tracer()
	}
	return noopTrace.NewTracerProvider().Tracer("")
}

// Meter returns the global meter, or a no-op meter if not initialized
func Meter() metric.Meter {
	if t := Global(); t != nil {
		return t.Meter()
	}
	return noopMetrics.NewMeterProvider().Meter("")
}

// MetricsCollection returns the global metrics collection, or nil if not initialized or disabled
func MetricsCollection() *Metrics {
	if t := Global(); t != nil {
		return t.Metrics()
	}
	return nil
}

// Logger returns the global telemetry logger, or a default logger if not initialized
func Logger() logger.Logger {
	if t := Global(); t != nil {
		return t.Logger()
	}
	return logger.NewDefault().Named("telemetry")
}

// isNoOpMeter checks if the given meter is a no-op implementation
func isNoOpMeter(meter metric.Meter) bool {
	// Check if it's a no-op meter by examining the type
	meterType := reflect.TypeOf(meter)
	return meterType.PkgPath() == "go.opentelemetry.io/otel/metric/noop"
}
