package telemetry

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/ajiwo/resumatter/logger"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// HTTPMiddleware creates a Gin middleware for HTTP observability
func HTTPMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip telemetry for health checks and metrics endpoints
		if c.Request.URL.Path == "/health" || c.Request.URL.Path == "/metrics" {
			c.Next()
			return
		}

		start := time.Now()

		// Get telemetry instance
		tel := Global()
		if tel == nil {
			c.Next()
			return
		}

		tracer := tel.Tracer()
		metrics := tel.Metrics()
		log := tel.Logger()

		// Start HTTP span
		ctx, span := tracer.Start(c.Request.Context(), "http.request",
			trace.WithAttributes(
				attribute.String("http.method", c.Request.Method),
				attribute.String("http.url", c.Request.URL.String()),
				attribute.String("http.route", c.FullPath()),
				attribute.String("http.user_agent", c.Request.UserAgent()),
				attribute.String("http.remote_addr", c.ClientIP()),
			),
		)
		defer span.End()

		// Add span context to Gin context
		c.Request = c.Request.WithContext(ctx)

		// Add logger to context
		c.Request = c.Request.WithContext(logger.WithLogger(ctx, log))

		// Get request size
		requestSize := int64(0)
		if c.Request.ContentLength > 0 {
			requestSize = c.Request.ContentLength
		}

		// Process request
		c.Next()

		// Calculate duration
		duration := time.Since(start)

		// Get response information
		status := c.Writer.Status()
		responseSize := int64(c.Writer.Size())
		route := c.FullPath()
		if route == "" {
			route = "unknown"
		}

		// Update span with response information
		span.SetAttributes(
			attribute.Int("http.status_code", status),
			attribute.Int64("http.request_content_length", requestSize),
			attribute.Int64("http.response_content_length", responseSize),
			attribute.Float64("http.duration_ms", float64(duration.Nanoseconds())/1e6),
		)

		// Set span status based on HTTP status
		setSpanStatus(span, status)

		// Record metrics (metrics may be nil if metrics are disabled)
		if metrics != nil {
			statusStr := strconv.Itoa(status)
			metrics.RecordHTTPRequest(ctx, c.Request.Method, route, statusStr, duration, requestSize, responseSize)
		}

		// Log request
		logFields := []logger.Field{
			logger.String("method", c.Request.Method),
			logger.String("path", c.Request.URL.Path),
			logger.String("route", route),
			logger.Int("status", status),
			logger.Duration("duration", duration),
			logger.String("remote_addr", c.ClientIP()),
		}

		if requestSize > 0 {
			logFields = append(logFields, logger.Int64("request_size", requestSize))
		}
		if responseSize > 0 {
			logFields = append(logFields, logger.Int64("response_size", responseSize))
		}

		// Add error information if present
		if len(c.Errors) > 0 {
			logFields = append(logFields, logger.String("errors", c.Errors.String()))
		}

		// Log at appropriate level
		logHTTPStatus(ctx, log, logFields, status)
	}
}

// setSpanStatus sets the span status based on the HTTP status code
func setSpanStatus(span trace.Span, status int) {
	if status >= 400 {
		span.SetStatus(codes.Error, "HTTP error")
		if status >= 500 {
			span.RecordError(fmt.Errorf("HTTP %d error", status))
		}
	} else {
		span.SetStatus(codes.Ok, "")
	}

}

// logHTTPStatus logs the HTTP request at the appropriate level
func logHTTPStatus(ctx context.Context, log logger.Logger, logFields []logger.Field, status int) {
	switch {
	case status >= 500:
		log.Error(ctx, "HTTP request completed with server error", logFields...)
	case status >= 400:
		log.Warn(ctx, "HTTP request completed with client error", logFields...)
	default:
		log.Info(ctx, "HTTP request completed", logFields...)
	}
}

// RecoveryMiddleware creates a Gin middleware for panic recovery with observability
func RecoveryMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered any) {
		tel := Global()
		if tel == nil {
			c.AbortWithStatus(500)
			return
		}

		log := tel.Logger()

		// Get span from context if available
		span := trace.SpanFromContext(c.Request.Context())
		if span.IsRecording() {
			span.RecordError(fmt.Errorf("panic: %v", recovered))
			span.SetStatus(codes.Error, "panic recovered")
		}

		// Log the panic
		log.Error(c.Request.Context(), "Panic recovered in HTTP handler",
			logger.String("method", c.Request.Method),
			logger.String("path", c.Request.URL.Path),
			logger.String("remote_addr", c.ClientIP()),
			logger.Any("panic", recovered),
		)

		c.AbortWithStatus(500)
	})
}
