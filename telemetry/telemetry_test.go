package telemetry

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.opentelemetry.io/otel/attribute"
)

func shutdownTelemetry(ctx context.Context, t *testing.T, tel *Telemetry) {
	err := tel.Shutdown(ctx)
	require.Nil(t, err)
}

func TestTelemetryInitialize(t *testing.T) {
	ctx := context.Background()

	t.Run("initialize telemetry with empty config", func(t *testing.T) {
		cfg := Config{}
		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, tel)

		// Should not panic and return no-op implementations
		tracer := tel.Tracer()
		assert.NotNil(t, tracer)

		meter := tel.Meter()
		assert.NotNil(t, meter)

		// Metrics should be nil when disabled (no-op meter doesn't create real metrics)
		metrics := tel.Metrics()
		assert.Nil(t, metrics)

		// Should be able to shutdown
		err = tel.Shutdown(ctx)
		assert.NoError(t, err)
	})

	t.Run("initialize with custom logger", func(t *testing.T) {
		customLogger := logger.New(&logger.Config{
			Level:  logger.LevelDebug,
			Format: logger.FormatText,
			Output: os.Stderr,
		}).Named("custom-telemetry")

		cfg := Config{
			Logger: customLogger,
			Observability: config.ObservabilityConfig{
				ServiceName: "test-service",
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, tel)

		// Should use the custom logger
		assert.Equal(t, customLogger, tel.Logger())

		err = tel.Shutdown(ctx)
		assert.NoError(t, err)
	})

	t.Run("initialize with only tracing enabled", func(t *testing.T) {
		cfg := Config{
			Observability: config.ObservabilityConfig{
				ServiceName:    "test-service",
				ServiceVersion: "1.0.0",
				Environment:    "test",
				TracingEnabled: true,
				TraceExporter:  "file",
				TraceFilePath:  "/tmp/test-traces.json",
				MetricsEnabled: false,
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, tel)
		defer shutdownTelemetry(ctx, t, tel)

		// Tracer should work
		tracer := tel.Tracer()
		assert.NotNil(t, tracer)

		// Should be able to create spans
		_, span := tracer.Start(ctx, "test-span")
		span.SetAttributes(attribute.String("test", "value"))
		span.End()

		// Meter should be no-op but functional
		meter := tel.Meter()
		assert.NotNil(t, meter)

		// Metrics should be nil
		metrics := tel.Metrics()
		assert.Nil(t, metrics)
	})

	t.Run("initialize with only metrics enabled", func(t *testing.T) {
		cfg := Config{
			Observability: config.ObservabilityConfig{
				ServiceName:     "test-service",
				ServiceVersion:  "1.0.0",
				Environment:     "test",
				TracingEnabled:  false,
				MetricsEnabled:  true,
				MetricsExporter: "file",
				MetricsFilePath: "/tmp/test-metrics.json",
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, tel)
		defer shutdownTelemetry(ctx, t, tel)

		// Tracer should be no-op but functional
		tracer := tel.Tracer()
		assert.NotNil(t, tracer)

		// Meter should work
		meter := tel.Meter()
		assert.NotNil(t, meter)

		// Metrics should be available
		metrics := tel.Metrics()
		assert.NotNil(t, metrics)

		// Should be able to record metrics
		metrics.RecordHTTPRequest(ctx, "GET", "/test", "200", 100*time.Millisecond, 1024, 2048)
		metrics.RecordAIRequest(ctx, "analyze", "openai", "success", 500*time.Millisecond)
		metrics.RecordAITokens(ctx, "analyze", "openai", "total", 150)
	})

	t.Run("initialize with both tracing and metrics enabled", func(t *testing.T) {
		cfg := Config{
			Observability: config.ObservabilityConfig{
				ServiceName:     "test-service",
				ServiceVersion:  "1.0.0",
				Environment:     "test",
				TracingEnabled:  true,
				TraceExporter:   "file",
				TraceFilePath:   "/tmp/test-traces.json",
				MetricsEnabled:  true,
				MetricsExporter: "file",
				MetricsFilePath: "/tmp/test-metrics.json",
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, tel)
		defer shutdownTelemetry(ctx, t, tel)

		// Both should be functional
		tracer := tel.Tracer()
		assert.NotNil(t, tracer)

		meter := tel.Meter()
		assert.NotNil(t, meter)

		metrics := tel.Metrics()
		assert.NotNil(t, metrics)

		// Should be able to use both
		_, span := tracer.Start(ctx, "test-operation")
		span.SetAttributes(attribute.String("operation", "test"))

		metrics.RecordHTTPRequest(ctx, "POST", "/api/test", "201", 200*time.Millisecond, 512, 1024)

		span.End()
	})
}

func TestTelemetryGlobalFunctions(t *testing.T) {
	ctx := context.Background()

	// Clear any existing global telemetry
	SetGlobal(nil)

	t.Run("global functions with no telemetry set", func(t *testing.T) {
		// Should return no-op implementations, not panic
		tracer := Tracer()
		assert.NotNil(t, tracer)

		meter := Meter()
		assert.NotNil(t, meter)

		metrics := MetricsCollection()
		assert.Nil(t, metrics)

		logger := Logger()
		assert.NotNil(t, logger)
	})

	t.Run("global functions with telemetry set", func(t *testing.T) {
		cfg := Config{
			Observability: config.ObservabilityConfig{
				ServiceName:     "global-test",
				TracingEnabled:  true,
				TraceExporter:   "file",
				TraceFilePath:   "/tmp/global-traces.json",
				MetricsEnabled:  true,
				MetricsExporter: "file",
				MetricsFilePath: "/tmp/global-metrics.json",
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		defer shutdownTelemetry(ctx, t, tel)

		SetGlobal(tel)

		// Should return real implementations
		tracer := Tracer()
		assert.NotNil(t, tracer)

		meter := Meter()
		assert.NotNil(t, meter)

		metrics := MetricsCollection()
		assert.NotNil(t, metrics)

		logger := Logger()
		assert.NotNil(t, logger)

		// Should be able to use them
		_, span := tracer.Start(ctx, "global-test-span")
		span.End()

		metrics.RecordConfigReload(ctx, true)
	})
}

func TestTelemetryShutdown(t *testing.T) {
	ctx := context.Background()

	t.Run("shutdown with empty config", func(t *testing.T) {
		cfg := Config{}
		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)

		// Should shutdown without error
		err = tel.Shutdown(ctx)
		assert.NoError(t, err)

		// Should be marked as shutdown
		assert.True(t, tel.IsShutdown())

		// Multiple shutdowns should be safe
		err = tel.Shutdown(ctx)
		assert.NoError(t, err)
	})

	t.Run("shutdown with full config", func(t *testing.T) {
		cfg := Config{
			Observability: config.ObservabilityConfig{
				ServiceName:     "shutdown-test",
				TracingEnabled:  true,
				TraceExporter:   "file",
				TraceFilePath:   "/tmp/shutdown-traces.json",
				MetricsEnabled:  true,
				MetricsExporter: "file",
				MetricsFilePath: "/tmp/shutdown-metrics.json",
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)

		// Use telemetry before shutdown
		tracer := tel.Tracer()
		_, span := tracer.Start(ctx, "pre-shutdown-span")
		span.End()

		metrics := tel.Metrics()
		metrics.RecordHTTPRequest(ctx, "GET", "/health", "200", 10*time.Millisecond, 0, 100)

		// Should shutdown cleanly
		err = tel.Shutdown(ctx)
		assert.NoError(t, err)
		assert.True(t, tel.IsShutdown())
	})
}

func TestTelemetryLazyMetrics(t *testing.T) {
	ctx := context.Background()

	t.Run("metrics created lazily when enabled", func(t *testing.T) {
		cfg := Config{
			Observability: config.ObservabilityConfig{
				ServiceName:     "lazy-test",
				MetricsEnabled:  true,
				MetricsExporter: "file",
				MetricsFilePath: "/tmp/lazy-metrics.json",
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		defer shutdownTelemetry(ctx, t, tel)

		// First call should create metrics
		metrics1 := tel.Metrics()
		assert.NotNil(t, metrics1)

		// Second call should also work (creates new instance each time)
		metrics2 := tel.Metrics()
		assert.NotNil(t, metrics2)

		// Both should be functional
		metrics1.RecordHTTPRequest(ctx, "GET", "/test1", "200", 100*time.Millisecond, 0, 0)
		metrics2.RecordHTTPRequest(ctx, "GET", "/test2", "200", 100*time.Millisecond, 0, 0)
	})

	t.Run("metrics returns nil when disabled", func(t *testing.T) {
		cfg := Config{
			Observability: config.ObservabilityConfig{
				ServiceName:    "disabled-metrics-test",
				TracingEnabled: true,
				TraceExporter:  "file",
				TraceFilePath:  "/tmp/disabled-metrics-traces.json",
				MetricsEnabled: false,
			},
		}

		tel, err := Initialize(ctx, cfg)
		require.NoError(t, err)
		defer shutdownTelemetry(ctx, t, tel)

		// Should return nil when metrics disabled
		metrics := tel.Metrics()
		assert.Nil(t, metrics)

		// Multiple calls should consistently return nil
		metrics2 := tel.Metrics()
		assert.Nil(t, metrics2)
	})
}
