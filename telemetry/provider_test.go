package telemetry_test

import (
	"context"
	"strings"
	"testing"

	"github.com/ajiwo/resumatter/config"
	"github.com/ajiwo/resumatter/telemetry"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

// isConnectionError checks if an error is related to network connectivity issues
// that are expected when OTLP collector is not running in test environments
func isConnectionError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "connection error") ||
		strings.Contains(errStr, "dial tcp") ||
		strings.Contains(errStr, "exporter export timeout") ||
		strings.Contains(errStr, "transport: Error while dialing")
}

func shutdownProvider(ctx context.Context, t *testing.T, provider *telemetry.Provider) {
	err := provider.Shutdown(ctx)
	if err != nil && !isConnectionError(err) {
		require.NoError(t, err)
	}
}

// TestNewProvider tests the creation of a telemetry provider with various configurations
func TestNewProvider(t *testing.T) {
	ctx := context.Background()

	t.Run("minimal config with tracing and metrics disabled", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:    "test-service",
			ServiceVersion: "1.0.0",
			Environment:    "test",
			TracingEnabled: false,
			MetricsEnabled: false,
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)

		// When disabled, tracer and meter should return no-op implementations
		tracer := provider.Tracer()
		assert.NotNil(t, tracer, "Should return no-op tracer, not nil")

		meter := provider.Meter()
		assert.NotNil(t, meter, "Should return no-op meter, not nil")

		// No-op implementations should be safe to use
		_, span := tracer.Start(ctx, "test-span")
		span.SetAttributes(attribute.String("test", "value"))
		span.End()

		counter, err := meter.Int64Counter("test_counter")
		require.NoError(t, err)
		counter.Add(ctx, 1)
	})

	t.Run("empty service name should use default resource", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			// ServiceName is empty
			ServiceVersion: "1.0.0",
			Environment:    "test",
			TracingEnabled: false,
			MetricsEnabled: false,
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)

		// Should still work with empty service name
		tracer := provider.Tracer()
		assert.NotNil(t, tracer)

		meter := provider.Meter()
		assert.NotNil(t, meter)
	})

	t.Run("with tracing enabled", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:    "test-service",
			ServiceVersion: "1.0.0",
			Environment:    "test",
			TracingEnabled: true,
			MetricsEnabled: false,
			TraceExporter:  "file",
			TraceFilePath:  "/tmp/test-traces.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		defer shutdownProvider(ctx, t, provider)
		require.NotNil(t, provider)

		tracer := provider.Tracer()
		assert.NotNil(t, tracer)

		// Should be able to create and use spans
		_, span := tracer.Start(ctx, "test-operation")
		span.SetAttributes(
			attribute.String("operation.type", "test"),
			attribute.Int("operation.id", 123),
		)
		span.End()

		// Meter should still be no-op but functional
		meter := provider.Meter()
		assert.NotNil(t, meter)
	})

	t.Run("with metrics enabled", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:     "test-service",
			ServiceVersion:  "1.0.0",
			Environment:     "test",
			TracingEnabled:  false,
			MetricsEnabled:  true,
			MetricsExporter: "file",
			MetricsFilePath: "/tmp/test-metrics.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		defer shutdownProvider(ctx, t, provider)
		require.NotNil(t, provider)

		meter := provider.Meter()
		assert.NotNil(t, meter)

		// Should be able to create and use metrics
		counter, err := meter.Int64Counter("test_requests_total")
		require.NoError(t, err)
		counter.Add(ctx, 1, metric.WithAttributes(attribute.String("method", "GET")))

		histogram, err := meter.Float64Histogram("test_duration_seconds")
		require.NoError(t, err)
		histogram.Record(ctx, 0.123, metric.WithAttributes(attribute.String("endpoint", "/api/test")))

		// Tracer should still be no-op but functional
		tracer := provider.Tracer()
		assert.NotNil(t, tracer)
	})

	t.Run("with both tracing and metrics enabled", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:     "test-service",
			ServiceVersion:  "1.0.0",
			Environment:     "test",
			TracingEnabled:  true,
			MetricsEnabled:  true,
			TraceExporter:   "file",
			TraceFilePath:   "/tmp/test-traces.json",
			MetricsExporter: "file",
			MetricsFilePath: "/tmp/test-metrics.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		defer shutdownProvider(ctx, t, provider)
		require.NotNil(t, provider)

		tracer := provider.Tracer()
		assert.NotNil(t, tracer)

		meter := provider.Meter()
		assert.NotNil(t, meter)

		// Should be able to use both together
		counter, err := meter.Int64Counter("operations_total")
		require.NoError(t, err)

		_, span := tracer.Start(ctx, "combined-operation")
		span.SetAttributes(attribute.String("operation", "test"))

		counter.Add(ctx, 1, metric.WithAttributes(attribute.String("operation", "test")))

		span.End()
	})

	t.Run("with invalid trace exporter", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:    "test-service",
			TracingEnabled: true,
			TraceExporter:  "invalid",
		}
		provider, err := telemetry.NewProvider(ctx, cfg)
		assert.Nil(t, provider)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported trace exporter")
	})

	t.Run("with invalid metrics exporter", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:     "test-service",
			MetricsEnabled:  true,
			MetricsExporter: "invalid",
		}
		provider, err := telemetry.NewProvider(ctx, cfg)
		assert.Nil(t, provider)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported metrics exporter")
	})
}

func TestProviderOTLPConfiguration(t *testing.T) {
	ctx := context.Background()

	t.Run("OTLP trace configuration with gRPC", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:       "otlp-test",
			TracingEnabled:    true,
			TraceExporter:     "otlp",
			OTLPTraceProtocol: "grpc",
			OTLPTraceEndpoint: "localhost:4317",
			OTLPTraceInsecure: true,
			OTLPTraceHeaders:  map[string]string{"x-api-key": "test-key"},
		}

		// This will fail to connect but should create the provider successfully
		provider, err := telemetry.NewProvider(ctx, cfg)
		if err != nil {
			// Connection errors are expected in tests, but configuration errors are not
			assert.NotContains(t, err.Error(), "unsupported")
			assert.NotContains(t, err.Error(), "invalid")
		} else {
			require.NotNil(t, provider)
			defer shutdownProvider(ctx, t, provider)

			tracer := provider.Tracer()
			assert.NotNil(t, tracer)
		}
	})

	t.Run("OTLP trace configuration with HTTP", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:       "otlp-test",
			TracingEnabled:    true,
			TraceExporter:     "otlp",
			OTLPTraceProtocol: "http",
			OTLPTraceEndpoint: "localhost:4318",
			OTLPTraceURLPath:  "/v1/traces",
			OTLPTraceInsecure: true,
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		if err != nil {
			assert.NotContains(t, err.Error(), "unsupported")
			assert.NotContains(t, err.Error(), "invalid")
		} else {
			require.NotNil(t, provider)
			defer shutdownProvider(ctx, t, provider)
		}
	})

	t.Run("OTLP metrics configuration with gRPC", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:         "otlp-test",
			MetricsEnabled:      true,
			MetricsExporter:     "otlp",
			OTLPMetricsProtocol: "grpc",
			OTLPMetricsEndpoint: "localhost:4317",
			OTLPMetricsInsecure: true,
			OTLPMetricsHeaders:  map[string]string{"authorization": "Bearer token"},
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		if err != nil {
			assert.NotContains(t, err.Error(), "unsupported")
			assert.NotContains(t, err.Error(), "invalid")
		} else {
			require.NotNil(t, provider)
			defer shutdownProvider(ctx, t, provider)

			meter := provider.Meter()
			assert.NotNil(t, meter)
		}
	})

	t.Run("OTLP metrics configuration with HTTP", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:         "otlp-test",
			MetricsEnabled:      true,
			MetricsExporter:     "otlp",
			OTLPMetricsProtocol: "http",
			OTLPMetricsEndpoint: "localhost:4318",
			OTLPMetricsURLPath:  "/v1/metrics",
			OTLPMetricsInsecure: true,
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		if err != nil {
			assert.NotContains(t, err.Error(), "unsupported")
			assert.NotContains(t, err.Error(), "invalid")
		} else {
			require.NotNil(t, provider)
			defer shutdownProvider(ctx, t, provider)
		}
	})

	t.Run("invalid OTLP protocol", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:       "otlp-test",
			TracingEnabled:    true,
			TraceExporter:     "otlp",
			OTLPTraceProtocol: "invalid",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		assert.Nil(t, provider)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported OTLP trace protocol")
	})
}

func TestProviderFileExporter(t *testing.T) {
	ctx := context.Background()

	t.Run("file trace exporter", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:    "file-test",
			TracingEnabled: true,
			TraceExporter:  "file",
			TraceFilePath:  "/tmp/test-traces-provider.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		tracer := provider.Tracer()
		assert.NotNil(t, tracer)

		// Create a span to test the file exporter
		_, span := tracer.Start(ctx, "file-test-span")
		span.SetAttributes(attribute.String("test.type", "file-export"))
		span.End()
	})

	t.Run("file metrics exporter", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:     "file-test",
			MetricsEnabled:  true,
			MetricsExporter: "file",
			MetricsFilePath: "/tmp/test-metrics-provider.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		meter := provider.Meter()
		assert.NotNil(t, meter)

		// Create metrics to test the file exporter
		counter, err := meter.Int64Counter("file_test_counter")
		require.NoError(t, err)
		counter.Add(ctx, 5, metric.WithAttributes(attribute.String("test.type", "file-export")))
	})
}

func TestProviderShutdown(t *testing.T) {
	ctx := context.Background()

	t.Run("shutdown with no providers initialized", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:    "shutdown-test",
			TracingEnabled: false,
			MetricsEnabled: false,
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		// Should shutdown cleanly even with no providers
	})

	t.Run("shutdown with tracing only", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:    "shutdown-test",
			TracingEnabled: true,
			TraceExporter:  "file",
			TraceFilePath:  "/tmp/shutdown-traces.json",
			MetricsEnabled: false,
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		// Use the tracer before shutdown
		tracer := provider.Tracer()
		_, span := tracer.Start(ctx, "pre-shutdown-span")
		span.End()
	})

	t.Run("shutdown with metrics only", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:     "shutdown-test",
			TracingEnabled:  false,
			MetricsEnabled:  true,
			MetricsExporter: "file",
			MetricsFilePath: "/tmp/shutdown-metrics.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		// Use the meter before shutdown
		meter := provider.Meter()
		counter, err := meter.Int64Counter("pre_shutdown_counter")
		require.NoError(t, err)
		counter.Add(ctx, 1)
	})

	t.Run("shutdown with both providers", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:     "shutdown-test",
			TracingEnabled:  true,
			TraceExporter:   "file",
			TraceFilePath:   "/tmp/shutdown-both-traces.json",
			MetricsEnabled:  true,
			MetricsExporter: "file",
			MetricsFilePath: "/tmp/shutdown-both-metrics.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		// Use both before shutdown
		tracer := provider.Tracer()
		_, span := tracer.Start(ctx, "pre-shutdown-span")

		meter := provider.Meter()
		counter, err := meter.Int64Counter("pre_shutdown_counter")
		require.NoError(t, err)
		counter.Add(ctx, 1)

		span.End()
	})

	t.Run("multiple shutdowns should be safe", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:     "shutdown-test",
			TracingEnabled:  true,
			TraceExporter:   "file",
			TraceFilePath:   "/tmp/multi-shutdown-traces.json",
			MetricsEnabled:  true,
			MetricsExporter: "file",
			MetricsFilePath: "/tmp/multi-shutdown-metrics.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider) // Ensure it's shut down once at the end of the test

		// Multiple calls to provider.Shutdown should be safe
		err = provider.Shutdown(ctx)
		assert.NoError(t, err)

		err = provider.Shutdown(ctx)
		assert.NoError(t, err)

		err = provider.Shutdown(ctx)
		assert.NoError(t, err)
	})
}

func TestProviderResourceConfiguration(t *testing.T) {
	ctx := context.Background()

	t.Run("complete resource configuration", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			ServiceName:    "resource-test-service",
			ServiceVersion: "2.1.0",
			Environment:    "production",
			TracingEnabled: true,
			TraceExporter:  "file",
			TraceFilePath:  "/tmp/resource-traces.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		tracer := provider.Tracer()
		assert.NotNil(t, tracer)

		// Create a span to verify resource attributes are applied
		_, span := tracer.Start(ctx, "resource-test-span")
		span.SetAttributes(attribute.String("test.resource", "configured"))
		span.End()
	})

	t.Run("minimal resource configuration", func(t *testing.T) {
		cfg := config.ObservabilityConfig{
			// Only service name provided
			ServiceName:    "minimal-service",
			TracingEnabled: true,
			TraceExporter:  "file",
			TraceFilePath:  "/tmp/minimal-traces.json",
		}

		provider, err := telemetry.NewProvider(ctx, cfg)
		require.NoError(t, err)
		require.NotNil(t, provider)
		defer shutdownProvider(ctx, t, provider)

		tracer := provider.Tracer()
		assert.NotNil(t, tracer)
	})
}
