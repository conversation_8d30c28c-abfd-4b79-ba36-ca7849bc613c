# resumatter getting started configuration file
# This is a minimal configuration file to help you get started quickly.
# For a complete list of all available options, see config.example.yaml

ai:
  # Provider Presets/Profiles
  # Each preset is a complete, reusable provider configuration
  presets:
    # Default preset - configure this one for your needs
    default:
      # Choose your provider: "genai-gemini" or "openai"
      provider: "genai-gemini"
      
      # Model to use
      model: "gemini-2.0-flash"
      
      # Generation parameters
      temperature: 0.7
      max_tokens: 8192
      timeout: "30s"
      
      # Circuit breaker configuration (optional)
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"
      
      # Provider-specific configuration
      provider_config:
        # For Google Gemini, you need to specify:
        # - backend: Must be "gemini"
        # - api_key: Your Google AI Gemini API key
        backend: "gemini"
        # Use an environment variable for the API key (recommended for security)
        api_key: "${GEMINI_API_KEY}"
        # Or hardcode your API key (for quick testing only - don't commit real keys!)
        # api_key: "AIzaSyDUMMY_GEMINI_API_KEY_EXAMPLE_12345"

  # Default preset to use when operations don't specify a preset
  default_preset: "default"

  # Per-operation configurations using presets
  operations:
    # Resume tailoring operation
    tailor:
      preset: "default"
    
    # Resume evaluation operation
    evaluate:
      preset: "default"
    
    # Job analysis operation
    analyze:
      preset: "default"
    
    # Git commit message generation operation
    git-commit:
      preset: "default"

# Server Configuration
server:
  port: "8780"
  environment: "development"

# TLS Configuration (disabled by default)
tls:
  mode: "off"

# Authentication Configuration (disabled by default)
auth:
  enabled: false

# Rate Limiting Configuration (enabled by default to prevent AI cost escalation)
rate_limit:
  enabled: true
  backend: "memory"
  strategy: "token_bucket"
  key_by: "ip"
  
  defaults:
    requests_per_minute: 60
    burst_size: 10

# Logging Configuration
logging:
  level: "info"
  format: "text"

# Observability Configuration (minimal by default)
observability:
  service_name: "resumatter"
  service_version: "v1.0.0"
  environment: "development"
  
  tracing_enabled: false
  metrics_enabled: false
  logging_enabled: false

# --- Alternative Configuration Methods ---
# As an alternative to a configuration file, you can use environment variables.
# The application will automatically pick them up. Below are examples of how to set them:
#
# For Google Gemini:
# export AI_GEMINI_API_KEY="AIzaSyDUMMY_GEMINI_API_KEY_EXAMPLE_12345"
# export AI_GEMINI_MODEL="gemini-2.0-flash-lite"
#
# For OpenAI or OpenAI-Compatible APIs:
# export AI_OPENAI_API_KEY="sk-dummy-openai-api-key-example-12345"
# export AI_OPENAI_BASE_URL="https://api.openai-compatible.com/v1"
# export AI_OPENAI_MODEL="gpt-4o"